/**
 * 计算项目进度的工具函数
 */

export interface ProjectProgressResult {
  progress: number;
  statusText: string;
  isActive: boolean;
}

/**
 * 根据项目开始和结束日期计算项目进度
 * @param startDate 项目开始日期 (YYYY-MM-DD 格式)
 * @param endDate 项目结束日期 (YYYY-MM-DD 格式)
 * @returns 项目进度信息
 */
export function calculateProjectProgress(
  startDate: string,
  endDate: string
): ProjectProgressResult {
  const now = new Date();
  const start = new Date(startDate);
  const end = new Date(endDate);

  // 设置时间为当天的开始和结束，避免时区问题
  now.setHours(0, 0, 0, 0);
  start.setHours(0, 0, 0, 0);
  end.setHours(23, 59, 59, 999);

  const totalDuration = end.getTime() - start.getTime();
  const elapsed = now.getTime() - start.getTime();

  let progress = 0;
  let statusText = '';
  let isActive = false;

  if (now < start) {
    // 项目未开始
    progress = 0;
    statusText = '未开始';
    isActive = false;
  } else if (now > end) {
    // 项目已结束
    progress = 100;
    statusText = '已结束';
    isActive = false;
  } else {
    // 项目进行中
    progress = Math.round((elapsed / totalDuration) * 100);
    progress = Math.max(0, Math.min(100, progress)); // 确保在 0-100 范围内
    statusText = '进行中';
    isActive = true;
  }

  return {
    progress,
    statusText,
    isActive,
  };
}
