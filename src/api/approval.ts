import api from '@/lib/api';

// 创建审批
export const createApproval = (data: object) => {
  return api.post('/v1/affairs/approval/models', data);
};

// 更新审批
export const updateApproval = (modelId: string, data: object) => {
  return api.put(`/v1/affairs/approval/models/${modelId}`, data);
};

// 获取审批详情
export const getApproval = (modelId: string) => {
  return api.get(`/v1/affairs/approval/models/${modelId}`);
};

// 发起申请
export const submitApproval = (data: object) => {
  return api.post('/v1/affairs/approval/instances', data);
};

// 发起申请详情
export const submitDetail = (instanceId: string, data: any) => {
  return api.get(`/v1/affairs/approval/instances/${instanceId}`, {
    params: data,
  });
};

// 获取审批分组
export const getGroup = () => {
  return api.get('/v1/affairs/approval/groups');
};

// 获取审批模板详情
export const getTemplate = (templateId: string) => {
  return api.get(`/v1/affairs/approval/models/templates/${templateId}`);
};
