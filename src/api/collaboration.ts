import api from '@/lib/api';

// 机构列表
export const getAlliances = async () => {
  return api.get('/v1/collaboration/alliances');
};
export const getAllianceMembers = async (id: string) => {
  return api.get(`/v1/collaboration/alliances/${id}/members`);
};
export const getAllianceDetails = async (id: string) => {
  return api.get(`/v1/collaboration/alliances/${id}`);
};
export const updateAlliance = async (id: string, data: any) => {
  return api.put(`/v1/collaboration/alliances/${id}`, data);
};
export const removeMember = async (id: string, memberId: any) => {
  return api.delete(`/v1/collaboration/alliances/${id}/members/${memberId}`);
};
// 邀请成员
export const inviteMember = async (id: string, data: any) => {
  return api.post(`/v1/collaboration/alliances/${id}/members`, data);
};
// 同意邀请
export const acceptInvitation = async (id: string, data: any) => {
  return api.post(`/v1/collaboration/alliances/${id}/invitations:accept`, data);
};
// 拒绝邀请
export const rejectInvitation = async (id: string, data: any) => {
  return api.post(
    `/v1/collaboration/alliances/${id}/invitations:decline`,
    data
  );
};
// 搜索
export const searchAlliances = async (data: any) => {
  return api.get('/v1/contact/institutions:search', { params: data });
};
// 获取状态
export const getAllianceStatus = async (id: string) => {
  return api.get(`/v1/collaboration/alliances/${id}/invitations:checkStatus`);
};
