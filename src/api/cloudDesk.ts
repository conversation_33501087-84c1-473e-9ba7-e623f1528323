import api from '@/lib/api';

// 文件信息
export const getSourceDetail = async (id: string) => {
  return await api.get(`/app/v1/cloud-disk/sources/${id}`);
};

// 点赞列表
export const getLikeList = async (id: string) => {
  return await api.get(`/app/v1/cloud-disk/sources/${id}/likes`);
};

// 评论列表
export const getCommentList = async (id: string) => {
  return await api.get('/app/v1/cloud-disk/sources/comments', {
    params: {
      sourceId: id,
    },
  });
};
// 点赞
export const like = async ({ id }: { id: string }) => {
  return await api.post(`/app/v1/cloud-disk/sources/${id}/likes`);
};
// 评论
export const addComment = async (data: object) => {
  return await api.post('/app/v1/cloud-disk/sources/comments', data);
};
// 删除评论
export const deleteComment = async (commentId: string) => {
  return await api.delete(`/app/v1/cloud-disk/sources/comments/${commentId}`);
};
// 收藏
export const toggleBookmark = async ({
  id,
  isBookmark,
}: {
  id: string;
  isBookmark: boolean;
}) => {
  return await api.post(`/app/v1/cloud-disk/sources/${id}/star`, {
    action: isBookmark ? 'starred' : 'cancel',
  });
};
// 上报
export const report = async ({
  fileId,
  operlogType,
}: {
  fileId: string;
  operlogType: '1' | '2' | '3';
}) => {
  return await api.post('/app/v1/cloud-disk/sources/report', {
    params: {
      operlogType,
      fileId,
    },
  });
};
