import { Toast } from 'antd-mobile';
import axios from 'axios';
import Cookies from 'js-cookie';
import { useCommonStore } from '@/store/useCommonStore';
import { getMobile } from '@/utils';

// 定义 API 响应的通用类型接口
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

const api = axios.create({
  baseURL: process.env.NEXT_APP_API_HOST,
  timeout: 60_000,
});

// 添加请求拦截器
api.interceptors.request.use(
  (config) => {
    // 从 Zustand Store 里拿 token
    const token = useCommonStore.getState().authorization;
    const { version } = useCommonStore.getState();
    const { appType } = useCommonStore.getState();
    const { brand } = useCommonStore.getState();
    const os = getMobile();
    // const token = 'smaiMLjBc03L4Ektdb22TiMiEtjkdzv5wpYNjBUV28A=';
    console.log('🚀 ~ file: api.ts:17 ~ token:', token);
    config.headers.Authorization = token || Cookies.get('Authorization');
    // 添加 UnionId 请求头信息
    const unionId = Cookies.get('UnionId');
    if (unionId) {
      config.headers.UnionId = unionId;
    }
    if (brand) {
      config.headers['Brand'] = brand;
    }
    if (os) {
      config.headers['OS'] = os;
    }
    config.headers['Accept-Language'] = 'zh';
    config.headers['App-Type'] = appType;
    config.headers.version = version;
    return config;
  },
  (error) => {
    // 对请求错误做些什么
    return Promise.reject(error);
  }
);

// 添加响应拦截器
api.interceptors.response.use(
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  (response): any => {
    return response.data;
  },
  (error) => {
    console.error('请求错误：', error);
    // 对响应错误做点什么
    if (error.response) {
      // 请求已发送，响应状态码不为 2xx
      const { status, data } = error.response;
      // 根据状态码处理不同类型的错误
      if (status === 400) {
        // 处理请求参数错误
        Toast.show({ content: data.message || '请求参数不正确' });
      } else if (status === 401) {
        // 处理未授权或 token 失效
        Toast.show({ content: data.message || '请先登录' });
        // 可以在这里跳转到登录页面
      } else if (status === 404) {
        // 处理请求地址不存在
        Toast.show({ content: data.message || '请求的资源不存在' });
      } else if (status === 500) {
        // 处理服务端错误
        Toast.show({ content: data.message || '服务器出错了' });
      } else {
        // 其他类型的错误
        Toast.show({ content: '请求失败，请稍后再试' });
      }
    } else if (error.request) {
      // 请求已发送但没有收到响应
      Toast.show({ content: '网络似乎出现了问题' });
    } else {
      // 发生了一些其它错误
      console.log('Error', error.message);
    }
    return Promise.reject(error);
  }
);

export default api;
