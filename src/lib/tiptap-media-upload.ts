import { compress, generateKey, uploadObs } from '@/utils/obs';

// 文件大小限制
export const MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
export const MAX_VIDEO_SIZE = 100 * 1024 * 1024; // 100MB
export const MAX_AUDIO_SIZE = 50 * 1024 * 1024; // 50MB

// 支持的文件类型
export const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
  'image/gif',
];

export const SUPPORTED_VIDEO_TYPES = [
  'video/mp4',
  'video/mov',
  'video/avi',
  'video/webm',
  'video/quicktime',
];

export const SUPPORTED_AUDIO_TYPES = [
  'audio/mp3',
  'audio/wav',
  'audio/ogg',
  'audio/m4a',
];

// 媒体类型枚举
export enum MediaType {
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
}

// 上传进度回调类型
export type ProgressCallback = (progress: number) => void;

// 上传结果类型
export interface UploadResult {
  url: string;
  type: MediaType;
  size: number;
  duration?: number; // 视频/音频时长（秒）
  width?: number; // 图片/视频宽度
  height?: number; // 图片/视频高度
  poster?: string; // 视频封面图
}

/**
 * 验证文件类型和大小
 */
function validateFile(file: File): { isValid: boolean; error?: string } {
  // 检查文件类型
  const isImage = SUPPORTED_IMAGE_TYPES.includes(file.type);
  const isVideo = SUPPORTED_VIDEO_TYPES.includes(file.type);
  const isAudio = SUPPORTED_AUDIO_TYPES.includes(file.type);

  if (!(isImage || isVideo || isAudio)) {
    return {
      isValid: false,
      error: '不支持的文件类型',
    };
  }

  // 检查文件大小
  let maxSize = 0;
  if (isImage) maxSize = MAX_IMAGE_SIZE;
  else if (isVideo) maxSize = MAX_VIDEO_SIZE;
  else if (isAudio) maxSize = MAX_AUDIO_SIZE;

  if (file.size > maxSize) {
    const maxSizeMB = Math.round(maxSize / (1024 * 1024));
    return {
      isValid: false,
      error: `文件大小不能超过 ${maxSizeMB}MB`,
    };
  }

  return { isValid: true };
}

/**
 * 获取媒体类型
 */
function getMediaType(file: File): MediaType {
  if (SUPPORTED_IMAGE_TYPES.includes(file.type)) return MediaType.IMAGE;
  if (SUPPORTED_VIDEO_TYPES.includes(file.type)) return MediaType.VIDEO;
  if (SUPPORTED_AUDIO_TYPES.includes(file.type)) return MediaType.AUDIO;
  throw new Error('不支持的文件类型');
}

/**
 * 获取图片尺寸
 */
function getImageDimensions(
  file: File
): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(file);

    img.onload = () => {
      URL.revokeObjectURL(url);
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('无法获取图片尺寸'));
    };

    img.src = url;
  });
}

/**
 * 获取视频元数据（时长、尺寸、封面）
 */
function getVideoMetadata(file: File): Promise<{
  duration: number;
  width: number;
  height: number;
  poster: string;
}> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const url = URL.createObjectURL(file);

    video.preload = 'metadata';
    video.muted = true; // 避免自动播放策略问题

    video.onloadedmetadata = () => {
      const duration = video.duration;
      const width = video.videoWidth;
      const height = video.videoHeight;

      // 生成封面图（第1秒的帧）
      video.currentTime = Math.min(1, duration / 2);
    };

    video.onseeked = () => {
      try {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        ctx?.drawImage(video, 0, 0);

        const poster = canvas.toDataURL('image/jpeg', 0.8);
        URL.revokeObjectURL(url);

        resolve({
          duration: video.duration,
          width: video.videoWidth,
          height: video.videoHeight,
          poster,
        });
      } catch (error) {
        URL.revokeObjectURL(url);
        reject(new Error('无法生成视频封面'));
      }
    };

    video.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('无法获取视频元数据'));
    };

    video.src = url;
  });
}

/**
 * 获取音频时长
 */
function getAudioDuration(file: File): Promise<number> {
  return new Promise((resolve, reject) => {
    const audio = document.createElement('audio');
    const url = URL.createObjectURL(file);

    audio.preload = 'metadata';

    audio.onloadedmetadata = () => {
      URL.revokeObjectURL(url);
      resolve(audio.duration);
    };

    audio.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('无法获取音频时长'));
    };

    audio.src = url;
  });
}

/**
 * Tiptap 专用的媒体上传函数
 * 支持图片、视频、音频上传，自动压缩和元数据提取
 */
export async function uploadTiptapMedia(
  file: File,
  onProgress?: ProgressCallback,
  abortSignal?: AbortSignal
): Promise<UploadResult> {
  // 验证文件
  const validation = validateFile(file);
  if (!validation.isValid) {
    throw new Error(validation.error);
  }

  // 检查是否被取消
  if (abortSignal?.aborted) {
    throw new Error('上传已取消');
  }

  const mediaType = getMediaType(file);
  let uploadFile: File | Blob = file;
  let metadata: Partial<UploadResult> = {};

  try {
    // 根据媒体类型处理文件和获取元数据
    switch (mediaType) {
      case MediaType.IMAGE: {
        // 图片压缩
        uploadFile = await compress(file);
        const dimensions = await getImageDimensions(file);
        metadata = {
          width: dimensions.width,
          height: dimensions.height,
        };
        break;
      }

      case MediaType.VIDEO: {
        // 视频不压缩，但获取元数据
        const videoMeta = await getVideoMetadata(file);
        metadata = {
          duration: videoMeta.duration,
          width: videoMeta.width,
          height: videoMeta.height,
          poster: videoMeta.poster,
        };
        break;
      }

      case MediaType.AUDIO: {
        // 音频获取时长
        const duration = await getAudioDuration(file);
        metadata = { duration };
        break;
      }
    }

    // 检查是否被取消
    if (abortSignal?.aborted) {
      throw new Error('上传已取消');
    }

    // 生成上传路径
    const key = generateKey(file.name, 'tiptap');

    // 上传到 OBS
    const url = await uploadObs(uploadFile, key, false, (progress: number) => {
      if (abortSignal?.aborted) return;
      onProgress?.(progress);
    });

    if (!url) {
      throw new Error('上传失败');
    }

    return {
      url,
      type: mediaType,
      size: file.size,
      ...metadata,
    };
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('上传失败');
  }
}

/**
 * 专门用于图片上传的函数
 */
export async function uploadTiptapImage(
  file: File,
  onProgress?: ProgressCallback,
  abortSignal?: AbortSignal
): Promise<UploadResult> {
  if (!SUPPORTED_IMAGE_TYPES.includes(file.type)) {
    throw new Error('请选择图片文件');
  }

  return uploadTiptapMedia(file, onProgress, abortSignal);
}

/**
 * 专门用于视频上传的函数
 */
export async function uploadTiptapVideo(
  file: File,
  onProgress?: ProgressCallback,
  abortSignal?: AbortSignal
): Promise<UploadResult> {
  if (!SUPPORTED_VIDEO_TYPES.includes(file.type)) {
    throw new Error('请选择视频文件');
  }

  return uploadTiptapMedia(file, onProgress, abortSignal);
}

/**
 * 专门用于音频上传的函数
 */
export async function uploadTiptapAudio(
  file: File,
  onProgress?: ProgressCallback,
  abortSignal?: AbortSignal
): Promise<UploadResult> {
  if (!SUPPORTED_AUDIO_TYPES.includes(file.type)) {
    throw new Error('请选择音频文件');
  }

  return uploadTiptapMedia(file, onProgress, abortSignal);
}

/**
 * 批量上传媒体文件
 */
export async function uploadTiptapMediaBatch(
  files: File[],
  onProgress?: (fileIndex: number, progress: number) => void,
  onComplete?: (fileIndex: number, result: UploadResult) => void,
  onError?: (fileIndex: number, error: Error) => void
): Promise<UploadResult[]> {
  const results: UploadResult[] = [];

  // 使用 Promise.all 替代循环中的 await
  const uploadPromises = files.map(async (file, i) => {
    try {
      const result = await uploadTiptapMedia(file, (progress) =>
        onProgress?.(i, progress)
      );

      onComplete?.(i, result);
      return result;
    } catch (error) {
      const err = error instanceof Error ? error : new Error('上传失败');
      onError?.(i, err);
      throw err;
    }
  });

  const uploadResults = await Promise.all(uploadPromises);
  results.push(...uploadResults);

  return results;
}
