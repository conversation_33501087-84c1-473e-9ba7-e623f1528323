import type { Editor } from '@tiptap/react';
import { Video } from 'lucide-react';
import React, { useCallback } from 'react';
import { useHotkeys } from 'react-hotkeys-hook';
import { useIsMobile } from '@/hooks/use-mobile';
import { useTiptapEditor } from '@/hooks/use-tiptap-editor';
import { isExtensionAvailable, isNodeTypeSelected } from '@/lib/tiptap-utils';

export const VIDEO_UPLOAD_SHORTCUT_KEY = 'mod+shift+v';

export interface UseVideoUploadConfig {
  editor?: Editor | null;
  hideWhenUnavailable?: boolean;
  onInserted?: () => void;
}

export function canInsertVideo(editor: Editor | null): boolean {
  if (!(editor && editor.isEditable)) {
    return false;
  }
  if (
    !isExtensionAvailable(editor, 'videoUpload') ||
    isNodeTypeSelected(editor, ['video', 'videoUpload'])
  ) {
    return false;
  }

  return editor.can().insertContent({ type: 'videoUpload' });
}

export function isVideoActive(editor: Editor | null): boolean {
  if (!(editor && editor.isEditable)) {
    return false;
  }
  return editor.isActive('videoUpload');
}

export function insertVideo(editor: Editor | null): boolean {
  if (!(editor && editor.isEditable)) {
    return false;
  }
  if (!canInsertVideo(editor)) {
    return false;
  }

  try {
    return editor.chain().focus().setVideoUploadNode().run();
  } catch {
    return false;
  }
}

export function useVideoUpload(config?: UseVideoUploadConfig) {
  const {
    editor: providedEditor,
    hideWhenUnavailable = false,
    onInserted,
  } = config || {};

  const { editor } = useTiptapEditor(providedEditor);
  const isMobile = useIsMobile();
  const [isVisible, setIsVisible] = React.useState<boolean>(true);
  const canInsert = canInsertVideo(editor);
  const isActive = isVideoActive(editor);

  React.useEffect(() => {
    if (!editor) {
      return;
    }

    const handleSelectionUpdate = () => {
      const shouldShow =
        editor.isEditable && isExtensionAvailable(editor, 'videoUpload');
      if (hideWhenUnavailable) {
        setIsVisible(shouldShow && canInsertVideo(editor));
      } else {
        setIsVisible(shouldShow);
      }
    };

    handleSelectionUpdate();
    editor.on('selectionUpdate', handleSelectionUpdate);

    return () => {
      editor.off('selectionUpdate', handleSelectionUpdate);
    };
  }, [editor, hideWhenUnavailable]);

  const handleVideo = useCallback(() => {
    if (!editor) {
      return false;
    }

    const success = insertVideo(editor);
    if (success) {
      onInserted?.();
    }
    return success;
  }, [editor, onInserted]);

  useHotkeys(
    VIDEO_UPLOAD_SHORTCUT_KEY,
    (event) => {
      event.preventDefault();
      handleVideo();
    },
    {
      enabled: isVisible && canInsert,
      enableOnContentEditable: !isMobile,
      enableOnFormTags: true,
    }
  );

  return {
    isVisible,
    isActive,
    handleVideo,
    canInsert,
    label: '添加视频',
    shortcutKeys: VIDEO_UPLOAD_SHORTCUT_KEY,
    Icon: Video,
  };
}
