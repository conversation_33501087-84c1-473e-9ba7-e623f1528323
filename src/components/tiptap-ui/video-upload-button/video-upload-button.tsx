import type { Editor } from '@tiptap/react';
import type React from 'react';
import { Button } from '@/components/tiptap-ui-primitive/button';
import { useVideoUpload } from './use-video-upload';

interface VideoUploadButtonProps {
  editor?: Editor | null;
  text?: string;
  hideWhenUnavailable?: boolean;
  onInserted?: () => void;
}

export const VideoUploadButton: React.FC<VideoUploadButtonProps> = ({
  editor,
  text = '视频',
  hideWhenUnavailable = false,
  onInserted,
}) => {
  const { isVisible, canInsert, handleVideo, label, isActive, Icon } =
    useVideoUpload({
      editor,
      hideWhenUnavailable,
      onInserted,
    });

  if (!isVisible) return null;

  return (
    <Button
      aria-label={label}
      data-active={isActive}
      data-style="ghost"
      disabled={!canInsert}
      onClick={handleVideo}
      title={label}
    >
      <Icon className="tiptap-button-icon" />
      {text && <span className="ml-1">{text}</span>}
    </Button>
  );
};
