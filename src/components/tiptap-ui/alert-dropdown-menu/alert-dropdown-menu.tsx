import type { Editor } from '@tiptap/core';
import clsx from 'clsx';
import {
  AlertTriangle,
  Bell,
  CheckCircle2,
  ChevronDown,
  Info,
  type LucideIcon,
  XCircle,
} from 'lucide-react';
import { useMemo, useState } from 'react';
import { Button, ButtonGroup } from '@/components/tiptap-ui-primitive/button';
import { Card, CardBody } from '@/components/tiptap-ui-primitive/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/tiptap-ui-primitive/dropdown-menu';

type AlertType = 'info' | 'success' | 'warning' | 'error';

export interface AlertDropdownMenuProps {
  editor: Editor | null;
  className?: string;
  size?: 'sm' | 'md';
}

/**
 * Alert 下拉菜单（整合：Toggle / Info / Success / Warning / Error / Unset）
 * - 使用项目的 tiptap-ui-primitive/dropdown-menu 原子组件
 * - 使用 tailwind + clsx；图标来自 lucide-react
 */
export function AlertDropdownMenu({
  editor,
  className,
  size = 'md',
}: AlertDropdownMenuProps) {
  const [open, setOpen] = useState(false);

  const isActiveAlert = !!editor?.isActive('alert');
  const activeType =
    (editor?.getAttributes('alert')?.type as AlertType | undefined) ??
    undefined;

  const sizeCls = size === 'sm' ? 'h-8 px-2 text-[13px]' : 'h-9 px-3 text-sm';

  const items: {
    key: string;
    label: string;
    type?: AlertType;
    icon: LucideIcon;
    onClick: () => void;
    active?: boolean;
    danger?: boolean;
  }[] = useMemo(() => {
    const toggle = {
      key: 'toggle',
      label: isActiveAlert
        ? 'Toggle Alert（退出或切换）'
        : 'Toggle Alert（包裹）',
      icon: Bell,
      onClick: () => {
        const nextType: AlertType = activeType ?? 'info';
        editor?.chain().focus().toggleAlert({ type: nextType }).run();
        setOpen(false);
      },
    };
    const info = {
      key: 'info',
      label: 'Info',
      type: 'info' as const,
      icon: Info,
      onClick: () => {
        editor?.chain().focus().setAlert({ type: 'info' }).run();
        setOpen(false);
      },
      active: activeType === 'info',
    };
    const success = {
      key: 'success',
      label: 'Success',
      type: 'success' as const,
      icon: CheckCircle2,
      onClick: () => {
        editor?.chain().focus().setAlert({ type: 'success' }).run();
        setOpen(false);
      },
      active: activeType === 'success',
    };
    const warning = {
      key: 'warning',
      label: 'Warning',
      type: 'warning' as const,
      icon: AlertTriangle,
      onClick: () => {
        editor?.chain().focus().setAlert({ type: 'warning' }).run();
        setOpen(false);
      },
      active: activeType === 'warning',
    };
    const error = {
      key: 'error',
      label: 'Error',
      type: 'error' as const,
      icon: XCircle,
      onClick: () => {
        editor?.chain().focus().setAlert({ type: 'error' }).run();
        setOpen(false);
      },
      active: activeType === 'error',
      danger: true,
    };
    const unset = {
      key: 'unset',
      label: 'Unset Alert',
      icon: XCircle,
      onClick: () => {
        editor?.chain().focus().unsetAlert().run();
        setOpen(false);
      },
      danger: true,
    };
    return [toggle, info, success, warning, error, unset];
  }, [editor, isActiveAlert, activeType]);

  const TriggerIcon = activeType
    ? ({
        info: Info,
        success: CheckCircle2,
        warning: AlertTriangle,
        error: XCircle,
      }[activeType] as LucideIcon)
    : Bell;

  return (
    <DropdownMenu onOpenChange={setOpen} open={open}>
      <DropdownMenuTrigger asChild>
        <button
          className={clsx(
            'flex items-center gap-2 rounded-md border border-neutral-200 bg-white transition-colors hover:bg-neutral-50',
            sizeCls,
            className
          )}
          type="button"
        >
          <TriggerIcon
            className={clsx('h-4 w-4', {
              'text-sky-600': activeType === 'info',
              'text-emerald-600': activeType === 'success',
              'text-amber-600': activeType === 'warning',
              'text-rose-600': activeType === 'error',
              'text-neutral-600': !activeType,
            })}
          />
          <span className="text-neutral-700">Alert</span>
          <ChevronDown className="h-3.5 w-3.5 text-neutral-500" />
        </button>
      </DropdownMenuTrigger>

      <DropdownMenuContent className="min-w-44 p-1">
        <Card>
          <CardBody>
            <ButtonGroup>
              {items.map((it) => {
                const Icon = it.icon;
                return (
                  <DropdownMenuItem
                    className={clsx(
                      'flex items-center gap-2 rounded-md px-2.5 py-2 text-left',
                      it.active && 'bg-neutral-100',
                      it.danger && 'text-rose-600'
                    )}
                    key={it.key}
                    onClick={it.onClick}
                  >
                    <Icon
                      className={clsx('h-4 w-4', {
                        'text-sky-600': it.type === 'info',
                        'text-emerald-600': it.type === 'success',
                        'text-amber-600': it.type === 'warning',
                        'text-rose-600': it.type === 'error' || it.danger,
                        'text-neutral-600': !(it.type || it.danger),
                      })}
                    />
                    <span className="text-neutral-800 text-sm">{it.label}</span>
                    {it.active ? (
                      <span className="ml-auto text-[11px] text-neutral-500">
                        当前
                      </span>
                    ) : null}
                  </DropdownMenuItem>
                );
              })}{' '}
            </ButtonGroup>
          </CardBody>
        </Card>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
