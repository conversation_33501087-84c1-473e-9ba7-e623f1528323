import { Extension } from '@tiptap/core';

export interface ProjectContextStorage {
  projectId: string;
  // 未来可扩展其他项目相关信息
  // userId?: string
  // organizationId?: string
}

export const ProjectContextExtension = Extension.create<
  Record<string, never>,
  ProjectContextStorage
>({
  name: 'projectContext',

  addStorage() {
    return {
      projectId: '',
    };
  },

  onBeforeCreate() {
    // 可以在这里添加初始化逻辑
  },
});
