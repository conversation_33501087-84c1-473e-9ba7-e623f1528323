.tiptap-dropdown-menu {
  --tt-dropdown-menu-bg-color: var(--white);
  --tt-dropdown-menu-border-color: var(--tt-gray-light-a-100);
  --tt-dropdown-menu-text-color: var(--tt-gray-light-a-600);

  .dark & {
    --tt-dropdown-menu-border-color: var(--tt-gray-dark-a-50);
    --tt-dropdown-menu-bg-color: var(--tt-gray-dark-50);
    --tt-dropdown-menu-text-color: var(--tt-gray-dark-a-600);
  }
}

/* --------------------------------------------
    --------- DROPDOWN MENU STYLING SETTINGS -----------
    -------------------------------------------- */
.tiptap-dropdown-menu {
  z-index: 50;
  outline: none;
  transform-origin: var(--radix-dropdown-menu-content-transform-origin);
  max-height: var(--radix-dropdown-menu-content-available-height);

  > * {
    max-height: var(--radix-dropdown-menu-content-available-height);
  }

  /* Animation states */
  &[data-state="open"] {
    animation:
      fadeIn 150ms cubic-bezier(0.16, 1, 0.3, 1),
      zoomIn 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  &[data-state="closed"] {
    animation:
      fadeOut 150ms cubic-bezier(0.16, 1, 0.3, 1),
      zoomOut 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  /* Position-based animations */
  &[data-side="top"],
  &[data-side="top-start"],
  &[data-side="top-end"] {
    animation: slideFromBottom 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  &[data-side="right"],
  &[data-side="right-start"],
  &[data-side="right-end"] {
    animation: slideFromLeft 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  &[data-side="bottom"],
  &[data-side="bottom-start"],
  &[data-side="bottom-end"] {
    animation: slideFromTop 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  &[data-side="left"],
  &[data-side="left-start"],
  &[data-side="left-end"] {
    animation: slideFromRight 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }
}
