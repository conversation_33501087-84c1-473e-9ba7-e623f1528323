import * as React from "react"
import { NodeViewContent, NodeViewWrapper } from "@tiptap/react"
import clsx from "clsx"

type Variant = "info" | "success" | "warning" | "error"

interface Props {
  node: {
    attrs: {
      type?: Variant
    }
  }
  updateAttributes: (attrs: Record<string, unknown>) => void
  selected: boolean
  deleteNode: () => void
}

const VARIANTS: { key: Variant; label: string }[] = [
  { key: "info", label: "信息" },
  { key: "success", label: "成功" },
  { key: "warning", label: "提醒" },
  { key: "error", label: "错误" },
]

export default function AlertNodeView({
  node,
  updateAttributes,
  selected,
  deleteNode,
}: Props) {
  const type = (node.attrs?.type || "info") as Variant

  const setType = (v: Variant) => updateAttributes({ type: v })

  return (
    <NodeViewWrapper
      as="div"
      data-alert=""
      data-type={type}
      className={clsx(
        "group my-3 rounded-lg border p-3 transition-colors",
        "outline-offset-2",
        selected && "ring-2 ring-sky-400",
        type === "info" && "border-sky-300/40 bg-sky-50/70 dark:border-sky-400/30 dark:bg-sky-900/20",
        type === "success" && "border-emerald-300/40 bg-emerald-50/70 dark:border-emerald-400/30 dark:bg-emerald-900/20",
        type === "warning" && "border-amber-300/40 bg-amber-50/70 dark:border-amber-400/30 dark:bg-amber-900/20",
        type === "error" && "border-rose-300/40 bg-rose-50/70 dark:border-rose-400/30 dark:bg-rose-900/20",
      )}
    >
      <div className="mb-2 flex items-center gap-2 text-xs text-neutral-600 dark:text-neutral-300">
        <span className="font-medium">Alert</span>

        <div className="ml-auto flex items-center gap-1 opacity-0 transition-opacity group-hover:opacity-100">
          {VARIANTS.map((v) => (
            <button
              key={v.key}
              type="button"
              onClick={() => setType(v.key)}
              className={clsx(
                "rounded px-2 py-1",
                v.key === type
                  ? "bg-neutral-900 text-white dark:bg-white dark:text-neutral-900"
                  : "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-200",
              )}
              contentEditable={false}
            >
              {v.label}
            </button>
          ))}
          <button
            type="button"
            onClick={deleteNode}
            className={clsx(
              "rounded px-2 py-1",
              "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-200",
              "hover:bg-neutral-200 dark:hover:bg-neutral-700",
            )}
            contentEditable={false}
          >
            删除
          </button>
        </div>
      </div>

      <div className="prose prose-sm max-w-none dark:prose-invert">
        <NodeViewContent className="outline-none" />
      </div>
    </NodeViewWrapper>
  )
}
