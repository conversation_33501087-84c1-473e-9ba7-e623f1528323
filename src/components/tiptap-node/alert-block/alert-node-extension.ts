import { mergeAttributes, Node } from "@tiptap/core"
import { ReactNodeViewRenderer } from "@tiptap/react"
import AlertNodeView from "./alert-node"

export type AlertType = "info" | "success" | "warning" | "error"

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    alert: {
      setAlert: (attrs?: { type?: AlertType }) => ReturnType
      toggleAlert: (attrs?: { type?: AlertType }) => ReturnType
      unsetAlert: () => ReturnType
    }
  }
}

/**
 * Alert 块节点（v3）
 * data-alert="" data-type="info|success|warning|error"
 * 可编辑 block 内容，支持切换变体与快捷键
 */
export const AlertNode = Node.create({
  name: "alert",

  group: "block",
  content: "block+",
  defining: true,
  selectable: true,
  draggable: false,

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  addAttributes() {
    return {
      type: {
        default: "info",
        parseHTML: (element: HTMLElement) =>
          element.getAttribute("data-type") || "info",
        renderHTML: (attributes: Record<string, any>) => {
          if (!attributes.type) return {}
          return { "data-type": attributes.type }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-alert]',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [
      "div",
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        "data-alert": "",
      }),
      0,
    ]
  },

  addNodeView() {
    return ReactNodeViewRenderer(AlertNodeView)
  },

  addCommands() {
    return {
      setAlert:
        (attrs) =>
        ({ commands }) => {
          return commands.wrapIn(this.name, attrs)
        },
      toggleAlert:
        (attrs) =>
        ({ commands, state }) => {
          const { $from, to } = state.selection as any
          const range = $from.blockRange(to)
          const inAlert = range?.parent?.type?.name === this.name
          if (inAlert) {
            return commands.lift(this.name)
          }
          return commands.wrapIn(this.name, attrs)
        },
      unsetAlert:
        () =>
        ({ commands }) => {
          return commands.lift(this.name)
        },
    }
  },

  addKeyboardShortcuts() {
    return {
      "Mod-Alt-a": () => this.editor.commands.toggleAlert(),
      Backspace: () => {
        const { state, commands } = this.editor
        const { $from } = state.selection as any
        if (
          $from.parent.type.name === this.name &&
          $from.parent.textContent.length === 0
        ) {
          return commands.lift(this.name)
        }
        return false
      },
      Enter: () => {
        // 在块尾部回车，退出块并新起段落
        const { state, commands } = this.editor
        const { $from, $to } = state.selection as any
        if ($from.parent.type.name === this.name && $from.pos === $to.pos) {
          return commands.splitBlock()
        }
        return false
      },
    }
  },
})

export default AlertNode
