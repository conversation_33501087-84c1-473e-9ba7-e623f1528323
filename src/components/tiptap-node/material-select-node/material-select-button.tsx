import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query';
import type { Editor } from '@tiptap/core';
import {
  Button as AntdButton,
  Image as AntImage,
  Checkbox,
  InfiniteScroll,
  Loading,
  Popup,
  Segmented,
  Tag,
} from 'antd-mobile';
import clsx from 'clsx';
import { Folder, Image as ImageIcon, Play, Video, Volume2 } from 'lucide-react';
import React from 'react';
import { getMaterialList } from '@/api/pbl';
import type { ProjectContextStorage } from '@/components/tiptap-extensions/project-context-extension';

// 素材数据类型 - 直接使用后端返回格式
interface MaterialItem {
  instId: string;
  mediaId: string;
  observationId: string;
  type: number; // 1=图片，2=视频，3=音频
  url: string;
  fileSize: string;
  cover: string;
  duration?: number;
  videoPlayType: number;
  handleStatus: number;
  handleError: string;
  createUserId: string;
  createTime: string;
  updateTime: string;
  projectId: string;
  source: number;
  name: string;
  deptId: string;
  uploadErr: string;
  uploadFailTimes: number;
  uploadTime: string;
  fileid: string;
  checkStatus: string;
  reprocessing: string;
  checkResult: string;
  reprocessingTime: string | null;
  handleEndTime: string;
  isMerge: number;
  sort: number;
  caption: string;
  zone: string | null;
  tags: string;
  createUser: {
    id: string;
    name: string;
    avatar: string;
  };
}

// 格式化文件大小
const formatFileSize = (size: string) => {
  const sizeNum = Number.parseInt(size, 10);
  if (sizeNum < 1024 * 1024) {
    return `${(sizeNum / 1024).toFixed(1)}KB`;
  }
  return `${(sizeNum / (1024 * 1024)).toFixed(1)}MB`;
};

// 格式化时长
const formatDuration = (seconds?: number) => {
  if (!seconds) {
    return;
  }
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 获取素材类型文本
const getMaterialTypeText = (type: number) => {
  const typeMap = { 1: '图片', 2: '视频', 3: '音频' };
  return typeMap[type as keyof typeof typeMap] || '未知';
};

type Props = {
  editor: Editor | null;
  buttonText?: string;
  onClick?: () => void;
};

type SelectionMode = 'all' | 'images' | 'videos' | 'audios';

const pageSize = 10;

const MaterialSelectButton: React.FC<Props> = ({
  editor,
  buttonText,
  onClick,
}) => {
  const [visible, setVisible] = React.useState(false);
  const [selectedIds, setSelectedIds] = React.useState<string[]>([]);
  const [mode, setMode] = React.useState<SelectionMode>('all');
  const queryClient = useQueryClient();

  // 从 editor storage 中获取 projectId
  const projectId = React.useMemo(() => {
    if (!editor) {
      return '';
    }
    try {
      return (
        (editor.storage as unknown as { projectContext: ProjectContextStorage })
          .projectContext?.projectId || ''
      );
    } catch {
      return '';
    }
  }, [editor]);

  // 获取素材列表（分页）
  const {
    data: infiniteMaterialData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
    refetch,
  } = useInfiniteQuery({
    queryKey: ['materialList', projectId],
    queryFn: async ({ pageParam = 1 }) => {
      if (!projectId) {
        // 当 projectId 为空时，返回一个符合期望的空结构，避免抛出错误
        return { data: [], page: pageParam, hasMore: false };
      }
      const response = await getMaterialList({
        projectId,
        page: pageParam,
        perPage: pageSize,
      });
      // @ts-expect-error
      const materials = response?.list || [];
      console.log('🚀 ~ materials111:', materials);

      return {
        data: materials as MaterialItem[],
        page: pageParam,
        hasMore: materials.length >= pageSize,
      };
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (!lastPage.hasMore) {
        return;
      }
      return lastPage.page + 1;
    },
    enabled: false, // 默认禁用，手动触发
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
  });

  // 从 infiniteMaterialData 中提取素材数据
  const materials = infiniteMaterialData?.pages
    ? infiniteMaterialData.pages.flatMap((page) => {
        if (page?.data) {
          return page.data;
        }
        return [];
      })
    : [];

  // 加载更多数据的处理函数
  const loadMore = async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  };

  const handleOpen = () => {
    onClick?.();
    setVisible(true);
  };

  const toggleSelectMaterial = (id: string) => {
    setSelectedIds((prev) =>
      prev.includes(id) ? prev.filter((x) => x !== id) : [...prev, id]
    );
  };

  const clearSelection = () => {
    setSelectedIds([]);
  };

  const handleConfirm = () => {
    if (!editor) {
      return;
    }

    const selectedMaterials = materials.filter((material) =>
      selectedIds.includes(material.mediaId)
    );

    console.log('selectedMaterials', selectedMaterials);

    // 插入选中的素材到编辑器
    if (selectedMaterials.length > 0) {
      // 创建要插入的内容节点数组
      const nodes = selectedMaterials.flatMap((material, index) => {
        let node: object | null = null;

        if (material.type === 1) {
          // 1=图片
          node = {
            type: 'image',
            attrs: {
              src: material.url,
              alt: material.name,
              title: material.name,
              width: null,
              height: null,
            },
          };
        } else if (material.type === 2) {
          // 2=视频
          node = {
            type: 'video',
            attrs: {
              src: material.url,
              poster:
                material.cover ||
                `${material.url}?x-workflow-graph-name=video-thumbnail`, // 使用 cover 作为视频封面
              title: material.name,
              width: null,
              height: null,
              duration: material.duration,
            },
          };
        }

        if (node) {
          // 如果不是第一个素材，在前面添加一个空段落作为分隔
          if (index > 0) {
            return [{ type: 'paragraph' }, node];
          }
          return [node];
        }

        return [];
      });

      // 一次性插入所有节点
      editor.chain().focus().insertContent(nodes).run();
    }

    setVisible(false);
    clearSelection();
  };

  // 根据模式过滤素材
  const filteredMaterials = materials.filter((material) => {
    switch (mode) {
      case 'all':
        return true; // 显示所有素材
      case 'images':
        return material.type === 1; // 1=图片
      case 'videos':
        return material.type === 2; // 2=视频
      case 'audios':
        return material.type === 3; // 3=音频
      default:
        return true;
    }
  });

  return (
    <>
      <button
        className="flex flex-col items-center rounded-xl bg-indigo-50 p-3 font-medium text-neutral-700 text-xs transition-colors hover:bg-indigo-100 active:bg-indigo-200"
        onClick={handleOpen}
        type="button"
      >
        <Folder className="mb-1 h-5 w-5 text-indigo-500 text-xs" />
        {buttonText || '从素材库选择'}
      </button>

      <Popup
        afterClose={() => {
          // 在关闭动画结束后清空数据，避免 visual bug
          queryClient.removeQueries({ queryKey: ['materialList', projectId] });
          clearSelection();
          setMode('all');
        }}
        afterShow={() => {
          // 动画后请求数据，避免 InfiniteScroll bug
          if (projectId) {
            refetch().catch(console.error);
          }
        }}
        bodyClassName="p-0 h-[85vh] rounded-t-2xl"
        onClose={() => {
          setVisible(false);
        }}
        position="bottom"
        visible={visible}
      >
        <div className="flex h-full w-full flex-col">
          <div className="sticky top-0 z-10 flex items-center justify-between border-zinc-200 border-b bg-white px-4 py-3 dark:border-zinc-800 dark:bg-zinc-900">
            <div className="flex items-center gap-3">
              <Segmented
                className="text-xs"
                onChange={(value) => setMode(value as SelectionMode)}
                options={[
                  { label: '全部', value: 'all' },
                  { label: '图片', value: 'images' },
                  { label: '视频', value: 'videos' },
                  { label: '音频', value: 'audios' },
                ]}
                value={mode}
              />
            </div>
          </div>

          <div className="flex-1 overflow-y-auto px-4 py-3">
            <div className="mb-3 text-xs text-zinc-500 dark:text-zinc-400">
              选择要插入的素材。支持多选。
            </div>

            {/* 加载状态 */}
            {isLoading && (
              <div className="flex items-center justify-center py-8">
                <Loading />
                <span className="ml-2 text-zinc-500">加载中...</span>
              </div>
            )}

            {/* 错误状态 */}
            {error && (
              <div className="py-4">
                <div className="py-8 text-center text-red-500">
                  加载失败，请重试
                </div>
                <div className="mt-4 text-center">
                  <AntdButton
                    onClick={() => {
                      refetch().catch(console.error);
                    }}
                    size="small"
                  >
                    重试
                  </AntdButton>
                </div>
              </div>
            )}

            {/* 空状态 */}
            {!(isLoading || error) && materials.length === 0 && (
              <div className="py-8 text-center text-zinc-500 dark:text-zinc-400">
                暂无素材
              </div>
            )}

            {/* 数据列表 */}
            {!(isLoading || error) && materials.length > 0 && (
              <>
                <div className="space-y-4">
                  {filteredMaterials.map((material) => {
                    const checked = selectedIds.includes(material.mediaId);
                    return (
                      <button
                        className={clsx(
                          'w-full rounded-lg border p-3 text-left transition-colors',
                          'border-zinc-200 hover:border-zinc-300 dark:border-zinc-800 dark:hover:border-zinc-700',
                          checked && 'border-indigo-500'
                        )}
                        key={material.mediaId}
                        onClick={() => toggleSelectMaterial(material.mediaId)}
                        type="button"
                      >
                        <div className="flex items-start justify-between gap-2">
                          <div className="flex min-w-0 flex-1 flex-col items-start">
                            <div className="mb-1 flex items-center gap-2">
                              {material.type === 1 && (
                                <ImageIcon className="h-4 w-4 text-blue-500" />
                              )}
                              {material.type === 2 && (
                                <Video className="h-4 w-4 text-green-500" />
                              )}
                              {material.type === 3 && (
                                <Volume2 className="h-4 w-4 text-orange-500" />
                              )}
                              <div className="text-sm text-zinc-600 dark:text-zinc-300">
                                {material.name || '未命名文件'}
                              </div>
                            </div>
                          </div>
                          <Checkbox
                            checked={checked}
                            onChange={(val) => {
                              if (val) {
                                !selectedIds.includes(material.mediaId) &&
                                  toggleSelectMaterial(material.mediaId);
                              } else {
                                selectedIds.includes(material.mediaId) &&
                                  toggleSelectMaterial(material.mediaId);
                              }
                            }}
                            onClick={(e) => e.stopPropagation()}
                          />
                        </div>
                        <div className="mt-2">
                          <div className="relative aspect-video w-[200px]">
                            <AntImage
                              className="aspect-square w-full rounded-md object-cover"
                              fit="cover"
                              src={
                                material.cover ||
                                `${material.url}?x-workflow-graph-name=video-thumbnail`
                              }
                            />
                            <div className="absolute inset-0 flex items-center justify-center rounded-md bg-black bg-opacity-30">
                              <Play className="h-8 w-8 text-white" />
                            </div>
                          </div>
                        </div>
                        <div className="mt-2 flex items-center justify-between gap-2">
                          <div className="text-sm text-zinc-500 dark:text-zinc-400">
                            {material.createUser.name} · {material.createTime}
                          </div>
                          <div className="text-sm">
                            {formatFileSize(material.fileSize)}
                          </div>
                        </div>
                      </button>
                    );
                  })}
                </div>

                {filteredMaterials.length === 0 && (
                  <div className="py-8 text-center text-zinc-500 dark:text-zinc-400">
                    {(() => {
                      let typeText = '素材';
                      if (mode === 'all') {
                        typeText = '素材';
                      } else if (mode === 'images') {
                        typeText = '图片素材';
                      } else if (mode === 'videos') {
                        typeText = '视频素材';
                      } else if (mode === 'audios') {
                        typeText = '音频素材';
                      }
                      return `暂无${typeText}`;
                    })()}
                  </div>
                )}

                {/* 无限滚动组件 */}
                <InfiniteScroll
                  className="!py-2"
                  hasMore={!!hasNextPage}
                  loadMore={loadMore}
                  threshold={100}
                >
                  {(() => {
                    if (isFetchingNextPage) {
                      return (
                        <div className="py-3 text-center">
                          <span className="text-gray-500 text-sm">
                            加载更多数据...
                          </span>
                        </div>
                      );
                    }
                    if (hasNextPage) {
                      return (
                        <div className="py-3 text-center">
                          <span className="text-gray-500 text-sm">
                            上拉加载更多
                          </span>
                        </div>
                      );
                    }
                    return (
                      <div className="py-3 text-center">
                        <span className="text-gray-500 text-sm">
                          没有更多数据了
                        </span>
                      </div>
                    );
                  })()}
                </InfiniteScroll>
              </>
            )}
          </div>

          {/* bottom action bar */}
          <div className="flex-shrink-0 border-zinc-200 border-t p-4 dark:border-zinc-800">
            <div className="flex gap-3">
              <AntdButton
                className="flex-1"
                onClick={() => {
                  setVisible(false);
                  clearSelection();
                }}
              >
                取消
              </AntdButton>
              <AntdButton
                className="flex-1"
                color="primary"
                disabled={selectedIds.length === 0}
                onClick={handleConfirm}
              >
                确定(
                {selectedIds.length})
              </AntdButton>
            </div>
          </div>
        </div>
      </Popup>
    </>
  );
};

export default MaterialSelectButton;
