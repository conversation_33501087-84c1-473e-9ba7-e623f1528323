:root {
  --tiptap-video-upload-active: var(--tt-brand-color-500);
  --tiptap-video-upload-progress-bg: var(--tt-brand-color-50);
  --tiptap-video-upload-icon-bg: var(--tt-brand-color-500);

  --tiptap-video-upload-text-color: var(--tt-gray-light-a-700);
  --tiptap-video-upload-subtext-color: var(--tt-gray-light-a-400);
  --tiptap-video-upload-border: var(--tt-gray-light-a-300);
  --tiptap-video-upload-border-hover: var(--tt-gray-light-a-400);
  --tiptap-video-upload-border-active: var(--tt-brand-color-500);

  --tiptap-video-upload-icon-doc-bg: var(--tt-gray-light-a-200);
  --tiptap-video-upload-icon-doc-border: var(--tt-gray-light-300);
  --tiptap-video-upload-icon-color: var(--white);
}

.dark {
  --tiptap-video-upload-active: var(--tt-brand-color-400);
  --tiptap-video-upload-progress-bg: var(--tt-brand-color-900);
  --tiptap-video-upload-icon-bg: var(--tt-brand-color-400);

  --tiptap-video-upload-text-color: var(--tt-gray-dark-a-700);
  --tiptap-video-upload-subtext-color: var(--tt-gray-dark-a-400);
  --tiptap-video-upload-border: var(--tt-gray-dark-a-300);
  --tiptap-video-upload-border-hover: var(--tt-gray-dark-a-400);
  --tiptap-video-upload-border-active: var(--tt-brand-color-400);

  --tiptap-video-upload-icon-doc-bg: var(--tt-gray-dark-a-200);
  --tiptap-video-upload-icon-doc-border: var(--tt-gray-dark-300);
  --tiptap-video-upload-icon-color: var(--black);
}

.tiptap-video-upload {
  margin: 2rem 0;

  input[type="file"] {
    display: none;
  }

  .tiptap-video-upload-dropzone {
    position: relative;
    width: 5rem;
    height: 5rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .tiptap-video-upload-icon-container {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: var(--tiptap-video-upload-icon-bg);
    border-radius: var(--tt-radius-lg, 0.75rem);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .tiptap-video-upload-icon {
    width: 2rem;
    height: 2rem;
    color: var(--tiptap-video-upload-icon-color);
  }

  .tiptap-video-upload-text {
    color: var(--tiptap-video-upload-text-color);
    font-weight: 500;
    font-size: 0.875rem;
    line-height: normal;

    em {
      font-style: normal;
      text-decoration: underline;
    }
  }

  .tiptap-video-upload-subtext {
    color: var(--tiptap-video-upload-subtext-color);
    font-weight: 600;
    line-height: normal;
    font-size: 0.75rem;
  }

  .tiptap-video-upload-drag-area {
    padding: 2rem 1.5rem;
    border: 1.5px dashed var(--tiptap-video-upload-border);
    border-radius: var(--tt-radius-md, 0.5rem);
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--tiptap-video-upload-border-hover);
    }

    &.drag-active {
      border-color: var(--tiptap-video-upload-border-active);
      background-color: rgba(
        var(--tiptap-video-upload-active-rgb, 0, 123, 255),
        0.05
      );
    }

    &.drag-over {
      border-color: var(--tiptap-video-upload-border-active);
      background-color: rgba(
        var(--tiptap-video-upload-active-rgb, 0, 123, 255),
        0.1
      );
    }
  }

  .tiptap-video-upload-content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 0.25rem;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .tiptap-video-upload-previews {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .tiptap-video-upload-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--tiptap-video-upload-border);
    margin-bottom: 0.5rem;

    span {
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--tiptap-video-upload-text-color);
    }
  }

  // Individual File Preview Styles
  .tiptap-video-upload-preview {
    position: relative;
    border-radius: var(--tt-radius-md, 0.5rem);
    overflow: hidden;

    .tiptap-video-upload-progress {
      position: absolute;
      inset: 0;
      background-color: var(--tiptap-video-upload-progress-bg);
      transition: all 300ms ease-out;
    }

    .tiptap-video-upload-preview-content {
      position: relative;
      border: 1px solid var(--tiptap-video-upload-border);
      border-radius: var(--tt-radius-md, 0.5rem);
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .tiptap-video-upload-file-info {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      height: 2rem;

      .tiptap-video-upload-file-icon {
        padding: 0.5rem;
        background-color: var(--tiptap-video-upload-icon-bg);
        border-radius: var(--tt-radius-lg, 0.75rem);

        svg {
          width: 0.875rem;
          height: 0.875rem;
          color: var(--tiptap-video-upload-icon-color);
        }
      }
    }

    .tiptap-video-upload-details {
      display: flex;
      flex-direction: column;
    }

    .tiptap-video-upload-actions {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .tiptap-video-upload-progress-text {
        font-size: 0.75rem;
        color: var(--tiptap-video-upload-border-active);
        font-weight: 600;
      }
    }
  }
}

.tiptap.ProseMirror.ProseMirror-focused {
  .ProseMirror-selectednode .tiptap-video-upload-drag-area {
    border-color: var(--tiptap-video-upload-active);
  }
}

@media (max-width: 480px) {
  .tiptap-video-upload {
    .tiptap-video-upload-drag-area {
      padding: 1.5rem 1rem;
    }

    .tiptap-video-upload-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    .tiptap-video-upload-preview-content {
      padding: 0.75rem;
    }
  }
}
