'use client';

import type { NodeViewProps } from '@tiptap/react';
import { NodeViewWrapper } from '@tiptap/react';
import clsx from 'clsx';
import { X } from 'lucide-react';

export const VideoNodeComponent: React.FC<NodeViewProps> = (props) => {
  const { node, editor, getPos, selected } = props;
  const { src, poster, title, width, height } = node.attrs;

  const handleDelete = () => {
    const pos = getPos();
    if (typeof pos === 'number') {
      editor
        .chain()
        .focus()
        .deleteRange({ from: pos, to: pos + node.nodeSize })
        .run();
    }
  };

  return (
    <NodeViewWrapper
      className={clsx(
        'video-node-wrapper relative my-4',
        selected && 'ProseMirror-selectednode'
      )}
    >
      <div className="group relative">
        <video
          className="h-auto w-full rounded-lg bg-black"
          controls
          height={height ?? undefined}
          poster={poster ?? undefined}
          src={src}
          title={title ?? undefined}
          width={width ?? undefined}
        >
          您的浏览器不支持视频播放
        </video>

        <button
          aria-label="删除视频"
          className="absolute top-2 right-2 rounded-full bg-black/70 p-1.5 text-white transition-colors hover:bg-black/90"
          onClick={handleDelete}
          type="button"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    </NodeViewWrapper>
  );
};

export default VideoNodeComponent;
