import type { CommandProps } from '@tiptap/core';
import { mergeAttributes, Node } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
import { VideoNodeComponent } from './video-node';

export interface VideoNodeOptions {
  /**
   * HTML attributes to add to the video element.
   * @default {}
   */
  HTMLAttributes: Record<string, unknown>;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    video: {
      /**
       * Insert a video node
       */
      setVideo: (options: {
        src: string;
        poster?: string;
        title?: string;
        width?: number;
        height?: number;
        duration?: number;
      }) => ReturnType;
    };
  }
}

/**
 * Custom Tiptap video node that renders a React NodeView player in the editor,
 * and exports to a semantic <video> element for HTML rendering.
 */
export const VideoNodeExtension = Node.create<VideoNodeOptions>({
  name: 'video',

  group: 'block',

  inline: false,

  atom: true,

  draggable: true,

  selectable: true,

  addOptions() {
    return {
      HTMLAttributes: {},
    };
  },

  addAttributes() {
    return {
      src: {
        default: null,
        parseHTML: (element) => element.getAttribute('src'),
        renderHTML: (attributes) => {
          if (!attributes.src) {
            return {};
          }
          return { src: attributes.src };
        },
      },
      poster: {
        default: null,
        parseHTML: (element) => element.getAttribute('poster'),
        renderHTML: (attributes) => {
          if (!attributes.poster) {
            return {};
          }
          return { poster: attributes.poster };
        },
      },
      title: {
        default: null,
        parseHTML: (element) => element.getAttribute('title'),
        renderHTML: (attributes) => {
          if (!attributes.title) {
            return {};
          }
          return { title: attributes.title };
        },
      },
      width: {
        default: null,
        parseHTML: (element) => {
          const v = element.getAttribute('width');
          return v ? Number(v) : null;
        },
        renderHTML: (attributes) => {
          if (!attributes.width) {
            return {};
          }
          return { width: attributes.width };
        },
      },
      height: {
        default: null,
        parseHTML: (element) => {
          const v = element.getAttribute('height');
          return v ? Number(v) : null;
        },
        renderHTML: (attributes) => {
          if (!attributes.height) {
            return {};
          }
          return { height: attributes.height };
        },
      },
      duration: {
        default: null,
        parseHTML: (element) => {
          const v = element.getAttribute('data-duration');
          return v ? Number(v) : null;
        },
        renderHTML: (attributes) => {
          if (!attributes.duration) {
            return {};
          }
          return { 'data-duration': String(attributes.duration) };
        },
      },
    };
  },

  parseHTML() {
    return [{ tag: 'video' }];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'video',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        controls: true,
        playsinline: 'true',
      }),
    ];
  },

  addCommands() {
    return {
      setVideo:
        (options: {
          src: string;
          poster?: string;
          title?: string;
          width?: number;
          height?: number;
          duration?: number;
        }) =>
        ({ commands }: CommandProps) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          });
        },
    };
  },

  addNodeView() {
    return ReactNodeViewRenderer(VideoNodeComponent);
  },
});

export default VideoNodeExtension;
