import type { Editor, JSONContent } from '@tiptap/core';
import { Extension } from '@tiptap/core';

// 媒体类型定义
export type MediaItem = {
  mediaId: string;
  observationId: string;
  type: 1 | 2 | 3; // 1:图片, 2:视频, 3:音频
  url: string;
  fileSize?: string;
  cover?: string; // 视频封面
  duration?: number;
  name?: string;
  [key: string]: unknown;
};

// 观察记录类型定义
export type ObservationRecord = {
  observationId: string;
  title: string;
  content: string;
  userId: string;
  userRole: number;
  type: number;
  delFlag: number;
  labels: string[];
  createTime: string;
  updateTime: string;
  source: string;
  deptId: string;
  observeDate: string;
  projectId: string;
  handleStatus: number;
  handleTimes: number;
  zone?: string | null;
  tags: {
    tagId: string;
    tagName: string;
  }[];
  medias: MediaItem[];
  createUser: {
    id: string;
    name: string;
    avatar: string;
  };
  // 兼容旧格式
  id?: string;
  time?: string;
  author?: {
    name: string;
    avatar: string;
  };
  images?: string[];
};

export function insertObservationRecords(
  editor: Editor,
  records: ObservationRecord[]
) {
  if (!(editor && records?.length)) {
    return;
  }

  const nodes: JSONContent[] = records.flatMap((r) => {
    // 处理媒体文件，优先使用新格式
    const mediaNodes: JSONContent[] = [];
    if (r.medias?.length) {
      for (const media of r.medias) {
        if (media.type === 1) {
          // 图片
          mediaNodes.push({
            type: 'image',
            attrs: {
              src: media.url,
              alt: r.title,
              title: r.title,
            },
          });
        } else if (media.type === 2) {
          // 视频
          mediaNodes.push({
            type: 'video',
            attrs: {
              src: media.url,
              poster: media.cover,
              title: r.title,
              duration: media.duration,
            },
          });
        }
        // 音频暂时不插入到编辑器中，只在选择界面显示
      }
    } else if (r.images?.length) {
      // 兼容旧格式
      for (const url of r.images) {
        mediaNodes.push({
          type: 'image',
          attrs: {
            src: url,
            alt: r.title,
            title: r.title,
          },
        });
      }
    }

    // 获取时间和作者信息，兼容新旧格式
    const time = r.createTime || r.time || new Date().toISOString();
    const authorName = r.createUser?.name || r.author?.name || '未知';

    const titleParagraph: JSONContent = {
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: `【观察】${r.title} `,
          marks: [{ type: 'bold' }],
        },
        { type: 'text', text: `（${time} · ${authorName}）` },
      ],
    };

    const contentParagraph: JSONContent = {
      type: 'paragraph',
      content: [{ type: 'text', text: r.content }],
    };

    const spacer: JSONContent = { type: 'paragraph' };

    return [titleParagraph, contentParagraph, ...mediaNodes, spacer];
  });

  editor.chain().focus().insertContent(nodes).run();
}

export type SelectedMedia = {
  recordId: string;
  mediaIndex: number;
  mediaUrl: string;
  mediaType: 1 | 2 | 3; // 1:图片, 2:视频, 3:音频
  recordTitle: string;
  cover?: string; // 视频封面
};

// 兼容旧的图片选择类型
export type SelectedImage = SelectedMedia;

export function insertObservationImages(
  editor: Editor,
  selectedImages: SelectedImage[]
) {
  if (!(editor && selectedImages?.length)) {
    return;
  }

  const nodes: JSONContent[] = selectedImages.flatMap((img) => {
    // 只插入图片类型的媒体
    if (img.mediaType !== 1) {
      return [];
    }

    const imageNode: JSONContent = {
      type: 'image',
      attrs: { src: img.mediaUrl, alt: img.recordTitle },
    };

    const captionParagraph: JSONContent = {
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: `图：${img.recordTitle}`,
          marks: [{ type: 'italic' }],
        },
      ],
    };

    const spacer: JSONContent = { type: 'paragraph' };

    return [imageNode, captionParagraph, spacer];
  });

  editor.chain().focus().insertContent(nodes).run();
}

export function insertObservationMedias(
  editor: Editor,
  selectedMedias: SelectedMedia[]
) {
  if (!(editor && selectedMedias?.length)) {
    return;
  }

  const nodes: JSONContent[] = selectedMedias.flatMap((media) => {
    if (media.mediaType === 1) {
      // 图片
      const imageNode: JSONContent = {
        type: 'image',
        attrs: {
          src: media.mediaUrl,
          alt: media.recordTitle,
          title: media.recordTitle,
        },
      };

      const captionParagraph: JSONContent = {
        type: 'paragraph',
        content: [
          {
            type: 'text',
            text: `图：${media.recordTitle}`,
            marks: [{ type: 'italic' }],
          },
        ],
      };

      const spacer: JSONContent = { type: 'paragraph' };

      return [imageNode, captionParagraph, spacer];
    }

    if (media.mediaType === 2) {
      // 视频
      const videoNode: JSONContent = {
        type: 'video',
        attrs: {
          src: media.mediaUrl,
          poster:
            media.cover ||
            `${media.mediaUrl}?x-workflow-graph-name=video-thumbnail`,
          title: media.recordTitle,
        },
      };

      const captionParagraph: JSONContent = {
        type: 'paragraph',
        content: [
          {
            type: 'text',
            text: `视频：${media.recordTitle}`,
            marks: [{ type: 'italic' }],
          },
        ],
      };

      const spacer: JSONContent = { type: 'paragraph' };

      return [videoNode, captionParagraph, spacer];
    }

    // 音频暂时只插入文本描述
    const mediaParagraph: JSONContent = {
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: `【音频】${media.recordTitle}`,
          marks: [{ type: 'bold' }],
        },
      ],
    };

    const spacer: JSONContent = { type: 'paragraph' };

    return [mediaParagraph, spacer];
  });

  editor.chain().focus().insertContent(nodes).run();
}

const ObservationInsertNode = Extension.create({
  name: 'observationInsertNode',
});

export default ObservationInsertNode;
