import { useInfiniteQuery } from '@tanstack/react-query';
import type { Editor } from '@tiptap/core';
import {
  Button as AntdButton,
  Image as AntImage,
  Checkbox,
  ErrorBlock,
  Grid,
  InfiniteScroll,
  Loading,
  Popup,
  Segmented,
  Tag,
} from 'antd-mobile';
import clsx from 'clsx';
import {
  CheckSquare,
  Image as ImageIcon,
  Music,
  Play,
  Video,
  Volume2,
} from 'lucide-react';
import React from 'react';

import { getObservationList } from '@/api/pbl';
import type { ProjectContextStorage } from '@/components/tiptap-extensions/project-context-extension';
import {
  insertObservationImages,
  insertObservationMedias,
  insertObservationRecords,
  type ObservationRecord,
  type SelectedMedia,
} from './observation-insert-node-extension';

type Props = {
  editor: Editor | null;
  buttonText?: string;
  onClick?: () => void;
};

type SelectionMode = 'records' | 'images' | 'videos' | 'audios';

const pageSize = 10;

const ObservationInsertButton: React.FC<Props> = ({
  editor,
  buttonText,
  onClick,
}) => {
  const [visible, setVisible] = React.useState(false);
  const [selectedIds, setSelectedIds] = React.useState<string[]>([]);
  const [selectedMedias, setSelectedMedias] = React.useState<SelectedMedia[]>(
    []
  );
  const [mode, setMode] = React.useState<SelectionMode>('records');
  const [infiniteActive, setInfiniteActive] = React.useState(false);
  const scrollRef = React.useRef<HTMLDivElement>(null);

  // 从 editor storage 中获取 projectId
  const projectId = React.useMemo(() => {
    if (!editor) {
      return '';
    }
    try {
      return (
        (editor.storage as unknown as { projectContext: ProjectContextStorage })
          .projectContext?.projectId || ''
      );
    } catch {
      return '';
    }
  }, [editor]);

  // 获取观察记录数据（分页）
  const {
    data: infiniteObservationData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
    refetch,
  } = useInfiniteQuery({
    queryKey: ['observationList', projectId],
    queryFn: async ({ pageParam = 1 }) => {
      if (!projectId) {
        return { data: [], page: pageParam, hasMore: false };
      }

      const response = await getObservationList({
        projectId,
        page: pageParam,
        perPage: pageSize,
      });
      // @ts-expect-error
      const records = response?.list || [];

      return {
        data: records,
        page: pageParam,
        hasMore: records.length === pageSize,
      };
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      // 如果 hasMore 为 true，则返回下一页的页码
      if (lastPage?.hasMore) {
        return lastPage.page + 1;
      }
      // 否则，没有下一页
      return;
    },
    enabled: !!projectId && visible,
    refetchOnWindowFocus: false,
    // 防止自动获取下一页
    refetchOnMount: false,
    refetchOnReconnect: false,
  });

  // 从 infiniteObservationData 中提取记录数据
  const observationRecords = infiniteObservationData?.pages
    ? infiniteObservationData.pages.flatMap((page) => {
        if (page) {
          return page.data || [];
        }
        return [];
      })
    : [];

  // 加载更多数据的处理函数
  const loadMore = async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  };

  // 首次打开不触发无限滚动，用户有滚动动作后再激活
  const handleScrollActivate: React.UIEventHandler<HTMLDivElement> = (e) => {
    if (infiniteActive) {
      return;
    }
    const el = e.currentTarget;
    if (el && el.scrollTop > 0) {
      setInfiniteActive(true);
    }
  };

  const toggleSelectRecord = (id: string) => {
    setSelectedIds((prev) =>
      prev.includes(id) ? prev.filter((x) => x !== id) : [...prev, id]
    );
  };

  const toggleSelectMedia = (
    recordId: string,
    mediaIndex: number,
    mediaUrl: string,
    mediaType: 1 | 2 | 3,
    recordTitle: string,
    cover?: string
  ) => {
    setSelectedMedias((prev) => {
      const exists = prev.some(
        (media) =>
          media.recordId === recordId && media.mediaIndex === mediaIndex
      );
      if (exists) {
        return prev.filter(
          (media) =>
            !(media.recordId === recordId && media.mediaIndex === mediaIndex)
        );
      }
      return [
        ...prev,
        { recordId, mediaIndex, mediaUrl, mediaType, recordTitle, cover },
      ];
    });
  };

  const clearSelection = () => {
    setSelectedIds([]);
    setSelectedMedias([]);
  };

  const handleConfirm = () => {
    if (!editor) {
      return;
    }

    if (mode === 'records') {
      const chosen: ObservationRecord[] = observationRecords.filter((r) =>
        selectedIds.includes(r.observationId)
      );
      insertObservationRecords(editor, chosen);
    } else {
      // 根据模式过滤媒体类型
      let filteredMedias = selectedMedias;
      if (mode === 'images') {
        filteredMedias = selectedMedias.filter((m) => m.mediaType === 1);
      } else if (mode === 'videos') {
        filteredMedias = selectedMedias.filter((m) => m.mediaType === 2);
      } else if (mode === 'audios') {
        filteredMedias = selectedMedias.filter((m) => m.mediaType === 3);
      }

      if (mode === 'images') {
        insertObservationImages(editor, filteredMedias);
      } else {
        insertObservationMedias(editor, filteredMedias);
      }
    }

    setVisible(false);
    clearSelection();
  };

  const handleOpen = () => {
    onClick?.();
    setVisible(true);
    setInfiniteActive(false);
    // 打开时将列表滚动到顶部，避免哨兵初始就在视口
    if (scrollRef.current) {
      try {
        scrollRef.current.scrollTo({ top: 0 });
      } catch {
        scrollRef.current.scrollTop = 0;
      }
    }
    // 如果有 projectId 但没有数据，触发重新获取
    if (projectId && !infiniteObservationData && !isLoading) {
      refetch();
    }
  };

  // 根据模式获取当前显示的媒体
  const getDisplayMedias = (record: ObservationRecord) => {
    if (mode === 'records') {
      return record.medias || [];
    }
    if (mode === 'images') {
      return record.medias?.filter((m) => m.type === 1) || [];
    }
    if (mode === 'videos') {
      return record.medias?.filter((m) => m.type === 2) || [];
    }
    if (mode === 'audios') {
      return record.medias?.filter((m) => m.type === 3) || [];
    }
    return [];
  };

  // 渲染媒体项
  const renderMediaItem = (
    media: {
      type: 1 | 2 | 3;
      url: string;
      cover?: string;
      duration?: number;
      mediaUrl: string;
    },
    index: number,
    record: ObservationRecord
  ) => {
    const recordId = record.observationId || record.id;
    if (!(recordId && media.url)) {
      return null;
    }
    const recordTitle = record.title || '无标题';

    const isSelected = selectedMedias.some(
      (m) => m.recordId === recordId && m.mediaIndex === index
    );

    const handleMediaClick = () => {
      toggleSelectMedia(
        recordId,
        index,
        media.url,
        media.type,
        recordTitle,
        media.cover
      );
    };

    if (media.type === 1) {
      // 图片
      return (
        <Grid.Item key={`${recordId}-media-${index}`}>
          <div className="relative">
            <AntImage
              className={clsx(
                'aspect-square w-full rounded-md object-cover transition-all',
                isSelected && 'ring-2 ring-blue-500 ring-offset-2'
              )}
              fit="cover"
              onClick={handleMediaClick}
              src={media.url}
            />
            {isSelected && (
              <div className="absolute top-1 right-1 rounded-full bg-blue-500 p-1">
                <ImageIcon className="h-3 w-3 text-white" />
              </div>
            )}
          </div>
        </Grid.Item>
      );
    }

    if (media.type === 2) {
      // 视频
      return (
        <Grid.Item key={`${recordId}-media-${index}`}>
          <div className="relative">
            <button
              className={clsx(
                'flex aspect-square w-full items-center justify-center rounded-md bg-gray-100 transition-all',
                isSelected && 'ring-2 ring-blue-500 ring-offset-2'
              )}
              onClick={handleMediaClick}
              type="button"
            >
              <div className="relative h-full w-full">
                <AntImage
                  className="aspect-square w-full rounded-md object-cover"
                  fit="cover"
                  src={
                    media.cover ||
                    `${media.url}?x-workflow-graph-name=video-thumbnail`
                  }
                />
                <div className="absolute inset-0 flex items-center justify-center rounded-md bg-black bg-opacity-30">
                  <Play className="h-8 w-8 text-white" />
                </div>
              </div>
            </button>
            {isSelected && (
              <div className="absolute top-1 right-1 rounded-full bg-blue-500 p-1">
                <Video className="h-3 w-3 text-white" />
              </div>
            )}
          </div>
        </Grid.Item>
      );
    }

    if (media.type === 3) {
      // 音频
      return (
        <Grid.Item key={`${recordId}-media-${index}`}>
          <div className="relative">
            <button
              className={clsx(
                'flex aspect-square w-full items-center justify-center rounded-md bg-gray-100 transition-all',
                isSelected && 'ring-2 ring-blue-500 ring-offset-2'
              )}
              onClick={handleMediaClick}
              type="button"
            >
              <div className="flex flex-col items-center">
                <Music className="mb-2 h-8 w-8 text-gray-400" />
                <span className="text-gray-500 text-xs">音频</span>
                {media.duration && (
                  <span className="text-gray-400 text-xs">
                    {Math.floor(media.duration / 60)}:
                    {(media.duration % 60).toString().padStart(2, '0')}
                  </span>
                )}
              </div>
            </button>
            {isSelected && (
              <div className="absolute top-1 right-1 rounded-full bg-blue-500 p-1">
                <Volume2 className="h-3 w-3 text-white" />
              </div>
            )}
          </div>
        </Grid.Item>
      );
    }

    return null;
  };

  // 获取提示文本
  const getHintText = () => {
    switch (mode) {
      case 'records':
        return '支持多选。每条记录会插入标题、内容与媒体。';
      case 'images':
        return '选择要插入的图片。点击图片进行选择。';
      case 'videos':
        return '选择要插入的视频。点击视频进行选择。';
      case 'audios':
        return '选择要插入的音频。点击音频进行选择。';
      default:
        return '';
    }
  };

  return (
    <>
      <button
        className="flex flex-col items-center rounded-xl bg-blue-50 p-3 font-medium text-neutral-700 text-xs transition-colors hover:bg-blue-100 active:bg-blue-200"
        onClick={handleOpen}
        type="button"
      >
        <CheckSquare className="mb-1 h-5 w-5 text-blue-500" />
        {buttonText || null}
      </button>

      <Popup
        bodyClassName="p-0"
        onClose={() => {
          setVisible(false);
          clearSelection();
          setInfiniteActive(false);
        }}
        position="bottom"
        visible={visible}
      >
        <div className="flex h-[80vh] max-h-[80vh] w-full flex-col">
          <div className="sticky top-0 z-10 flex items-center justify-center border-zinc-200 border-b bg-white px-4 py-3 dark:border-zinc-800 dark:bg-zinc-900">
            <Segmented
              onChange={(value) => setMode(value as SelectionMode)}
              options={[
                { label: '观察记录', value: 'records' },
                { label: '图片', value: 'images' },
                { label: '视频', value: 'videos' },
                // { label: '音频', value: 'audios' },
              ]}
              value={mode}
            />
          </div>

          <div
            className="flex-1 overflow-y-auto px-4 py-3"
            onScroll={handleScrollActivate}
            ref={scrollRef}
          >
            <div className="mb-3 text-xs text-zinc-500 dark:text-zinc-400">
              {getHintText()}
            </div>

            {/* 加载状态 */}
            {isLoading && (
              <div className="flex items-center justify-center py-8">
                <Loading />
                <span className="ml-2 text-zinc-500">加载中...</span>
              </div>
            )}
            {/* 错误状态 */}
            {error && (
              <div className="py-4">
                <ErrorBlock
                  description={
                    error instanceof Error ? error.message : '请检查网络连接'
                  }
                  status="disconnected"
                  title="加载失败"
                />
                <div className="mt-4 text-center">
                  <AntdButton
                    onClick={() => {
                      refetch().catch(console.error);
                    }}
                    size="small"
                  >
                    重试
                  </AntdButton>
                </div>
              </div>
            )}

            {/* 空状态 */}
            {!(isLoading || error) && observationRecords.length === 0 && (
              <div className="py-8">
                <ErrorBlock
                  description="当前项目还没有观察记录"
                  status="empty"
                  title="暂无观察记录"
                />
              </div>
            )}

            {/* 数据列表 */}
            {!(isLoading || error) && observationRecords.length > 0 && (
              <div className="space-y-4">
                {observationRecords.map((rec) => {
                  const recordId = rec.observationId || rec.id;
                  if (!recordId) {
                    // 如果没有唯一ID，则跳过渲染此条记录
                    return null;
                  }

                  const checked =
                    mode === 'records' && selectedIds.includes(recordId);
                  const displayMedias = getDisplayMedias(rec);

                  // 如果不是记录模式且没有对应类型的媒体，则不显示
                  if (mode !== 'records' && displayMedias.length === 0) {
                    return null;
                  }

                  const title = rec.title || '无标题';
                  const content =
                    rec.content || rec.vediosummary || rec.description || '';

                  return (
                    <button
                      className={clsx(
                        'w-full rounded-lg border p-3 text-left transition-colors',
                        'border-zinc-200 hover:border-zinc-300 dark:border-zinc-800 dark:hover:border-zinc-700',
                        mode === 'records' && checked && 'border-blue-500'
                      )}
                      key={recordId}
                      onClick={() =>
                        mode === 'records' && toggleSelectRecord(recordId)
                      }
                      type="button"
                    >
                      <div className="mb-2 flex items-start justify-between gap-2">
                        <div className="flex min-w-0 flex-col items-start">
                          <div className="font-semibold text-base text-zinc-900 dark:text-zinc-100">
                            {title}
                          </div>
                          <div className="mt-1 line-clamp-2 text-left text-xs text-zinc-600 dark:text-zinc-300">
                            {content}
                          </div>
                          <div className="mt-2 flex flex-wrap items-center gap-2">
                            {(rec.tags || []).map((t) => (
                              <Tag
                                className="text-[10px]"
                                color="primary"
                                fill="outline"
                                key={`${recordId}-tag-${t}`}
                              >
                                {t.tagName}
                              </Tag>
                            ))}
                          </div>
                          <div className="mt-2 text-xs text-zinc-500 dark:text-zinc-400">
                            {rec.createTime
                              ? new Date(rec.createTime).toLocaleString()
                              : '未知时间'}{' '}
                            · {rec.createUser?.name || '未知'}
                          </div>
                        </div>
                        {mode === 'records' && (
                          <Checkbox
                            checked={checked}
                            onChange={(val) => {
                              if (val) {
                                !selectedIds.includes(recordId) &&
                                  toggleSelectRecord(recordId);
                              } else {
                                selectedIds.includes(recordId) &&
                                  toggleSelectRecord(recordId);
                              }
                            }}
                            onClick={(e) => e.stopPropagation()}
                          />
                        )}
                      </div>
                      {displayMedias.length > 0 && (
                        <div className="mt-2">
                          <Grid columns={3} gap={mode === 'records' ? 8 : 10}>
                            {mode === 'records'
                              ? displayMedias.slice(0, 6).map((media, idx) => {
                                  if (media.type === 1) {
                                    return (
                                      <Grid.Item
                                        key={`${recordId}-media-image-${media.url}-${idx}`}
                                      >
                                        <AntImage
                                          className="aspect-square w-full rounded-md object-cover"
                                          fit="cover"
                                          src={media.url}
                                        />
                                      </Grid.Item>
                                    );
                                  }
                                  if (media.type === 2) {
                                    return (
                                      <Grid.Item
                                        key={`${recordId}-media-video-${media.url}-${idx}`}
                                      >
                                        <div className="flex aspect-square w-full items-center justify-center rounded-md bg-gray-100">
                                          <div className="relative h-full w-full">
                                            <AntImage
                                              className="aspect-square w-full rounded-md object-cover"
                                              fit="cover"
                                              src={
                                                media.cover ||
                                                `${media.url}?x-workflow-graph-name=video-thumbnail`
                                              }
                                            />
                                            <div className="absolute inset-0 flex items-center justify-center rounded-md bg-black bg-opacity-30">
                                              <Play className="h-6 w-6 text-white" />
                                            </div>
                                          </div>
                                        </div>
                                      </Grid.Item>
                                    );
                                  }
                                  return (
                                    <Grid.Item
                                      key={`${recordId}-media-audio-${media.url}-${idx}`}
                                    >
                                      <div className="flex aspect-square w-full items-center justify-center rounded-md bg-gray-100">
                                        <Music className="h-6 w-6 text-gray-400" />
                                      </div>
                                    </Grid.Item>
                                  );
                                })
                              : displayMedias.map((media, idx) =>
                                  renderMediaItem(media, idx, rec)
                                )}
                          </Grid>
                        </div>
                      )}
                    </button>
                  );
                })}

                {/* 无限滚动组件 */}
                <InfiniteScroll
                  className="!py-2"
                  hasMore={!!hasNextPage && infiniteActive}
                  loadMore={loadMore}
                  threshold={100}
                >
                  {(() => {
                    if (isFetchingNextPage) {
                      return (
                        <div className="py-3 text-center">
                          <span className="text-gray-500 text-sm">
                            加载更多数据...
                          </span>
                        </div>
                      );
                    }
                    if (hasNextPage) {
                      return (
                        <div className="py-3 text-center">
                          <span className="text-gray-500 text-sm">
                            上拉加载更多
                          </span>
                        </div>
                      );
                    }
                    return (
                      <div className="py-3 text-center">
                        <span className="text-gray-500 text-sm">
                          没有更多数据了
                        </span>
                      </div>
                    );
                  })()}
                </InfiniteScroll>
              </div>
            )}
          </div>

          {/* bottom action bar */}
          <div className="flex-shrink-0 border-zinc-200 border-t p-4 dark:border-zinc-800">
            <div className="flex gap-3">
              <AntdButton
                className="flex-1"
                onClick={() => {
                  setVisible(false);
                  clearSelection();
                }}
              >
                取消
              </AntdButton>
              <AntdButton
                className="flex-1"
                color="primary"
                disabled={
                  mode === 'records'
                    ? selectedIds.length === 0
                    : selectedMedias.length === 0
                }
                onClick={handleConfirm}
              >
                确定(
                {mode === 'records'
                  ? selectedIds.length
                  : selectedMedias.length}
                )
              </AntdButton>
            </div>
          </div>
        </div>
      </Popup>
    </>
  );
};

export default ObservationInsertButton;
