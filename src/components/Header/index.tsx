'use client';

import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import type React from 'react';
import { useEffect, useState } from 'react';

import { getMobile, handelGoBack } from '@/utils/index';

const IMAGE_EXTENSIONS_REGEX = /\.(jpg|jpeg|png|gif|webp)$/i;

interface HeaderProps {
  type?: 'light' | 'dark';
  title?: string;
  children?: React.ReactNode;
  background?: string | 'transparent';
}

const Header: React.FC<HeaderProps> = ({
  type = 'dark',
  title = '',
  children,
  background = 'transparent',
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [safeAreaTop, setSafeAreaTop] = useState(44);

  useEffect(() => {
    const device = getMobile();

    const getSafeAreaTop = () => {
      if (typeof window !== 'undefined') {
        const computedStyle = getComputedStyle(document.documentElement);
        const safeAreaInsetTop = computedStyle.getPropertyValue(
          'env(safe-area-inset-top)'
        );

        if (safeAreaInsetTop && safeAreaInsetTop !== '0px') {
          const value = Number.parseInt(safeAreaInsetTop.replace('px', ''), 10);
          return Math.max(value, 20);
        }
      }

      if (device === 'ios') {
        return window.screen.height >= 812 ? 44 : 20;
      }
      if (device === 'android') {
        return 24;
      }

      return 20;
    };

    setSafeAreaTop(getSafeAreaTop() + 16);
  }, []);

  const handleBack = () => {
    handelGoBack(router);
  };

  const backIcon =
    type === 'light'
      ? '/images/common/goBackLight.png'
      : '/images/common/goBack.png';

  const getBackgroundStyle = () => {
    const baseStyle: React.CSSProperties = {
      paddingTop: `${safeAreaTop}px`,
    };

    if (background === 'transparent') {
      return baseStyle;
    }

    // 判断是否为渐变
    if (
      background &&
      (background.includes('linear-gradient') ||
        background.includes('radial-gradient'))
    ) {
      return {
        ...baseStyle,
        background,
        backgroundAttachment: 'fixed',
        backdropFilter: 'blur(10px)',
      };
    }

    // 判断是否为图片URL
    if (
      background &&
      (background.startsWith('http') ||
        background.startsWith('/') ||
        IMAGE_EXTENSIONS_REGEX.test(background))
    ) {
      return {
        ...baseStyle,
        backgroundImage: `url(${background})`,
        backgroundSize: 'cover',
        backgroundPosition: 'top center',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'fixed',
      };
    }

    // 其他情况作为纯色处理
    return {
      ...baseStyle,
      backgroundColor: background || 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(10px)',
    };
  };

  const shouldShowTitleBar = searchParams.get('hideTitleBar');
  console.log('shouldShowTitleBar', shouldShowTitleBar);
  if (shouldShowTitleBar !== '1') {
    console.log('不显示标题栏');
    return null;
  }
  return (
    <>
      <div
        className="fixed top-0 right-0 left-0 z-50 flex items-center justify-between px-4 pb-4"
        style={getBackgroundStyle()}
      >
        <button
          className="flex h-8 w-8 items-center justify-center"
          onClick={handleBack}
          type="button"
        >
          <Image
            alt="返回"
            className="h-[40px] w-[23px]"
            height={40}
            src={backIcon}
            width={23}
          />
        </button>

        {title && (
          <h1
            className={`-translate-x-1/2 absolute left-1/2 transform font-medium text-lg ${
              type === 'light' ? 'text-white' : 'text-gray-900'
            }`}
          >
            {title}
          </h1>
        )}
        {children && <div className="flex flex-1 justify-end">{children}</div>}
        {!children && <div className="h-8 w-8" />}
      </div>
      <div style={{ height: '100px' }} />
    </>
  );
};

export default Header;
