import { Highlight } from '@tiptap/extension-highlight';
import { Image } from '@tiptap/extension-image';
import { TaskItem, TaskList } from '@tiptap/extension-list';
import { Subscript } from '@tiptap/extension-subscript';
import { Superscript } from '@tiptap/extension-superscript';
import { TextAlign } from '@tiptap/extension-text-align';
import { Typography } from '@tiptap/extension-typography';
import { Selection } from '@tiptap/extensions';
import { EditorContent, EditorContext, useEditor } from '@tiptap/react';
// --- Tiptap Core Extensions ---
import { StarterKit } from '@tiptap/starter-kit';
import React from 'react';
import { HorizontalRule } from '@/components/tiptap-node/horizontal-rule-node/horizontal-rule-node-extension';
// --- Tiptap Node ---
import { ImageUploadNode } from '@/components/tiptap-node/image-upload-node/image-upload-node-extension';
import { VideoNodeExtension } from '@/components/tiptap-node/video-node/video-node-extension';
import { VideoUploadNode } from '@/components/tiptap-node/video-upload-node/video-upload-node-extension';
// --- UI Primitives ---
import { Button } from '@/components/tiptap-ui-primitive/button';
import { Spacer } from '@/components/tiptap-ui-primitive/spacer';
import {
  Toolbar,
  ToolbarGroup,
  ToolbarSeparator,
} from '@/components/tiptap-ui-primitive/toolbar';
import '@/components/tiptap-node/blockquote-node/blockquote-node.scss';
import '@/components/tiptap-node/code-block-node/code-block-node.scss';
import '@/components/tiptap-node/horizontal-rule-node/horizontal-rule-node.scss';
import '@/components/tiptap-node/list-node/list-node.scss';
import '@/components/tiptap-node/image-node/image-node.scss';
import '@/components/tiptap-node/video-upload-node/video-upload-node.scss';
import '@/components/tiptap-node/heading-node/heading-node.scss';
import '@/components/tiptap-node/paragraph-node/paragraph-node.scss';
import type { Editor, JSONContent } from '@tiptap/core';

// --- antd-mobile ---
import { Popup } from 'antd-mobile';
// 使用 lucide-react 作为额外图标来源
import {
  CheckSquare,
  ChevronRight,
  Code,
  Edit3,
  Keyboard,
  List,
  ListOrdered,
  MessageSquareText,
  Minimize2,
  Plus,
  Quote,
  ScrollText,
  Sparkles,
} from 'lucide-react';
// --- Icons ---
import { ArrowLeftIcon } from '@/components/tiptap-icons/arrow-left-icon';
import { HighlighterIcon } from '@/components/tiptap-icons/highlighter-icon';
import { LinkIcon } from '@/components/tiptap-icons/link-icon';
// --- Custom Node (Alert) ---
import { AlertNode } from '@/components/tiptap-node/alert-block/alert-node-extension';
// --- Components ---
// import { ThemeToggle } from '@/components/tiptap-templates/simple/theme-toggle';
// import { AlertDropdownMenu } from '@/components/tiptap-ui/alert-dropdown-menu';
import { BlockquoteButton } from '@/components/tiptap-ui/blockquote-button';
import { CodeBlockButton } from '@/components/tiptap-ui/code-block-button';
import {
  ColorHighlightPopover,
  ColorHighlightPopoverButton,
  ColorHighlightPopoverContent,
} from '@/components/tiptap-ui/color-highlight-popover';
// --- Tiptap UI ---
import { HeadingDropdownMenu } from '@/components/tiptap-ui/heading-dropdown-menu';
import { ImageUploadButton } from '@/components/tiptap-ui/image-upload-button';
import {
  LinkButton,
  LinkContent,
  LinkPopover,
} from '@/components/tiptap-ui/link-popover';
import { ListDropdownMenu } from '@/components/tiptap-ui/list-dropdown-menu';
import { MarkButton } from '@/components/tiptap-ui/mark-button';
import { TextAlignButton } from '@/components/tiptap-ui/text-align-button';
import { UndoRedoButton } from '@/components/tiptap-ui/undo-redo-button';
import { VideoUploadButton } from '@/components/tiptap-ui/video-upload-button/video-upload-button';
import { useCursorVisibility } from '@/hooks/use-cursor-visibility';
// --- Hooks ---
import { useIsMobile } from '@/hooks/use-mobile';
import { useScrolling } from '@/hooks/use-scrolling';
import { useWindowSize } from '@/hooks/use-window-size';
import {
  getDocumentSummary,
  tiptapToMarkdown,
  tiptapToMarkdownWithPreciseCursor,
} from '@/lib/tiptap-markdown-converter';
import {
  MAX_IMAGE_SIZE,
  MAX_VIDEO_SIZE,
  uploadTiptapImage,
  uploadTiptapVideo,
} from '@/lib/tiptap-media-upload';

// --- Styles ---
import './simple-editor.scss';
import './_variables.scss';
import './_keyframe-animations.scss';

import clsx from 'clsx';
import { aiGenerateContent } from '@/api/pbl';
import {
  ProjectContextExtension,
  type ProjectContextStorage,
} from '@/components/tiptap-extensions/project-context-extension';
import { MaterialSelectButton } from '@/components/tiptap-node/material-select-node';
import {
  ObservationInsertButton,
  ObservationInsertNode,
} from '@/components/tiptap-node/observation-insert-node';
import { PhaseSummaryInsertButton } from '@/components/tiptap-node/phase-summary-insert-node';
import { simulateAICommand } from '@/lib/ai-service';

const MainToolbarContent = ({
  onHighlighterClick,
  onLinkClick,
  onHideKeyboard,
  isMobile,
  editor,
  onOpenActions,
  onOpenAIMenu,
  aiLoading,
}: {
  onHighlighterClick: () => void;
  onLinkClick: () => void;
  onHideKeyboard: () => void;
  isMobile: boolean;
  editor: import('@tiptap/core').Editor | null;
  onOpenActions: () => void;
  onOpenAIMenu: () => void;
  aiLoading: boolean;
}) => {
  const isFocused = editor?.isFocused ?? false;

  return (
    <>
      <Spacer />
      <ToolbarGroup>
        <UndoRedoButton action="undo" />
        <UndoRedoButton action="redo" />
      </ToolbarGroup>

      {/* 仅在移动端且编辑器聚焦时显示“收起键盘”按钮 */}
      {isMobile && isFocused && (
        <>
          <ToolbarSeparator />
          <ToolbarGroup>
            <Button
              aria-label="收起键盘"
              data-style="ghost"
              onClick={onHideKeyboard}
              title="收起键盘"
            >
              <Keyboard className="tiptap-button-icon" />
            </Button>
          </ToolbarGroup>
        </>
      )}
      <ToolbarGroup>
        <Button
          aria-label="更多操作"
          data-style="ghost"
          onClick={onOpenActions}
          title="更多操作"
        >
          <Plus className="tiptap-button-icon" />
        </Button>
        {/* AI 工具按钮 */}
        <Button
          aria-label="AI 工具"
          className={clsx(aiLoading && 'animate-pulse')}
          data-style="ghost"
          onClick={onOpenAIMenu}
          title="AI 工具"
        >
          <Sparkles
            className={clsx(
              'tiptap-button-icon',
              aiLoading ? '!text-blue-500' : '!text-fuchsia-700'
            )}
          />
        </Button>
      </ToolbarGroup>

      <ToolbarSeparator />

      <ToolbarGroup>
        <HeadingDropdownMenu levels={[1, 2, 3, 4]} portal={isMobile} />
        <ListDropdownMenu
          portal={isMobile}
          types={['bulletList', 'orderedList', 'taskList']}
        />
        <BlockquoteButton />
        <CodeBlockButton />
      </ToolbarGroup>

      <ToolbarSeparator />

      <ToolbarGroup>
        <MarkButton type="bold" />
        <MarkButton type="italic" />
        <MarkButton type="strike" />
        <MarkButton type="code" />
        <MarkButton type="underline" />
        {isMobile ? (
          <ColorHighlightPopoverButton onClick={onHighlighterClick} />
        ) : (
          <ColorHighlightPopover />
        )}
        {isMobile ? <LinkButton onClick={onLinkClick} /> : <LinkPopover />}
      </ToolbarGroup>

      <ToolbarSeparator />

      <ToolbarGroup>
        <MarkButton type="superscript" />
        <MarkButton type="subscript" />
      </ToolbarGroup>

      <ToolbarSeparator />

      <ToolbarGroup>
        <TextAlignButton align="left" />
        <TextAlignButton align="center" />
        <TextAlignButton align="right" />
        <TextAlignButton align="justify" />
      </ToolbarGroup>

      <ToolbarSeparator />

      <ToolbarGroup>
        <ImageUploadButton text="图片" />
        <VideoUploadButton text="视频" />
      </ToolbarGroup>

      <Spacer />

      {isMobile && <ToolbarSeparator />}

      {/* <ToolbarGroup>
        <ThemeToggle />
      </ToolbarGroup> */}

      {/* <ToolbarSeparator /> */}

      {/* Alert dropdown at the end */}
      {/* <ToolbarGroup>
        <AlertDropdownMenu editor={editor} />
      </ToolbarGroup> */}
    </>
  );
};

const MobileToolbarContent = ({
  type,
  onBack,
}: {
  type: 'highlighter' | 'link';
  onBack: () => void;
}) => (
  <>
    <ToolbarGroup>
      <Button data-style="ghost" onClick={onBack}>
        <ArrowLeftIcon className="tiptap-button-icon" />
        {type === 'highlighter' ? (
          <HighlighterIcon className="tiptap-button-icon" />
        ) : (
          <LinkIcon className="tiptap-button-icon" />
        )}
      </Button>
    </ToolbarGroup>

    <ToolbarSeparator />

    {type === 'highlighter' ? (
      <ColorHighlightPopoverContent />
    ) : (
      <LinkContent />
    )}
  </>
);

export function SimpleEditor({
  onChange,
  content,
  projectId,
}: {
  onChange: (content: JSONContent) => void;
  content: JSONContent;
  projectId: string;
}) {
  const isMobile = useIsMobile();
  const windowSize = useWindowSize();
  const [mobileView, setMobileView] = React.useState<
    'main' | 'highlighter' | 'link'
  >('main');
  const toolbarRef = React.useRef<HTMLDivElement>(null);

  // Actions Popup visible
  const [actionsVisible, setActionsVisible] = React.useState(false);

  // AI 菜单可见性（仅 UI）
  const [aiMenuVisible, setAiMenuVisible] = React.useState(false);

  // AI 操作状态
  const [aiLoading, setAiLoading] = React.useState(false);
  const [aiToneMenuVisible, setAiToneMenuVisible] = React.useState(false);

  // 先创建 editor，再声明依赖 editor 的方法，避免“used before declaration”报错
  const editor = useEditor({
    immediatelyRender: false,
    shouldRerenderOnTransaction: false,
    editorProps: {
      attributes: {
        autocomplete: 'off',
        autocorrect: 'off',
        autocapitalize: 'off',
        'aria-label': 'Main content area, start typing to enter text.',
        class: 'simple-editor',
      },
    },
    extensions: [
      StarterKit.configure({
        horizontalRule: false,
        link: {
          openOnClick: false,
          enableClickSelection: true,
        },
      }),
      HorizontalRule,
      TextAlign.configure({ types: ['heading', 'paragraph'] }),
      TaskList,
      TaskItem.configure({ nested: true }),
      Highlight.configure({ multicolor: true }),
      Image,
      Typography,
      Superscript,
      Subscript,
      Selection,
      ImageUploadNode.configure({
        accept: 'image/*',
        maxSize: MAX_IMAGE_SIZE,
        limit: 3,
        upload: async (
          file: File,
          onProgress?: (event: { progress: number }) => void,
          signal?: AbortSignal
        ) => {
          const result = await uploadTiptapImage(
            file,
            (progress) => onProgress?.({ progress }),
            signal
          );
          return result.url;
        },
        onError: (error) => console.error('Image upload failed:', error),
      }),
      VideoUploadNode.configure({
        accept: 'video/*',
        maxSize: MAX_VIDEO_SIZE,
        limit: 1,
        upload: async (
          file: File,
          onProgress?: (event: { progress: number }) => void,
          signal?: AbortSignal
        ) => {
          const result = await uploadTiptapVideo(
            file,
            (progress) => onProgress?.({ progress }),
            signal
          );
          return result.url;
        },
        onError: (error) => console.error('Video upload failed:', error),
      }),
      VideoNodeExtension,
      AlertNode, // register custom alert block
      ObservationInsertNode, // ensure custom node is registered
      ProjectContextExtension, // register project context extension
    ],
    content,
    onUpdate({ editor }) {
      console.log('🚀 ~ editor:', editor.getJSON());
      onChange(editor.getJSON());
    },
  });

  // 设置 projectId 到 editor storage
  React.useEffect(() => {
    if (editor && projectId) {
      const storage = editor.storage as unknown as {
        projectContext: ProjectContextStorage;
      };
      if (storage.projectContext) {
        storage.projectContext.projectId = projectId;
      }
    }
  }, [editor, projectId]);

  const handleHideKeyboard = React.useCallback(() => {
    // 使用 Tiptap API 取消焦点
    editor?.commands.blur();
    // 双重保险：让根 DOM 失焦，促使移动端键盘收起
    const el = editor?.view.dom as HTMLElement | undefined;
    el?.blur();
  }, [editor]);

  const isScrolling = useScrolling();
  const rect = useCursorVisibility({
    editor,
    overlayHeight: toolbarRef.current?.getBoundingClientRect().height ?? 0,
  });

  React.useEffect(() => {
    if (!isMobile && mobileView !== 'main') {
      setMobileView('main');
    }
  }, [isMobile, mobileView]);

  // AI 命令处理函数
  const handleAICommand = React.useCallback(
    async (command: string, options?: Record<string, unknown>) => {
      if (!editor || aiLoading) {
        return;
      }

      const { from, to, empty } = editor.state.selection;
      console.log('🚀 ~ editor.state.selection:', editor.state.selection);
      console.log('🚀 ~ from:', from);
      const selectedText = empty
        ? ''
        : editor.state.doc.textBetween(from, to, ' ');
      console.log('🚀 ~ selectedText:', selectedText);

      // 获取完整文档内容
      const fullDocumentJSON = editor.getJSON();
      const documentSummary = getDocumentSummary(fullDocumentJSON);

      // 根据命令类型决定是否使用带光标位置的 markdown
      let fullDocumentMarkdown: string;

      if (command === 'continue' && empty) {
        // 续写命令且没有选中文本时，使用带光标位置标记的 markdown
        const cursorPosition = from;
        fullDocumentMarkdown = tiptapToMarkdownWithPreciseCursor(
          fullDocumentJSON,
          cursorPosition,
          editor // 传入编辑器实例以使用临时标记方法
        );
        console.log(
          '📝 续写模式 - 带光标位置的 Markdown:',
          fullDocumentMarkdown
        );
      } else {
        // 其他情况使用常规 markdown
        fullDocumentMarkdown = tiptapToMarkdown(fullDocumentJSON);
        console.log('📄 常规模式 - 完整文档 Markdown:', fullDocumentMarkdown);
      }

      console.log('📊 文档统计:', documentSummary);
      console.log('🎯 光标位置:', from, '选中范围:', { from, to, empty });

      setAiLoading(true);
      setAiMenuVisible(false);
      setAiToneMenuVisible(false);

      try {
        const response = await simulateAICommand(command, selectedText, {
          ...options,
          fullDocument: fullDocumentMarkdown,
          documentContext: documentSummary,
          cursorPosition: empty ? from : undefined, // 传递光标位置信息
        });

        if (response.success) {
          if (command === 'continue') {
            // 续写：在光标位置插入
            editor.chain().focus().insertContent(response.content).run();
          } else if (
            command === 'summarize' ||
            command === 'polish' ||
            command === 'expand'
          ) {
            // 总结、润色、扩写：替换选中的文本
            if (empty) {
              editor.chain().focus().insertContent(response.content).run();
            } else {
              editor
                .chain()
                .focus()
                .insertContentAt({ from, to }, response.content)
                .run();
            }
          } else if (command === 'shorten' && !empty) {
            // 缩写：替换选中的文本
            editor
              .chain()
              .focus()
              .insertContentAt({ from, to }, response.content)
              .run();
          } else if (command === 'adjustTone' && !empty) {
            // 调整语气：替换选中的文本
            editor
              .chain()
              .focus()
              .insertContentAt({ from, to }, response.content)
              .run();
          }
        } else {
          console.error('AI Command failed:', response.error);
          // 可以添加错误提示
        }
      } catch (error) {
        console.error('AI Command error:', error);
      } finally {
        setAiLoading(false);
      }
    },
    [editor, aiLoading]
  );

  // 流式插入 AI 内容的函数
  const streamAIContent = React.useCallback(
    (aiResponse: string, startPosition: number) => {
      if (!(editor && aiResponse)) {
        return;
      }

      // 将响应按字符分割，但保持中文字符完整
      const chars = Array.from(aiResponse);
      const currentPosition = startPosition;

      // 逐字符插入，模拟流式效果
      chars.forEach((char, index) => {
        setTimeout(() => {
          if (editor.isDestroyed) {
            return; // 防止编辑器已销毁时继续执行
          }

          editor
            .chain()
            .focus()
            .insertContentAt(currentPosition + index, char)
            .run();
        }, index * 30); // 30ms 延迟模拟流式效果，可以调整速度
      });
    },
    [editor]
  );

  // 处理自定义提示词
  const handleCustomPrompt = React.useCallback(
    async (prompt: string) => {
      if (!editor || aiLoading || !prompt.trim()) {
        return;
      }

      // 获取完整文档内容作为上下文
      const fullDocumentJSON = editor.getJSON();
      const { from } = editor.state.selection;

      // 为自定义提示词也使用带光标位置的 markdown，让 AI 了解上下文
      const fullDocumentMarkdown = tiptapToMarkdownWithPreciseCursor(
        fullDocumentJSON,
        from,
        editor // 传入编辑器实例
      );

      console.log('🤖 自定义提示词:', prompt);
      console.log('📄 带光标位置的文档上下文:', fullDocumentMarkdown);
      console.log('🎯 当前光标位置:', from);

      setAiLoading(true);
      setAiMenuVisible(false);

      try {
        // 构建完整的查询内容,包含用户提示词和文档上下文
        const query = `用户请求：${prompt}

        文档上下文（当前光标位置在第${from}个字符处）：
        ${fullDocumentMarkdown}

        请根据用户的请求和文档上下文，生成合适的内容。请直接输出内容，不要包含额外的解释。`;

        const response = await aiGenerateContent({
          conversation_id: '',
          files: [],
          inputs: [],
          query,
          response_mode: 'blocking',
        });

        if (response?.answer) {
          // 使用流式插入 AI 生成的内容
          streamAIContent(response.answer, from);
        } else {
          console.error('Custom prompt failed: AI返回内容为空');
        }
      } catch (error) {
        console.error('Custom prompt error:', error);
      } finally {
        setAiLoading(false);
      }
    },
    [editor, aiLoading, streamAIContent]
  );

  return (
    <div className="simple-editor-wrapper">
      <EditorContext.Provider value={{ editor }}>
        <Toolbar
          ref={toolbarRef}
          style={{
            ...(isScrolling && isMobile
              ? { opacity: 0, transition: 'opacity 0.1s ease-in-out' }
              : {}),
            ...(isMobile
              ? {
                  bottom: `calc(100% - ${windowSize.height - rect.y}px)`,
                }
              : {}),
          }}
        >
          {mobileView === 'main' ? (
            <MainToolbarContent
              aiLoading={aiLoading}
              editor={editor}
              isMobile={isMobile}
              onHideKeyboard={handleHideKeyboard}
              onHighlighterClick={() => setMobileView('highlighter')}
              onLinkClick={() => setMobileView('link')}
              onOpenActions={() => setActionsVisible(true)}
              onOpenAIMenu={() => setAiMenuVisible(true)}
            />
          ) : (
            <MobileToolbarContent
              onBack={() => setMobileView('main')}
              type={mobileView === 'highlighter' ? 'highlighter' : 'link'}
            />
          )}
        </Toolbar>

        <EditorContent
          className="simple-editor-content"
          editor={editor}
          role="presentation"
        />

        {/* 底部操作面板 Popup —— 按图实现：含标题、分组、小方块按钮网格 */}
        <Popup
          bodyClassName={clsx('pb-safe')}
          onClose={() => setActionsVisible(false)}
          position="bottom"
          showCloseButton
          visible={actionsVisible}
        >
          <div className="px-4 pt-3 pb-8">
            {/* 顶部标题栏：左侧关闭图标由 showCloseButton 提供，这里仅展示标题 */}
            <div className="mb-2 flex items-center justify-center">
              <div className="font-semibold text-base text-neutral-900">
                添加
              </div>
            </div>

            {/* 分组：基础 */}
            <div className="mt-3 mb-2 font-medium text-neutral-500 text-xs">
              基础
            </div>
            <div className="grid grid-cols-5 gap-6 sm:gap-12">
              {/* H1-H5 */}
              {[
                {
                  label: 'H1',
                  run: () =>
                    editor?.chain().focus().setHeading({ level: 1 }).run(),
                },
                {
                  label: 'H2',
                  run: () =>
                    editor?.chain().focus().setHeading({ level: 2 }).run(),
                },
                {
                  label: 'H3',
                  run: () =>
                    editor?.chain().focus().setHeading({ level: 3 }).run(),
                },
                {
                  label: 'H4',
                  run: () =>
                    editor?.chain().focus().setHeading({ level: 4 }).run(),
                },
                {
                  label: 'H5',
                  run: () =>
                    editor?.chain().focus().setHeading({ level: 5 }).run(),
                },
              ].map((item) => (
                <button
                  className="h-12 w-12 rounded-xl bg-neutral-100 font-semibold text-neutral-800 text-sm transition-colors hover:bg-neutral-200 active:bg-neutral-300"
                  key={String(item.label)}
                  onClick={() => {
                    item.run();
                    setActionsVisible(false);
                  }}
                  type="button"
                >
                  {item.label}
                </button>
              ))}

              {/* 有序/无序/任务/代码/引用 */}
              {[
                {
                  label: <ListOrdered className="mx-auto h-5 w-5" />,
                  run: () => editor?.chain().focus().toggleOrderedList().run(),
                  key: 'ol',
                },
                {
                  label: <List className="mx-auto h-5 w-5" />,
                  run: () => editor?.chain().focus().toggleBulletList().run(),
                  key: 'ul',
                },
                {
                  label: <CheckSquare className="mx-auto h-5 w-5" />,
                  run: () => editor?.chain().focus().toggleTaskList().run(),
                  key: 'task',
                },
                {
                  label: <Code className="mx-auto h-5 w-5" />,
                  run: () => editor?.chain().focus().toggleCodeBlock().run(),
                  key: 'code',
                },
                {
                  label: <Quote className="mx-auto h-5 w-5" />,
                  run: () => editor?.chain().focus().toggleBlockquote().run(),
                  key: 'quote',
                },
              ].map((item) => (
                <button
                  className="h-12 w-12 rounded-xl bg-neutral-100 text-neutral-800 transition-colors hover:bg-neutral-200 active:bg-neutral-300"
                  key={item.key}
                  onClick={() => {
                    item.run();
                    setActionsVisible(false);
                  }}
                  type="button"
                >
                  {item.label}
                </button>
              ))}
            </div>

            {/* 分组：常用 */}
            <div className="mt-6 mb-2 font-medium text-neutral-500 text-xs">
              常用
            </div>
            <div className="grid grid-cols-4 gap-3">
              {/* 图片 */}
              {/* <button
                className="flex flex-col items-center rounded-xl bg-yellow-50 p-3 font-medium text-neutral-700 text-xs transition-colors hover:bg-yellow-100 active:bg-yellow-200"
                onClick={() => {
                  setActionsVisible(false);
                  // 复用现有图片上传按钮逻辑：打开系统上传由 toolbar 按钮承担，这里示例插入 hr 代替
                  // 若后续需要可暴露 ImageUploadButton 的 imperative handle
                  editor?.chain().focus().setHorizontalRule().run();
                }}
                type="button"
              >
                <ImageIcon className="mb-1 h-5 w-5 text-yellow-500" />
                图片
              </button> */}

              {/* 表格（预留） */}
              {/* <button
                className="flex flex-col items-center rounded-xl bg-teal-50 p-3 font-medium text-neutral-700 text-xs transition-colors hover:bg-teal-100 active:bg-teal-200"
                onClick={() => {
                  setActionsVisible(false);
                  // TODO: 如果引入表格扩展，这里插入表格
                }}
                type="button"
              >
                <TableIcon className="mb-1 h-5 w-5 text-teal-500" />
                表格
              </button> */}

              {/* 文件（预留） */}
              {/* <button
                className="flex flex-col items-center rounded-xl bg-orange-50 p-3 font-medium text-neutral-700 text-xs transition-colors hover:bg-orange-100 active:bg-orange-200"
                onClick={() => {
                  setActionsVisible(false);
                  // TODO: 自定义文件节点
                }}
                type="button"
              >
                <FileIcon className="mb-1 h-5 w-5 text-orange-500" />
                文件
              </button> */}

              {/* 视频（占位：后续接入 Video Node/上传） */}
              {/* <button
                className="flex flex-col items-center rounded-xl bg-indigo-50 p-3 font-medium text-neutral-700 text-xs transition-colors hover:bg-indigo-100 active:bg-indigo-200"
                onClick={() => {
                  setActionsVisible(false);
                  // TODO: 接入 @tiptap/extension-video 或自定义视频节点
                  // 这里先做占位行为，保持交互一致性
                  editor?.chain().focus().setHorizontalRule().run();
                }}
                type="button"
              >
                <VideoIcon className="mb-1 h-5 w-5 text-indigo-500" />
                视频
              </button> */}

              {/* 高亮块：复用已有高亮Popover逻辑中的 mark */}
              {/* <button
                className="flex flex-col items-center rounded-xl bg-amber-50 p-3 text-xs font-medium text-neutral-700 hover:bg-amber-100 active:bg-amber-200 transition-colors"
                onClick={() => {
                  editor?.chain().focus().toggleHighlight().run()
                  setActionsVisible(false)
                }}
              >
                <HighlighterIcon2 className="mb-1 h-5 w-5 text-amber-500" />
                高亮块
              </button> */}

              <ObservationInsertButton
                buttonText="插入观察记录"
                editor={editor}
                onClick={() => setActionsVisible(false)}
              />

              <PhaseSummaryInsertButton
                buttonText="插入阶段性总结"
                editor={editor}
                onClick={() => setActionsVisible(false)}
              />
              <MaterialSelectButton
                buttonText="从素材库选择"
                editor={editor}
                onClick={() => setActionsVisible(false)}
              />
            </div>
          </div>
        </Popup>
        {/* AI 菜单 Popup */}
        <Popup
          bodyClassName={clsx('pb-safe')}
          onClose={() => {
            setAiMenuVisible(false);
            setAiToneMenuVisible(false);
          }}
          position="bottom"
          showCloseButton
          visible={aiMenuVisible}
        >
          {aiToneMenuVisible ? (
            <div className="px-3 pt-3 pb-8">
              <div className="mb-3 font-medium text-neutral-500">选择语气</div>
              <div className="grid grid-cols-2 gap-3">
                {[
                  { tone: 'formal', text: '正式', desc: '商务、学术场合' },
                  { tone: 'casual', text: '轻松', desc: '日常交流' },
                  { tone: 'professional', text: '专业', desc: '技术文档' },
                  { tone: 'friendly', text: '友好', desc: '亲切自然' },
                ].map((item) => (
                  <button
                    className={clsx(
                      'flex flex-col items-start rounded-xl border border-neutral-200 p-4 text-left',
                      'hover:border-blue-500 hover:bg-blue-50 active:bg-blue-100',
                      'dark:border-neutral-800 dark:hover:border-blue-400 dark:hover:bg-blue-900/20'
                    )}
                    key={item.tone}
                    onClick={() =>
                      handleAICommand('adjustTone', { tone: item.tone })
                    }
                    type="button"
                  >
                    <span className="font-medium text-neutral-900 dark:text-neutral-100">
                      {item.text}
                    </span>
                    <span className="text-neutral-500 text-sm dark:text-neutral-400">
                      {item.desc}
                    </span>
                  </button>
                ))}
              </div>
            </div>
          ) : (
            <div className="px-3 pt-3 pb-8">
              <div className="mb-3 font-medium text-neutral-500">
                AI 文档助手
              </div>
              <div className="divide-y divide-neutral-100 overflow-hidden rounded-xl bg-white shadow-sm ring-1 ring-black/5 dark:divide-neutral-800 dark:bg-neutral-900">
                {[
                  {
                    icon: Edit3,
                    text: '续写',
                    command: 'continue',
                    description: '继续当前内容的写作',
                  },
                  {
                    icon: ScrollText,
                    text: '总结',
                    command: 'summarize',
                    description: '生成内容摘要',
                  },
                  {
                    icon: Sparkles,
                    text: '润色',
                    command: 'polish',
                    description: '优化文字表达',
                  },
                  {
                    icon: List,
                    text: '扩写',
                    command: 'expand',
                    description: '丰富内容细节',
                  },
                  {
                    icon: Minimize2,
                    text: '缩写',
                    command: 'shorten',
                    description: '精简内容长度',
                  },
                  {
                    icon: MessageSquareText,
                    text: '调整语气',
                    action: () => setAiToneMenuVisible(true),
                    arrow: true,
                  },
                ].map((item) => (
                  <button
                    className={clsx(
                      'flex w-full items-center gap-3 px-4 py-3',
                      'bg-white hover:bg-neutral-50 active:bg-neutral-100 disabled:cursor-not-allowed disabled:opacity-50',
                      'dark:bg-neutral-900 dark:active:bg-neutral-800 dark:hover:bg-neutral-800'
                    )}
                    disabled={aiLoading}
                    key={item.text}
                    onClick={() => {
                      if (item.command) {
                        handleAICommand(item.command);
                      } else if (item.action) {
                        item.action();
                      }
                    }}
                    type="button"
                  >
                    <item.icon className="h-5 w-5 text-neutral-500" />
                    <div className="flex-1 text-left">
                      <div className=" text-neutral-900 dark:text-neutral-100">
                        {item.text}
                      </div>
                      {item.description && (
                        <div className="mt-0.5 text-neutral-500 text-xs dark:text-neutral-400">
                          {item.description}
                        </div>
                      )}
                    </div>
                    {item.arrow ? (
                      <ChevronRight className="h-4 w-4 text-neutral-400" />
                    ) : null}
                  </button>
                ))}
              </div>

              <div className="mt-4">
                <div className="flex gap-2">
                  <input
                    className="flex-1 rounded-xl border border-neutral-200 bg-neutral-50 px-3 py-2 text-neutral-900 placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-neutral-800 dark:bg-neutral-800 dark:text-neutral-100 dark:placeholder-neutral-500 dark:focus:ring-blue-400"
                    disabled={aiLoading}
                    placeholder="告诉我你想写点什么…"
                    type="text"
                  />
                  <button
                    className={clsx(
                      'rounded-xl bg-blue-500 px-4 py-2 font-medium text-white',
                      'hover:bg-blue-600 active:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50'
                    )}
                    disabled={aiLoading}
                    onClick={(e) => {
                      const input = e.currentTarget
                        .previousElementSibling as HTMLInputElement;
                      if (input.value.trim()) {
                        handleCustomPrompt(input.value);
                        input.value = '';
                      }
                    }}
                    type="button"
                  >
                    发送
                  </button>
                </div>
              </div>

              {aiLoading && (
                <div className="mt-4 flex items-center justify-center gap-2 text-neutral-500 text-sm">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent" />
                  AI 正在思考中…
                </div>
              )}
            </div>
          )}
        </Popup>
      </EditorContext.Provider>
    </div>
  );
}
