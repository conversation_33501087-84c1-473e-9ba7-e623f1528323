'use client';

import { format } from 'date-fns';
import {
  <PERSON>ertCircle,
  BookOpen,
  Edit3,
  Settings,
  UserPlus,
  X,
} from 'lucide-react';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

import {
  getAllianceDetails,
  getAllianceMembers,
  removeMember,
  updateAlliance,
} from '@/api/collaboration';
import { getCurrentSchoolInfo } from '@/api/common';
import { navigationToNativePage } from '@/utils';
export default function CommunityPage() {
  const router = useRouter();
  const id = useSearchParams().get('id') as string;
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  const [isEditingName, setIsEditingName] = useState(false);
  const [isManageMode, setIsManageMode] = useState(false); // 新增管理模式状态
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [selectedMemberId, setSelectedMemberId] = useState<number | null>(null);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [info, setInfo] = useState({
    id: id || null, // 默认ID为1
    name: '',
    logo: 'https://unicorn-media.ancda.com/production/app/logo/logo.png',
    type: 1, // 1: 共同体, 2: 集团园
    description: '该组织没有填写简介',
    createTime: '',
  });
  const [instId, setInstId] = useState<string | null>(null);
  // 页面加载状态
  const [isLoading, setIsLoading] = useState(true);

  // 高品质配色与前景色计算工具
  const PALETTE = [
    '#4E78FF', // 蓝
    '#6A5AE0', // 靛紫
    '#00B5D8', // 青
    '#34C759', // 绿
    '#FF8A34', // 橙
    '#FF5C8A', // 粉
    '#2EC4B6', // 青绿
    '#8B5CF6', // 紫
    '#F59E0B', // 琥珀
    '#10B981', // 翡翠
    '#3B82F6', // 天蓝
    '#E11D48', // 赤
    '#14B8A6', // 蓝绿
    '#A78BFA', // 淡紫
    '#F97316', // 橘
  ];

  const hashStringToIndex = (str: string, modulo: number) => {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      // 使用乘法并限制范围，避免按位运算触发 lint
      hash = Math.imul(hash, 31) + str.charCodeAt(i);
      // 保持整数范围
      if (hash > 2_147_483_647) hash -= 4_294_967_296;
      if (hash < -2_147_483_648) hash += 4_294_967_296;
    }
    return Math.abs(hash) % modulo;
  };

  const hexToRgb = (hex: string) => {
    const m = hex.replace('#', '');
    const bigint = Number.parseInt(
      m.length === 3
        ? m
            .split('')
            .map((c) => c + c)
            .join('')
        : m,
      16
    );
    // 避免位运算，使用除法与取整替代
    const r = Math.floor(bigint / 0x1_00_00) % 256;
    const g = Math.floor(bigint / 0x1_00) % 256;
    const b = bigint % 256;
    return { r, g, b };
  };

  // 使用 HSP 亮度模型选择更可读的前景色
  const getContrastingTextColor = (hex: string) => {
    const { r, g, b } = hexToRgb(hex);
    const brightness = Math.sqrt(0.299 * r * r + 0.587 * g * g + 0.114 * b * b);
    return brightness > 180 ? '#1F2937' /* gray-800 */ : '#FFFFFF';
  };

  const getColorForMember = (member: AllianceMember) => {
    const key = `${member.instId}-${member.instName || ''}`;
    const idx = hashStringToIndex(key, PALETTE.length);
    return PALETTE[idx];
  };

  type AllianceMember = {
    instId: number;
    instName: string;
    roleType: number; // 1 牵头园, 其他核心园
    status?: number; // 2 待确认
    joinTime?: number; // 加入时间时间戳（待确认不展示）
  };

  const [members, setMembers] = useState<AllianceMember[]>([]);
  const [tempDescription, setTempDescription] = useState(info.description);
  const [tempName, setTempName] = useState(info.name);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const nameInputRef = useRef<HTMLInputElement>(null);

  // 判断当前用户是否是牵头园（roleType为1的园所）
  const isLeaderInstitute = members.some(
    (member: AllianceMember) =>
      member.roleType === 1 &&
      instId != null &&
      member.instId.toString() === instId
  );
  const canInviteMembers = isLeaderInstitute; // 只有牵头园可以邀请新成员

  const handleEditDescription = () => {
    if (!canInviteMembers) {
      return;
    }
    setIsEditingDescription(true);
    setTempDescription(info.description);
  };

  const handleEditName = () => {
    if (!canInviteMembers) {
      return;
    }
    setIsEditingName(true);
    setTempName(info.name);
  };

  const handleSaveDescription = async () => {
    if (tempDescription.trim()) {
      const updatedInfo = {
        ...info,
        description: tempDescription.trim(),
      };
      await updateAlliance(id, updatedInfo);
      setInfo(updatedInfo);
      setIsEditingDescription(false);
    }
  };

  const handleSaveName = async () => {
    if (tempName.trim()) {
      const updatedInfo = {
        ...info,
        name: tempName.trim(),
      };
      await updateAlliance(id, updatedInfo);
      setInfo(updatedInfo);
      setIsEditingName(false);
    }
  };

  const handleCancelDescription = () => {
    setIsEditingDescription(false);
    setTempDescription(info.description);
  };

  const handleCancelName = () => {
    setIsEditingName(false);
    setTempName(info.name);
  };

  // 处理移出成员
  const handleRemoveClick = (memberId: number) => {
    setSelectedMemberId(memberId);
    setShowConfirmDialog(true);
  };

  const handleConfirmRemove = async () => {
    if (selectedMemberId) {
      await removeMember(id, selectedMemberId);
      setMembers(
        members.filter((member) => member.instId !== selectedMemberId)
      );
      setShowConfirmDialog(false);
      setSelectedMemberId(null);

      // 显示成功消息
      setShowSuccessMessage(true);
      setTimeout(() => {
        setShowSuccessMessage(false);
      }, 3000);
    }
  };

  const handleCancelRemove = () => {
    setShowConfirmDialog(false);
    setSelectedMemberId(null);
  };

  const selectedMember = members.find((m) => m.instId === selectedMemberId);

  // 切换管理模式
  const toggleManageMode = () => {
    // 只有牵头园才能进入管理模式
    if (isLeaderInstitute) {
      setIsManageMode(!isManageMode);
    }
  };

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTempDescription(e.target.value);
    // 自动调整高度
    e.target.style.height = 'auto';
    e.target.style.height = `${e.target.scrollHeight}px`;
  };
  const fetchAlliancesDetails = async () => {
    const res = await getAllianceDetails(id);
    // 兼容返回为 AxiosResponse 或 直接数据 两种形式
    const data = (res as any)?.data ?? (res as any);
    setInfo(data);
    setTempDescription(data.description);
    setTempName(data.name);
  };
  const fetchMembers = async () => {
    if (id) {
      const resInst = (await getCurrentSchoolInfo()) as {
        instId: string | number;
      };
      setInstId(String(resInst.instId));
      try {
        const res = (await getAllianceMembers(id)) as {
          list?: AllianceMember[];
        };
        // 先按 roleType 升序，其次将 status === 2 的成员排在最后
        const sorted = (res?.list || [])
          .slice()
          .sort((a: AllianceMember, b: AllianceMember) => {
            const roleDiff = (a.roleType || 0) - (b.roleType || 0);
            if (roleDiff !== 0) {
              return roleDiff;
            }
            const aPending = a.status === 2 ? 1 : 0;
            const bPending = b.status === 2 ? 1 : 0;
            return aPending - bPending;
          });
        setMembers(sorted);
      } catch (error) {
        console.error('获取成员列表失败:', error);
        setMembers([]);
      }
    }
  };
  const initData = async () => {
    setIsLoading(true);
    try {
      await fetchAlliancesDetails();
      await fetchMembers();
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    if (isEditingDescription && textareaRef.current) {
      textareaRef.current.focus();
      // 自动调整高度
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [isEditingDescription]);

  useEffect(() => {
    if (isEditingName && nameInputRef.current) {
      nameInputRef.current.focus();
      nameInputRef.current.select();
    }
  }, [isEditingName]);

  useEffect(() => {
    const originalTitle = document.title;
    document.title = '协作组织';
    initData();
    return () => {
      document.title = originalTitle;
    };
  }, []);
  return (
    <div className="min-h-screen bg-gray-50 pb-36">
      {/* 全局加载遮罩 */}
      {isLoading && (
        <div className="fixed inset-0 z-[100] flex items-center justify-center bg-white/70 backdrop-blur-sm">
          <div className="flex flex-col items-center">
            {/* spinner */}
            <div className="h-10 w-10 animate-spin rounded-full border-2 border-[#4E78FF] border-t-transparent" />
            <span className="mt-3 text-gray-600 text-sm">加载中...</span>
          </div>
        </div>
      )}
      {/* 机构信息卡片 */}
      <div className="p-4">
        <div className="rounded-2xl bg-[#4E78FF0D] p-5">
          <div className="flex items-start">
            <div className="mr-4 rounded-2xl">
              <Image
                alt="logo"
                className="size-12 rounded-lg object-cover"
                height={50}
                src={
                  info.logo ||
                  'https://unicorn-media.ancda.com/production/app/logo/logo.png'
                }
                width={50}
              />
            </div>
            <div className="flex-1">
              {/* 名称编辑区域 */}
              {isEditingName && canInviteMembers ? (
                <div className="relative mb-3">
                  <div
                    className={
                      'overflow-hidden rounded-xl bg-gradient-to-br from-blue-50/50 via-white to-indigo-50/50 p-1 shadow-blue-100/20 shadow-lg transition-all duration-300'
                    }
                  >
                    <div className="rounded-lg bg-white p-3">
                      <input
                        className="w-full bg-transparent font-medium text-gray-900 text-lg outline-none placeholder:text-gray-400"
                        maxLength={20}
                        onChange={(e) => setTempName(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleSaveName();
                          } else if (e.key === 'Escape') {
                            handleCancelName();
                          }
                        }}
                        placeholder="请输入名称..."
                        ref={nameInputRef}
                        type="text"
                        value={tempName}
                      />

                      {/* 底部工具栏 */}
                      <div className="mt-2 flex items-center justify-between">
                        {/* 字数统计 - 带进度条 */}
                        <div className="flex items-center gap-2">
                          <div className="h-0.5 w-8 overflow-hidden rounded-full bg-gray-200">
                            <div
                              className="h-full bg-gradient-to-r from-[#4E78FF] to-[#7495FF] transition-all duration-300"
                              style={{
                                width: `${(tempName.length / 20) * 100}%`,
                              }}
                            />
                          </div>
                          <span className="text-gray-400 text-xs">
                            {tempName.length}/20
                          </span>
                        </div>

                        {/* 操作按钮 */}
                        <div className="flex items-center gap-2">
                          <button
                            className="rounded-lg px-3 py-1 font-medium text-gray-600 text-xs transition-all hover:bg-gray-100"
                            onClick={handleCancelName}
                            type="button"
                          >
                            取消
                          </button>
                          <button
                            className={`rounded-lg px-3 py-1 font-medium text-xs transition-all ${
                              tempName.trim() && tempName !== info.name
                                ? 'bg-gradient-to-r from-[#4E78FF] to-[#7495FF] text-white shadow-sm hover:shadow-md'
                                : 'cursor-not-allowed bg-gray-100 text-gray-400'
                            } `}
                            disabled={
                              !tempName.trim() || tempName === info.name
                            }
                            onClick={handleSaveName}
                            type="button"
                          >
                            保存
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 装饰性元素 */}
                  <div className="-left-0.5 -top-0.5 absolute size-2 animate-pulse rounded-full bg-[#4E78FF] blur-sm" />
                  <div className="-bottom-0.5 -right-0.5 absolute size-2 animate-pulse rounded-full bg-[#7495FF] blur-sm" />
                </div>
              ) : (
                <button
                  className="group mb-1 inline-flex cursor-pointer items-center gap-1.5 text-left font-medium text-gray-900 text-lg transition-colors hover:text-[#4E78FF]"
                  onClick={handleEditName}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleEditName();
                    }
                  }}
                  type="button"
                >
                  {info.name}
                  {canInviteMembers ? (
                    <Edit3 className="size-4 opacity-1" />
                  ) : null}
                </button>
              )}
              <p className="mb-3 text-gray-500 text-sm">
                {info.type === 1 ? '共同体' : '集团园'}
              </p>
            </div>
          </div>

          {/* 描述区域 - 优雅的编辑交互 */}
          <div className="mt-4">
            <div className="relative">
              {/* 描述内容容器 */}
              <div
                className={`relative overflow-hidden rounded-2xl transition-all duration-300 ${
                  isEditingDescription
                    ? 'bg-gradient-to-br from-[#4E78FF0D] via-white to-[#7495FF1A] p-1 shadow-[#4E78FF1A] shadow-lg'
                    : 'bg-gray-50/50'
                } `}
              >
                {isEditingDescription && canInviteMembers ? (
                  /* 编辑模式 */
                  <div className="rounded-xl bg-white p-4">
                    <textarea
                      className="block w-full resize-none bg-transparent text-gray-700 text-sm leading-relaxed outline-none placeholder:text-gray-400"
                      maxLength={200}
                      onChange={handleTextareaChange}
                      placeholder="请输入共同体描述..."
                      ref={textareaRef}
                      value={tempDescription}
                    />

                    {/* 底部工具栏 */}
                    <div className="mt-3 flex items-center justify-between border-gray-100 border-t pt-3">
                      {/* 字数统计 - 带进度条 */}
                      <div className="flex items-center gap-2">
                        <div className="h-1 w-20 overflow-hidden rounded-full bg-gray-200">
                          <div
                            className="h-full bg-gradient-to-r from-[#4E78FF] to-[#7495FF] transition-all duration-300"
                            style={{
                              width: `${(tempDescription.length / 200) * 100}%`,
                            }}
                          />
                        </div>
                        <span className="text-gray-400 text-xs">
                          {tempDescription.length}/200
                        </span>
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex items-center gap-2">
                        <button
                          className="rounded-lg px-3 py-1.5 font-medium text-gray-600 text-xs transition-all hover:bg-gray-100"
                          onClick={handleCancelDescription}
                          type="button"
                        >
                          取消
                        </button>
                        <button
                          className={`rounded-lg px-4 py-1.5 font-medium text-xs transition-all ${
                            tempDescription.trim() &&
                            tempDescription !== info.description
                              ? 'bg-gradient-to-r from-[#4E78FF] to-[#7495FF] text-white shadow-sm hover:shadow-md'
                              : 'cursor-not-allowed bg-gray-100 text-gray-400'
                          } `}
                          disabled={
                            !tempDescription.trim() ||
                            tempDescription === info.description
                          }
                          onClick={handleSaveDescription}
                          type="button"
                        >
                          保存
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  /* 查看模式 */
                  <button
                    className="group relative w-full cursor-pointer rounded-2xl p-4 text-left transition-all hover:bg-gray-50/80"
                    onClick={handleEditDescription}
                    type="button"
                  >
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {info.description}
                      {canInviteMembers ? <Edit3 className="size-3.5" /> : null}
                    </p>
                  </button>
                )}
              </div>

              {/* 装饰性元素 */}
              {isEditingDescription && (
                <>
                  <div className="-left-1 -top-1 absolute size-3 animate-pulse rounded-full bg-[#4E78FF] blur-md" />
                  <div className="-bottom-1 -right-1 absolute size-3 animate-pulse rounded-full bg-[#7495FF] blur-md" />
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 成员列表头部 */}
      <div className="mb-4 px-4">
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-base text-gray-900">成员列表</h3>
          {isLeaderInstitute && (
            <button
              className="flex items-center text-[#4E78FF] text-sm transition-colors hover:text-[#7495FF]"
              onClick={toggleManageMode}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  toggleManageMode();
                }
              }}
              type="button"
            >
              {isManageMode ? (
                <>
                  <X className="mr-1 size-4" />
                  取消
                </>
              ) : (
                <>
                  <Settings className="mr-1 size-4" />
                  管理
                </>
              )}
            </button>
          )}
        </div>
      </div>

      {/* 成员列表 */}
      <div className="px-4 pb-6">
        <div className="space-y-3">
          {members.map((member: AllianceMember) => (
            <div
              className="rounded-xl bg-white p-4 shadow-sm"
              key={member.instId}
            >
              {/* 行：左侧头像+文本，右侧操作按钮 */}
              <div className="flex items-start">
                {/* 头像占位 */}
                {(() => {
                  const bg =
                    member.status === 2 ? null : getColorForMember(member);
                  const fg = bg ? getContrastingTextColor(bg) : null;
                  return (
                    <div
                      className={`mr-3 flex size-12 shrink-0 items-center justify-center rounded-full ${
                        member.status === 2 ? 'bg-gray-200' : ''
                      }`}
                      style={bg ? { backgroundColor: bg } : undefined}
                    >
                      <span
                        className={`font-medium text-sm ${
                          member.status === 2 ? 'text-gray-400' : ''
                        }`}
                        style={fg ? { color: fg } : undefined}
                      >
                        {member.instName?.charAt(0)}
                      </span>
                    </div>
                  );
                })()}

                {/* 成员信息 */}
                <div className="min-w-0 flex-1">
                  {/* 顶部：名称可换行 + 右侧操作按钮同一行 */}
                  <div className="flex items-start justify-between gap-2">
                    <div className="min-w-0">
                      <h3
                        className={`break-words font-medium text-base ${
                          member.status === 2
                            ? 'text-gray-400'
                            : 'text-gray-900'
                        }`}
                      >
                        {member.instName}
                      </h3>
                      {member.status === 2 && (
                        <span className="mt-1 inline-block rounded-full bg-[#4E78FF1A] px-2 py-0.5 font-medium text-[#4E78FF] text-xs">
                          待确认
                        </span>
                      )}
                    </div>

                    {/* 操作按钮 - 与名称同一行；仅管理模式且非牵头园显示 */}
                    {isManageMode &&
                      member.roleType !== 1 &&
                      member.status !== 2 && (
                        <button
                          className="ml-3 shrink-0 rounded-lg border border-red-200 px-4 py-1.5 text-red-500 text-sm transition-colors hover:bg-red-50"
                          onClick={() => handleRemoveClick(member.instId)}
                          type="button"
                        >
                          移出
                        </button>
                      )}
                  </div>

                  {/* 次行：标签与时间同一行不换行 */}
                  <div className="mt-2 text-gray-500 text-sm">
                    <div className="flex items-center gap-4 whitespace-nowrap">
                      {member.roleType === 1 ? (
                        <span className="text-[#4E78FF]">牵头园</span>
                      ) : (
                        <span>核心园</span>
                      )}
                      {member.status !== 2 && member.joinTime ? (
                        <span>
                          加入时间：
                          {format(
                            new Date(member.joinTime * 1000),
                            'yyyy-MM-dd'
                          )}
                        </span>
                      ) : null}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 管理模式下的提示信息 */}
      {isManageMode && (
        <div className="px-4 pb-6">
          <p className="text-center text-gray-400 text-xs">
            牵头园无法被移出，其他成员可以被移出
          </p>
        </div>
      )}

      {/* 确认对话框 */}
      {showConfirmDialog && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 px-4">
          <div className="w-full max-w-sm rounded-2xl bg-white p-6">
            <div className="mb-4 flex items-start">
              <div className="flex size-12 shrink-0 items-center justify-center rounded-full bg-[#4E78FF1A]">
                <AlertCircle className="size-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <h3 className="mb-1 font-medium text-gray-900 text-lg">
                  确认移出成员
                </h3>
                <p className="text-gray-600 text-sm">
                  确定要将
                  <span className="font-medium">
                    {selectedMember?.instName}
                  </span>
                  移出吗？
                </p>
              </div>
            </div>
            <div className="flex space-x-3">
              <button
                className="flex-1 rounded-lg border border-gray-300 px-4 py-2 font-medium text-gray-700 transition-colors hover:bg-gray-50"
                onClick={handleCancelRemove}
                type="button"
              >
                取消
              </button>
              <button
                className="flex-1 rounded-lg bg-[#4E78FF] px-4 py-2 font-medium text-white transition-colors hover:bg-blue-600"
                onClick={handleConfirmRemove}
                type="button"
              >
                确认移出
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 成功提示消息 */}
      {showSuccessMessage && (
        <div className="-translate-x-1/2 fixed top-20 left-1/2 z-50 flex items-center space-x-2 rounded-lg bg-green-500 px-6 py-3 text-white shadow-lg">
          <svg
            aria-labelledby="successTitle"
            className="size-5"
            fill="none"
            role="img"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <title id="successTitle">Success</title>
            <path
              d="M5 13l4 4L19 7"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
            />
          </svg>
          <span className="font-medium">成员已成功移出</span>
        </div>
      )}

      {/* 底部操作按钮区域 - 固定在底部 */}
      <div className="fixed inset-x-0 bottom-0 border-gray-100 border-t bg-white p-4 shadow-lg">
        <div className="mx-auto max-w-md space-y-2">
          {/* 进入资源库按钮 */}
          <button
            className="flex w-full items-center justify-center rounded-xl bg-gradient-to-r from-indigo-500 to-purple-500 px-4 py-3 font-medium text-white shadow-sm transition-all hover:shadow-md"
            onClick={() =>
              navigationToNativePage(
                `app://app/cloud/cloudFileBrowsing?spaceType=4&allianceId=${id}`
              )
            }
            type="button"
          >
            <BookOpen className="mr-2 size-5" />
            进入云盘
          </button>

          {/* 邀请新成员按钮 - 带权限判断 */}
          {canInviteMembers ? (
            <button
              className="flex w-full items-center justify-center rounded-xl bg-[#4E78FF] px-4 py-3 font-medium text-white transition-colors hover:bg-[#7495FF]"
              onClick={() =>
                router.push(
                  `/collaboration/community/invite?name=${info.name}&id=${id}`
                )
              }
              type="button"
            >
              <UserPlus className="mr-2 size-5" />
              邀请新成员
            </button>
          ) : null}
        </div>
      </div>
    </div>
  );
}
