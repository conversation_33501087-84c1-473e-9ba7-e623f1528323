'use client';

import { Toast } from 'antd-mobile';
import { Info, Loader2, Search, X } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

import { inviteMember, searchAlliances } from '@/api/collaboration';

export default function InviteMemberPage() {
  const searchParams = useSearchParams();
  const allianceId = searchParams.get('id') || '';
  const name = searchParams.get('name');
  const [searchValue, setSearchValue] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [searchError, setSearchError] = useState('');
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  const fetchInviteMember = async (inviteInstId: string) => {
    try {
      const response = await inviteMember(allianceId, {
        allianceId,
        inviteInstId,
      });
      if (response) {
        Toast.show({
          icon: 'success',
          content: '邀请发送成功',
        });
      }
    } catch (error) {
      Toast.show({
        icon: 'fail',
        content: '邀请发送失败',
      });
    }
  };

  // 仅在点击“搜索”时发起请求

  const handleSearch = () => {
    const keyword = searchValue.trim();
    if (keyword.length < 4) {
      setSearchResults([]);
      setHasSearched(false);
      setSearchError('');
      return;
    }
    setIsSearching(true);
    setSearchError('');
    setHasSearched(true);
    searchAlliances({ name: keyword })
      .then((res: any) => {
        setSearchResults(res?.list || []);
      })
      .catch((error) => {
        console.error('搜索失败:', error);
        setSearchError('搜索失败，请重试');
        setSearchResults([]);
      })
      .finally(() => {
        setIsSearching(false);
      });
  };

  // 处理输入变化（不自动搜索）
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setSearchValue(value);
  };

  // 清空搜索
  const clearSearch = () => {
    setSearchValue('');
    setSearchResults([]);
    setHasSearched(false);
    setSearchError('');
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
  };

  // 清理占位（无定时器）
  useEffect(() => {
    document.title = '邀请成员';
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  const handleInvite = (kindergarten: any) => {
    fetchInviteMember(kindergarten.instId);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 当前组织信息 */}
      <div className="mb-4 bg-white p-4">
        <p className="mb-1 text-gray-500 text-sm">当前组织</p>
        <p className="font-medium text-base text-gray-900">{name}</p>
      </div>

      {/* 搜索区域 */}
      <div className="mb-6 px-4">
        <div className="flex items-stretch gap-3">
          <div className="relative flex-1">
            <Search
              className="-translate-y-1/2 pointer-events-none absolute top-1/2 left-3 text-gray-400"
              size={18}
            />
            <input
              className="h-11 w-full rounded-xl border border-gray-200 bg-white pr-10 pl-11 text-sm transition-all placeholder:text-gray-400 focus:border-[#4E78FF] focus:shadow-sm focus:outline-none focus:ring-2 focus:ring-[#4E78FF1A]"
              onChange={handleInputChange}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              placeholder="输入不少于4个字的园所名称"
              type="text"
              value={searchValue}
            />
            {searchValue && (
              <button
                className="-translate-y-1/2 absolute top-1/2 right-3 rounded-full p-1 text-gray-400 transition-colors hover:bg-gray-100 hover:text-gray-600"
                onClick={clearSearch}
                type="button"
              >
                <X size={16} />
              </button>
            )}
          </div>
          <button
            className="inline-flex h-11 items-center justify-center rounded-xl bg-[#4E78FF] px-3 font-medium text-sm text-white transition-all hover:bg-[#7495FF] disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:bg-[#4E78FF]"
            disabled={isSearching || searchValue.trim().length < 4}
            onClick={handleSearch}
            type="button"
          >
            {isSearching ? (
              <div className="flex items-center gap-1.5">
                <Loader2 className="animate-spin" size={14} />
                <span>搜索</span>
              </div>
            ) : (
              <div className="flex items-center gap-1.5">
                <Search size={14} />
                <span>搜索</span>
              </div>
            )}
          </button>
        </div>

        {/* 搜索提示和错误信息 */}
        {searchValue.length > 0 && searchValue.length < 4 && (
          <p className="mt-2 px-1 text-amber-600 text-xs">
            请输入至少 4 个字符后点击右侧“搜索”
          </p>
        )}
        {searchError && (
          <p className="mt-2 px-1 text-red-600 text-xs">{searchError}</p>
        )}
      </div>

      {/* 提示信息和搜索结果 */}
      <div className="px-4">
        {!(hasSearched || isSearching) && (
          <div className="mb-6">
            <div className="flex rounded-xl bg-[#4E78FF0D] p-4">
              <div className="mr-3 shrink-0">
                <div className="flex size-8 items-center justify-center rounded-full bg-[#4E78FF1A]">
                  <Info className="size-5 text-[#4E78FF]" />
                </div>
              </div>
              <div className="flex-1">
                <p className="mb-1 font-medium text-gray-900 text-sm">
                  搜索提示
                </p>
                <p className="text-gray-600 text-sm leading-relaxed">
                  请在上方搜索框中输入园所名称进行搜索，然后点击&ldquo;发送邀请&rdquo;按钮邀请对方加入组织。
                </p>
              </div>
            </div>
          </div>
        )}

        {/* 搜索结果 */}
        {hasSearched && searchResults.length > 0 && (
          <div>
            <div className="mb-4 flex items-center justify-between">
              <h3 className="font-medium text-gray-700 text-sm">
                搜索结果 ({searchResults.length})
              </h3>
              <button
                className="text-[#4E78FF] text-xs hover:text-[#7495FF]"
                onClick={clearSearch}
                type="button"
              >
                清空搜索
              </button>
            </div>
            <div className="space-y-3">
              {searchResults.map((kindergarten, index) => (
                <div
                  className="group rounded-xl bg-white p-4 shadow-sm transition-all hover:shadow-md"
                  key={kindergarten.id || index}
                >
                  <div className="flex items-center justify-between">
                    <div className="min-w-0 flex-1">
                      <h4 className="font-medium text-base text-gray-900">
                        <span className="block">
                          {kindergarten.fullName || kindergarten.name}
                        </span>
                      </h4>
                    </div>
                    <button
                      className="ml-4 shrink-0 rounded-lg bg-[#4E78FF] px-4 py-2.5 font-medium text-sm text-white transition-all hover:bg-[#7495FF] hover:shadow-sm active:scale-95"
                      onClick={() => handleInvite(kindergarten)}
                      type="button"
                    >
                      发送邀请
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 无搜索结果 */}
        {hasSearched &&
          searchResults.length === 0 &&
          !isSearching &&
          !searchError && (
            <div className="py-12 text-center">
              <div className="mx-auto mb-4 flex size-16 items-center justify-center rounded-full bg-gray-100">
                <Search className="size-8 text-gray-400" />
              </div>
              <h3 className="mb-2 font-medium text-base text-gray-900">
                未找到相关园所
              </h3>
              <p className="mb-4 text-gray-500 text-sm">
                请尝试使用其他关键词搜索
              </p>
              <button
                className="text-[#4E78FF] text-sm hover:text-[#7495FF]"
                onClick={clearSearch}
                type="button"
              >
                重新搜索
              </button>
            </div>
          )}
      </div>
    </div>
  );
}
