'use client';

import { ChevronRight } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';

import { getAlliances } from '@/api/collaboration';

type Organization = {
  id: string | number;
  logo?: string;
  name: string;
  type: number; // 1: 共同体, 其他: 集团园
};

export default function CollaborationPage() {
  const [organizations, setAlliances] = useState<Organization[]>([]);

  const fetchAlliances = async () => {
    const alliances = await getAlliances();
    // 兼容不同返回结构：优先使用 alliances.list，否则直接当作数组
    const list = alliances as unknown as
      | { list?: Organization[] }
      | Organization[] as { list?: Organization[] } | Organization[];
    const normalized = Array.isArray(list) ? list : (list.list ?? []);
    setAlliances(normalized);
  };
  useEffect(() => {
    const originalTitle = document.title;
    document.title = '协作组织';

    return () => {
      document.title = originalTitle;
    };
  }, []);
  useEffect(() => {
    fetchAlliances();
  }, []);
  return (
    <div className="min-h-screen bg-gray-50 p-4">
      {/* 机构卡片列表 */}
      <div className="mx-auto max-w-md space-y-4">
        {organizations.map((org) => (
          <Link
            className="cursor-pointer overflow-hidden rounded-2xl bg-white shadow-sm transition-all duration-200 active:scale-[0.98]"
            href={`/collaboration/community?id=${org.id}`}
            key={org.id}
          >
            <div className="flex items-center p-2">
              {/* 图标容器 */}
              <div className={'mr-4 shrink-0 rounded-2xl p-1'}>
                <Image
                  alt="logo"
                  height={60}
                  src={
                    org.logo ||
                    'https://unicorn-media.ancda.com/production/app/logo/logo.png'
                  }
                  width={60}
                />
              </div>

              {/* 文本内容 */}
              <div className="flex-1">
                <h2 className="mb-2 font-medium text-base text-gray-900">
                  {org.name}
                </h2>
                <p className="text-gray-500 text-sm">
                  {org.type === 1 ? '共同体' : '集团园'}
                </p>
              </div>

              {/* 右箭头 */}
              <ChevronRight className="size-5 shrink-0 text-gray-400" />
            </div>
          </Link>
        ))}
      </div>

      {/* 底部间距 */}
      <div className="h-20" />
    </div>
  );
}
