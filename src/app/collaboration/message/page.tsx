'use client';

import { Bell, Info } from 'lucide-react';
import Head from 'next/head';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import {
  acceptInvitation,
  getAllianceStatus,
  rejectInvitation,
} from '@/api/collaboration';

export default function Home() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const id = searchParams.get('allianceId') as string;
  const [message, setMessage] = useState({
    allianceName: searchParams.get('allianceName') || '暂无内容',
    leadInstName: searchParams.get('leadInstName') || '暂无内容',
  });
  const [inviteStatus, setInviteStatus] = useState(0); // 是否可操作
  const fetchStatus = async () => {
    const res = await getAllianceStatus(id);
    setInviteStatus(res.status);
  };
  useEffect(() => {
    const originalTitle = document.title;
    document.title = '消息详情';
    return () => {
      document.title = originalTitle;
    };
  }, []);
  useEffect(() => {
    fetchStatus();
    const allianceName = searchParams.get('allianceName');
    if (allianceName) {
      setMessage((prev) => ({
        ...prev,
        allianceName,
      }));
    }
  }, [searchParams]);

  // 共同体邀请同意
  const handleAcceptInvitation = () => {
    if (id) {
      acceptInvitation(id, { allianceId: id }).then(() => {
        setInviteStatus(1);
        router.push('/collaboration');
      });
    }
  };
  // 共同体邀请拒绝
  const handleRejectInvitation = () => {
    if (id) {
      rejectInvitation(id, { allianceId: id }).then(() => {
        setInviteStatus(3);
      });
    }
  };
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <Head>
        <title>消息详情</title>
      </Head>

      {/* Main Content */}
      <main className="pt-1 pb-6">
        {/* Message Card */}
        <div className="mx-4 mt-4 overflow-hidden rounded-xl bg-white shadow-sm">
          {/* Message Header */}
          <div className="border-gray-100 border-b p-4">
            <div className="mb-2 flex items-center gap-3">
              <div className="rounded-lg bg-[#4E78FF1A] p-2">
                <Bell className="size-5 text-[#4E78FF]" />
              </div>
              <h2 className="font-semibold text-gray-900 text-lg">系统通知</h2>
            </div>

            {/* Message Meta  */}
            <div className="mt-3 flex flex-wrap gap-x-4 gap-y-2">
              <div className="flex items-center text-gray-500 text-sm">
                <Info className="mr-1 size-4" />
                <span>受邀消息提醒</span>
              </div>
            </div>
          </div>

          {/* Message Content */}
          <div className="relative p-4">
            <div className="whitespace-pre-line text-gray-700 leading-relaxed">
              尊敬的园长： <br />
              您好！ <br />
              我们是[{message.allianceName}
              ]的牵头园 [{message.leadInstName}
              ]。在此诚挚地邀请贵园加入我们，与我们携手同行。
              <br />
            </div>

            {/* 状态标签放在右上角 */}
            {(inviteStatus === 1 || inviteStatus === 3) && (
              <div className="absolute top-0 right-0">
                {inviteStatus === 1 ? (
                  <span className="inline-flex items-center rounded-tr-xl rounded-bl-lg bg-green-100 px-3 py-1 font-medium text-green-800 text-xs">
                    已同意
                  </span>
                ) : (
                  <span className="inline-flex items-center rounded-tr-xl rounded-bl-lg bg-red-100 px-3 py-1 font-medium text-red-800 text-xs">
                    已拒绝
                  </span>
                )}
              </div>
            )}
            {/* 邀请操作按钮 - 仅当 2 时显示 */}
            {inviteStatus === 2 && (
              <div className="mt-6 flex gap-3">
                <button
                  className="flex-1 rounded-lg bg-[#4E78FF] px-4 py-3 text-center font-medium text-sm text-white transition-colors hover:bg-[#7495FF] focus:outline-none focus:ring-2 focus:ring-[#4E78FF] focus:ring-offset-2"
                  onClick={handleAcceptInvitation}
                  type="button"
                >
                  同意邀请
                </button>
                <button
                  className="flex-1 rounded-lg border border-gray-300 bg-white px-4 py-3 text-center font-medium text-gray-700 text-sm transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                  onClick={handleRejectInvitation}
                  type="button"
                >
                  拒绝邀请
                </button>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
