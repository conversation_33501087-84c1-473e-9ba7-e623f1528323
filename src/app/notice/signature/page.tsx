'use client';

import { Button, SpinLoading } from 'antd-mobile';
import { format } from 'date-fns';
import React, { useEffect, useRef, useState } from 'react';
import SignatureCanvas from 'react-signature-canvas';

import { postMessage } from '@/utils';
import { generateString, getEnv, uploadObs } from '@/utils/obs';

export default function Signature() {
  const signatureRef = useRef<SignatureCanvas>(null);

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {}, []);

  const handleSave = () => {
    setIsLoading(true);
    if (signatureRef.current?.isEmpty()) {
      postMessage({ url: '' });
      setIsLoading(false);
      return;
    }
    const dataUrl = signatureRef.current
      ?.getTrimmedCanvas()
      .toDataURL('image/png');
    // 处理签名图片的数据 URL
    // postMessage({ base64: dataUrl });
    const env = getEnv();
    const date = format(new Date(), 'yyyy-MM-dd');
    const key = `${env}/signature/${date}/${generateString(8)}.png`;
    uploadObs(dataUrl, key, true)
      .then((url) => {
        postMessage({ url });
      })
      .catch((err) => {
        console.log('upload error', err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleClear = () => {
    signatureRef.current?.clear();
  };

  return (
    <div className="h-screen w-screen">
      {isLoading && (
        <div className="fixed inset-0 z-50 flex size-full items-center justify-center bg-black">
          <SpinLoading color="#FFCA2C" />
        </div>
      )}
      <SignatureCanvas
        canvasProps={{
          className: 'w-full h-full',
        }}
        penColor="black"
        ref={signatureRef}
      />
      <div className="absolute right-4 bottom-4 flex w-full flex-row justify-end">
        <div>
          <Button onClick={handleClear} shape="rounded">
            重新签名
          </Button>
        </div>
        <div className="ml-2">
          <Button color="primary" onClick={handleSave} shape="rounded">
            保存签名
          </Button>
        </div>
      </div>
    </div>
  );
}
