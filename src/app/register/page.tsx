'use client';

import { useQuery } from '@tanstack/react-query';
import {
  ActionSheet,
  Button,
  ErrorBlock,
  Form,
  Input,
  List,
  Loading,
  Popup,
  SearchBar,
  TextArea,
  Toast,
} from 'antd-mobile';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import { useImmer } from 'use-immer';

import {
  getEnterprises,
  getExistence,
  getSalesman,
  postInstitutions,
} from '@/api/register';
import AreaSelect from '@/components/AreaSelect';
import Layout from '@/components/layout/Layout';
import VerificationCodeButton from '@/components/VerificationCodeButton';
import { isPalmBaby } from '@/lib/utils';
import { debounce, hinaRegisterCommonProperties, hinaTrack } from '@/utils';

if (typeof document !== 'undefined') {
  document.title = '注册学校';
}

interface ISchoolType {
  [key: string]: string; // 这里使用字符串索引签名，表示该接口可以包含任意键
}

const schoolTypeMap: ISchoolType = {
  1: '公办幼儿园',
  2: '民办幼儿园（普惠）',
  3: '民办幼儿园',
  4: '托育中心',
  5: '中小学',
};

export default function RegisterPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const saleId = searchParams?.get('saleId') || null;
  const agentId = searchParams?.get('agentId') || null;
  const saleName = searchParams?.get('saleName') || null;
  const sourceType = searchParams?.get('sourceType') || null;
  const channel = searchParams?.get('openType') || null; // 打开该链接的类型，1-抖音;2-微信
  const openTypeName = searchParams?.get('openTypeName') || null; // 打开该链接的类型名称，比如：抖音，微信
  const isPalmBabyApp =
    typeof window !== 'undefined'
      ? isPalmBaby(window.location.hostname)
      : false;

  const filterRegisterType = () => {
    if (sourceType) {
      return Number(sourceType);
    }
    if (saleId) {
      return 2;
    }
    if (agentId) {
      return 0;
    }
    return 1;
  };

  const [schoolNamePopupVisible, setSchoolNamePopupVisible] = useState(false);
  const [verificationCodeDisabled, setVerificationCodeDisabled] =
    useState(false);
  const [searchSchoolText, setSearchSchoolText] = useState('');
  const [salePopupVisible, setSaleNamePopupVisible] = useState(false);
  const [schoolSelectVisible, setSchoolSelectVisible] = useState(false);
  const [salesman, setSalesman] = useState<null | any[]>(null);
  const salesmanData = useRef([]);

  const {
    data: schoolList,
    isError,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['schoolList', { searchSchoolText }],
    queryFn: async () => {
      if (searchSchoolText.trim() === '') {
        Toast.show('请输入学校名称');
        return;
      }
      const res = await getEnterprises({ keyWords: searchSchoolText });
      return Array.isArray(res.data) ? res.data : [];
    },
    // placeholderData: [],
    enabled: false,
  });

  const [postData, setPostData] = useImmer({
    instName: '',
    type: 0,
    provinceCode: '',
    cityCode: '',
    districtCode: '',
    area: [],
    address: '',
    adminName: '',
    adminMobile: '',
    smsCode: '',
    saleId: saleId || '',
    agentId: agentId || '',
    saleName: saleName || '',
    channel,
    registerType: filterRegisterType(),
  });

  const [loading, setLoading] = useState(false);
  useEffect(() => {
    if (saleName || saleId) {
      hinaRegisterCommonProperties({
        marketer_name: () => saleName || saleId,
        channel: () => openTypeName,
      });
      hinaTrack('QRcode_page_show', {
        marketer_name: saleName || saleId,
        channel: openTypeName,
        saleId,
      });
    }
    setPostData((draft) => {
      draft.saleId = saleId as string;
      draft.agentId = agentId as string;
      draft.saleName = saleName as string;
      draft.registerType = filterRegisterType(); // 0：代理商报备二维码注册 1：APP注册 2：销售二维码注册,3：直播引流
    });
  }, [saleId, agentId, sourceType, openTypeName, saleName]);

  useEffect(() => {
    getSalesman().then((res: any) => {
      if (Array.isArray(res.data)) {
        salesmanData.current = res.data;
      }
    });
    1;
  }, []);

  const [form] = Form.useForm();

  const checkSchoolExist = async (name: string) => {
    const res: any = await getExistence({
      name,
    });
    const { isExist } = res;
    if (isExist === 1) {
      Toast.show({
        content: '学校已存在',
        position: 'bottom',
      });
      return;
    }
    setPostData((draft) => {
      draft.instName = name;
    });
    form.setFieldsValue({ instName: name });
    setSchoolNamePopupVisible(false);
  };

  const onFinish = () => {
    setLoading(true);
    postInstitutions(postData, {
      headers: {
        Brand: isPalmBabyApp ? 2 : 1,
      },
    })
      .then((res: any) => {
        setLoading(false);
        const { mobile, password } = res;
        if (saleName || saleId) {
          hinaTrack('QRcode_submit_click', {
            marketer_name: saleName || saleId,
            channel: openTypeName,
            saleId,
          });
        }
        Toast.show({ icon: 'success', content: '创建成功' });
        router.push(`/register/success?mobile=${mobile}&password=${password}`);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  const openSearchSalesNamePopup = () => {
    setSaleNamePopupVisible(true);
  };

  const resetSearchSalesman = () => {
    setSaleNamePopupVisible(false);
    setSalesman([]);
  };

  const onSelectSale = (value: { id: number; name: string }) => {
    setPostData((draft) => {
      draft.saleId = String(value.id);
    });
    form.setFieldsValue({ saleName: value.name });
    resetSearchSalesman();
  };

  const onSearchSalesman = debounce((value) => {
    if (value.trim() !== '') {
      setSalesman(
        salesmanData.current.filter((item: { id: number; name: string }) =>
          item.name.includes(value)
        )
      );
    }
  }, 1000);

  const onSelectSchoolType = (value: number) => {
    setPostData((draft) => {
      draft.type = value;
    });
    setSchoolSelectVisible(false);
  };

  return (
    <Layout>
      <div className="my-3 text-center">
        <h1 className="inline-block bg-gradient-to-r from-blue-600 via-green-500 to-indigo-400 bg-clip-text text-transparent">
          {isPalmBabyApp ? '掌心宝贝' : '掌心智校'}
        </h1>
        <p className="mt-1 text-gray-500">请填写真实的学校信息</p>
      </div>
      <Form
        footer={
          <div style={{ margin: '16px 16px 0' }}>
            <Button
              block
              color="primary"
              loading={loading}
              shape="rounded"
              size="large"
              type="submit"
            >
              提交
            </Button>
          </div>
        }
        form={form}
        onFinish={onFinish}
      >
        {/* {!isPalmBabyApp && !saleId && !agentId && (
          <Form.Item
            name="saleName"
            rules={[{ required: true, message: '选择销售' }]}
            label="选择销售"
            onClick={() => {
              openSearchSalesNamePopup();
            }}
          >
            <Input placeholder="请选择销售人员" readOnly />
          </Form.Item>
        )} */}
        <Form.Item
          label="学校名称"
          name="instName"
          onClick={() => setSchoolNamePopupVisible(true)}
          rules={[{ required: true, message: '请填写营业执照上的名称' }]}
        >
          <Input placeholder="请填写营业执照上的名称" readOnly />
        </Form.Item>
        <Form.Item
          initialValue={1}
          label="学校类型"
          name="type"
          onClick={(_, action) => {
            // action?.current?.open();
            setSchoolSelectVisible(true);
          }}
          rules={[{ required: true, message: '请选择学校类型' }]}
        >
          {schoolTypeMap[postData.type] ? (
            <span className="text-stone-700">
              {schoolTypeMap[postData.type]}
            </span>
          ) : (
            <span className="text-stone-300">请选择学校类型</span>
          )}
        </Form.Item>
        <Form.Item
          label="学校地区"
          name="area"
          rules={[
            { required: true, message: '请选择学校所在地' },
            {
              validator: (_, value) => {
                if (value && value.every((item) => item?.value)) {
                  return Promise.resolve();
                }
                return Promise.reject('请选择完整的省市区');
              },
            },
          ]}
        >
          <AreaSelect
            onChange={(value) => {
              setPostData((draft) => {
                draft.provinceCode = value[0]?.value || '';
                draft.cityCode = value[1]?.value || '';
                draft.districtCode = value[2]?.value || '';
              });
            }}
            value={[
              postData.provinceCode,
              postData.cityCode,
              postData.districtCode,
            ]}
          />
        </Form.Item>
        <Form.Item
          label="详细地址"
          name="address"
          rules={[{ required: true, message: '请填写详细地址' }]}
        >
          <TextArea
            autoSize
            maxLength={50}
            onChange={(value) =>
              setPostData((draft) => {
                draft.address = value;
              })
            }
            placeholder="请输入学校详细地址"
            rows={2}
            showCount
          />
        </Form.Item>
        <Form.Item
          className="border-gray-100 border-t-4"
          label="园长姓名"
          name="adminName"
          rules={[{ required: true, message: '请填写园长姓名' }]}
        >
          <Input
            onChange={(value) =>
              setPostData((draft) => {
                draft.adminName = value;
              })
            }
            placeholder="请输入园长姓名"
          />
        </Form.Item>
        <Form.Item
          label="手机号"
          name="adminMobile"
          rules={[
            {
              validator: (_, value) => {
                if (/1\d{10}/.test(value)) {
                  setVerificationCodeDisabled(false);
                  return Promise.resolve(true);
                }
                setVerificationCodeDisabled(true);
                return Promise.reject(new Error('请输入正确的手机号码'));
              },
            },
          ]}
        >
          <Input
            maxLength={11}
            onChange={(value) =>
              setPostData((draft) => {
                draft.adminMobile = value;
              })
            }
            placeholder="请输入手机号"
            type="tel"
          />
        </Form.Item>
        <Form.Item
          extra={
            <VerificationCodeButton
              disabled={verificationCodeDisabled}
              isPalmBaby={isPalmBabyApp}
              phoneNumber={postData.adminMobile}
            />
          }
          label="短信验证码"
          name="smsCode"
          rules={[
            { required: true, message: '请填写验证码' },
            { pattern: /\d{6}/, message: '请输入6位数字' },
          ]}
        >
          <Input
            className="focus:outline-none"
            maxLength={6}
            onChange={(value) =>
              setPostData((draft) => {
                draft.smsCode = value;
              })
            }
            placeholder="请输入短信验证码"
            type="number"
          />
        </Form.Item>
      </Form>
      <Popup
        bodyStyle={{ height: '50%' }}
        onClose={() => setSchoolNamePopupVisible(false)}
        onMaskClick={() => {
          setSchoolNamePopupVisible(false);
        }}
        position="bottom"
        visible={schoolNamePopupVisible}
      >
        <div className="flex h-full flex-col">
          <div className="sticky mb-3 flex items-center justify-between p-2">
            <div className="flex-1">
              <SearchBar
                onChange={setSearchSchoolText}
                placeholder="请输入学校名称"
                value={searchSchoolText}
              />
            </div>
            <div className="ml-2">
              <Button
                color="primary"
                onClick={() => {
                  refetch();
                }}
                size="small"
              >
                搜索
              </Button>
            </div>
          </div>
          <div className="h-full flex-1 overflow-y-scroll">
            {isLoading && (
              <div className="flex h-full items-center justify-center">
                <Loading />
              </div>
            )}
            {isError && <ErrorBlock status="default" />}
            {Array.isArray(schoolList) && schoolList.length === 0 ? (
              <ErrorBlock status="empty" />
            ) : (
              <List>
                {schoolList?.map((item: { name: string }, i) => (
                  <List.Item
                    key={i}
                    onClick={() => {
                      checkSchoolExist(item.name);
                    }}
                  >
                    {item.name}
                  </List.Item>
                ))}
              </List>
            )}
          </div>
        </div>
      </Popup>
      {/* 选择销售弹窗 */}
      <Popup
        bodyStyle={{ height: '50%' }}
        onClose={() => setSaleNamePopupVisible(false)}
        onMaskClick={() => {
          setSaleNamePopupVisible(false);
        }}
        position="bottom"
        visible={salePopupVisible}
      >
        <div className="flex h-full flex-col">
          <div className="sticky flex items-center justify-between p-2">
            <div className="flex-1">
              <SearchBar
                onChange={onSearchSalesman}
                placeholder="请输入销售人员名字"
              />
            </div>
          </div>
          <div className="h-full overflow-y-scroll">
            {Array.isArray(salesman) && salesman.length === 0 ? (
              <div className="flex h-full justify-center">
                <ErrorBlock description="暂无数据" status="empty" />
              </div>
            ) : null}
            <List
              style={{
                '--border-bottom': 'none',
                '--border-top': 'none',
              }}
            >
              {salesman?.map((item: { id: number; name: string }, i) => (
                <List.Item
                  key={i}
                  onClick={() => {
                    onSelectSale(item);
                  }}
                >
                  {item.name}
                </List.Item>
              ))}
            </List>
          </div>
        </div>
      </Popup>
      <ActionSheet
        actions={
          Object.entries(schoolTypeMap).map(([key, value]) => ({
            text: value,
            key,
            onClick: () => onSelectSchoolType(Number(key)),
          })) as any
        }
        extra="请选择学校类型"
        onClose={() => {
          setSchoolSelectVisible(false);
        }}
        visible={schoolSelectVisible}
      />
    </Layout>
  );
}
