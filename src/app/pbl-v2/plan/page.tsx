'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  Dialog,
  Input,
  SpinLoading,
  TextArea,
  Toast,
} from 'antd-mobile';
import { clsx } from 'clsx';
import {
  Brush,
  CheckCircle2,
  Edit3,
  Eye,
  Lightbulb,
  MessageSquare,
  Package,
  RefreshCw,
  Settings,
  Target,
  Users,
  Wand2,
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

// 项目信息接口
interface ProjectInfo {
  projectId: string;
  projectName: string;
  description: string;
  learningGoals: string;
  drivingQuestion: string;
  ageGroup: string;
  theme?: string;
}

// 活动步骤接口
interface ActivityStep {
  id: string;
  stepTitle: string;
  activityGoals: string;
  activityDescription: string;
  teacherInteraction: string;
  environmentSetup: string;
  materials: string[];
}

// 活动计划接口
interface ActivityPlan {
  steps: ActivityStep[];
  allMaterials: string[];
  generatedAt: string;
}

export default function PBLPlanPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const projectId = searchParams.get('projectId') || '1';

  // 状态管理
  const [projectInfo, setProjectInfo] = useState<ProjectInfo | null>(null);
  const [additionalRequirements, setAdditionalRequirements] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [activityPlan, setActivityPlan] = useState<ActivityPlan | null>(null);
  const [editingStep, setEditingStep] = useState<string | null>(null);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValue, setEditValue] = useState('');
  const [showRegenerateModal, setShowRegenerateModal] = useState(false);
  const [supplementInfo, setSupplementInfo] = useState('');

  // 模拟获取项目信息
  useEffect(() => {
    const mockProjectInfo: ProjectInfo = {
      projectId,
      projectName: '探索秋天的奥秘',
      description:
        '通过观察、收集、实验等方式，让幼儿深入了解秋天的特征，培养科学探索精神和自然观察能力。',
      learningGoals:
        '1. 认识秋天的自然现象和变化\n2. 培养观察和记录能力\n3. 学习分类和比较的科学方法\n4. 增强对大自然的热爱',
      drivingQuestion: '为什么树叶会变颜色？秋天还有哪些神奇的变化？',
      ageGroup: '4-5岁（中班）',
      theme: '自然科学',
    };
    setProjectInfo(mockProjectInfo);
  }, [projectId]);

  // 模拟AI生成活动计划
  const generateActivityPlan = useCallback(
    async (supplement?: string) => {
      if (!projectInfo) {
        return;
      }

      setIsGenerating(true);

      // 模拟API调用延迟
      await new Promise((resolve) => setTimeout(resolve, 3000));

      // 根据补充信息调整生成结果
      if (supplement && supplement.trim().length > 0) {
        console.log('补充信息:', supplement);
      }

      const mockPlan: ActivityPlan = {
        steps: [
          {
            id: '1',
            stepTitle: '第一步：激发兴趣 - 寻找秋天的颜色',
            activityGoals: '认识3种以上秋天的颜色，学习使用形容词描述颜色',
            activityDescription:
              '带领幼儿到户外观察秋天的景色，收集不同颜色的落叶。引导幼儿用"金黄的"、"火红的"、"橙色的"等词汇描述所见。',
            teacherInteraction:
              '提问："你看到了什么颜色的叶子？"、"这片叶子像什么？"鼓励幼儿大胆表达，及时给予肯定。',
            environmentSetup:
              '准备收集袋、放大镜，选择落叶丰富的户外场地，确保安全。',
            materials: ['收集袋', '放大镜', '记录本', '彩色笔'],
          },
          {
            id: '2',
            stepTitle: '第二步：科学探索 - 叶子变色的秘密',
            activityGoals: '初步了解叶子变色的原因，培养科学探究兴趣',
            activityDescription:
              '通过简单实验，让幼儿观察叶子的变化。用热水浸泡绿叶，观察颜色变化，引导思考。',
            teacherInteraction:
              '引导提问："为什么叶子会变颜色？"、"我们来做个小实验看看"，鼓励幼儿猜测和观察。',
            environmentSetup: '准备实验台，确保用水安全，提供充足的观察空间。',
            materials: ['透明容器', '热水', '绿叶', '温度计', '实验记录表'],
          },
          {
            id: '3',
            stepTitle: '第三步：创意表达 - 制作秋天的画册',
            activityGoals: '运用收集的材料进行创作，提高动手能力和审美能力',
            activityDescription:
              '使用收集的落叶制作拼贴画，记录秋天的发现。每个幼儿制作自己的"秋天发现册"。',
            teacherInteraction:
              '鼓励创意："你想用这些叶子做什么？"提供技术支持，展示不同的制作方法。',
            environmentSetup: '准备充足的桌面空间，良好的光线，展示区域。',
            materials: ['胶水', '彩纸', '剪刀', '画笔', '装订器'],
          },
          {
            id: '4',
            stepTitle: '第四步：分享交流 - 我的秋天发现',
            activityGoals: '提高语言表达能力，学会倾听和分享',
            activityDescription:
              '每个幼儿展示自己的作品，分享秋天的发现。组织小组讨论，总结秋天的特征。',
            teacherInteraction:
              '引导分享："告诉大家你最喜欢的发现"，鼓励其他幼儿提问和评价。',
            environmentSetup: '布置展示区，准备小舞台或展示板，营造分享氛围。',
            materials: ['展示板', '小话筒', '贴纸奖励', '拍照设备'],
          },
        ],
        allMaterials: [
          '收集袋',
          '放大镜',
          '记录本',
          '彩色笔',
          '透明容器',
          '热水',
          '绿叶',
          '温度计',
          '实验记录表',
          '胶水',
          '彩纸',
          '剪刀',
          '画笔',
          '装订器',
          '展示板',
          '小话筒',
          '贴纸奖励',
          '拍照设备',
        ],
        generatedAt: new Date().toISOString(),
      };

      setActivityPlan(mockPlan);
      setIsGenerating(false);

      Toast.show({
        icon: 'success',
        content: '活动计划生成成功！',
      });
    },
    [projectInfo]
  );

  // 开始编辑
  const startEdit = (stepId: string, field: string, currentValue: string) => {
    setEditingStep(stepId);
    setEditingField(field);
    setEditValue(currentValue);
  };

  // 保存编辑
  const saveEdit = () => {
    if (!(activityPlan && editingStep && editingField)) {
      return;
    }

    const updatedPlan = {
      ...activityPlan,
      steps: activityPlan.steps.map((step) =>
        step.id === editingStep ? { ...step, [editingField]: editValue } : step
      ),
    };

    setActivityPlan(updatedPlan);
    setEditingStep(null);
    setEditingField(null);
    setEditValue('');

    Toast.show({
      icon: 'success',
      content: '修改已保存',
    });
  };

  // 取消编辑
  const cancelEdit = () => {
    setEditingStep(null);
    setEditingField(null);
    setEditValue('');
  };

  // 显示重新生成弹窗
  const showRegenerateDialog = () => {
    setShowRegenerateModal(true);
  };

  // 重新生成计划
  const regeneratePlan = async () => {
    setShowRegenerateModal(false);
    setActivityPlan(null);
    await generateActivityPlan(supplementInfo);
    setSupplementInfo('');
  };

  // 取消重新生成
  const cancelRegenerate = () => {
    setShowRegenerateModal(false);
    setSupplementInfo('');
  };

  // 优化单个步骤
  const optimizeStep = async () => {
    const result = await Dialog.confirm({
      content: '确定要优化这个步骤吗？AI将为您提供更好的建议。',
      confirmText: '确定',
      cancelText: '取消',
    });

    if (result) {
      Toast.show({
        icon: 'loading',
        content: '正在优化...',
        duration: 2000,
      });

      // 模拟优化过程
      setTimeout(() => {
        Toast.show({
          icon: 'success',
          content: '步骤优化完成！',
        });
      }, 2000);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (event: React.KeyboardEvent, action: () => void) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      action();
    }
  };

  if (!projectInfo) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <SpinLoading />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-100 via-white to-indigo-50/30">
      {/* 顶部导航 */}
      <div className="mx-auto max-w-4xl border border-stone-50 border-b bg-white px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div>
              <h1 className="font-semibold text-lg text-slate-900">
                项目活动计划
              </h1>
              <p className="text-slate-500 text-sm">
                活动计划仅供参考，让 AI
                成为您的得力助手，轻松制定个性化教学方案。
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="mx-auto max-w-4xl px-4 py-6">
        {/* 项目信息卡片 */}
        <Card
          bodyStyle={{
            padding: '0 0 12px',
          }}
          className="mb-6 overflow-hidden rounded-2xl border-0 shadow-sm ring-1 ring-slate-200/50"
          style={{
            '--adm-card-header-border-width': '0',
          }}
          title="项目基本信息"
        >
          <div className="relative">
            {/* 装饰性背景 */}
            <div className="absolute inset-0 rounded-lg bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50" />
            {/* <div className="-right-10 -top-10 absolute h-32 w-32 rounded-full bg-indigo-100 opacity-60 blur-3xl" /> */}
            {/* <div className="-left-10 -bottom-10 absolute h-32 w-32 rounded-full bg-purple-100 opacity-60 blur-3xl" /> */}

            <div className="relative overflow-hidden rounded p-4">
              <div className="mb-4 flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-indigo-100 shadow-inner">
                    <Lightbulb className="h-6 w-6 text-indigo-600" />
                  </div>
                  <div>
                    <h2 className="font-semibold text-slate-900 text-xl">
                      {projectInfo.projectName}
                    </h2>
                    <div className="flex items-center gap-4 text-slate-600 text-xs">
                      <span className="flex items-center gap-1">
                        <Users className="h-4 w-4" />
                        {projectInfo.ageGroup}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-3">
                  <div>
                    <h3 className="mb-2 flex items-center gap-2 font-medium text-slate-900 text-sm">
                      <Eye className="h-4 w-4 text-indigo-500" />
                      项目描述
                    </h3>
                    <p className="text-slate-700 text-sm leading-relaxed">
                      {projectInfo.description}
                    </p>
                  </div>
                  <div>
                    <h3 className="mb-2 flex items-center gap-2 font-medium text-slate-900 text-sm">
                      <MessageSquare className="h-4 w-4 text-purple-500" />
                      驱动性问题
                    </h3>
                    <p className="text-slate-700 text-sm leading-relaxed">
                      {projectInfo.drivingQuestion}
                    </p>
                  </div>
                </div>
                <div>
                  <h3 className="mb-2 flex items-center gap-2 font-medium text-slate-900 text-sm">
                    <Target className="h-4 w-4 text-emerald-500" />
                    学习目标
                  </h3>
                  <div className="space-y-1">
                    {projectInfo.learningGoals.split('\n').map((goal) => (
                      <p
                        className="text-slate-700 text-sm leading-relaxed"
                        key={goal}
                      >
                        {goal}
                      </p>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* 生成按钮 */}
        {!activityPlan && (
          <>
            {/* 额外要求输入 */}
            <Card className="mb-16 rounded-2xl border-0 shadow-sm ring-1 ring-slate-200/50">
              <div className="p-2">
                <div className="mb-3 flex items-center gap-2 font-medium text-slate-900">
                  <Settings className="h-4 w-4 text-slate-600" />
                  附加要求
                </div>
                <TextArea
                  className="w-full"
                  onChange={setAdditionalRequirements}
                  placeholder="请描述您对活动计划的特殊要求，如：重点关注某个能力培养、特定的活动形式、时间安排等..."
                  rows={3}
                  value={additionalRequirements}
                />
              </div>
            </Card>
            <div className="fixed bottom-0 left-0 mb-4 flex w-full justify-center">
              <Button
                className={clsx(
                  'relative overflow-hidden rounded-2xl px-8 py-4 font-medium text-white shadow-lg transition-all duration-300',
                  'bg-gradient-to-r from-indigo-500 to-blue-500',
                  'hover:shadow-indigo-500/25 hover:shadow-xl',
                  'disabled:opacity-50'
                )}
                color="primary"
                disabled={isGenerating}
                loading={isGenerating}
                onClick={() => generateActivityPlan()}
                shape="rounded"
                size="large"
              >
                <div className="flex items-center gap-3">
                  {isGenerating ? (
                    <>
                      <SpinLoading className="h-5 w-5" />
                      <span>AI 正在生成活动计划...</span>
                    </>
                  ) : (
                    <>
                      <Wand2 className="h-5 w-5" />
                      <span>AI 生成活动计划</span>
                    </>
                  )}
                </div>
              </Button>
            </div>
          </>
        )}

        {/* 加载动画 */}
        {isGenerating && (
          <Card className="mb-6 rounded-2xl border-0 shadow-sm ring-1 ring-slate-200/50">
            <div className="p-8 text-center">
              <div className="mx-auto mb-4 h-16 w-16 animate-spin rounded-full border-4 border-indigo-200 border-t-indigo-500" />
              <h3 className="mb-2 font-medium text-slate-900">
                AI 正在为您精心设计活动计划
              </h3>
              <p className="text-slate-600 text-sm">
                分析项目需求，生成个性化教学方案...
              </p>
              <div className="mt-4 flex items-center justify-center gap-2">
                <div
                  className="h-2 w-2 animate-bounce rounded-full bg-indigo-400"
                  style={{ animationDelay: '0ms' }}
                />
                <div
                  className="h-2 w-2 animate-bounce rounded-full bg-purple-400"
                  style={{ animationDelay: '150ms' }}
                />
                <div
                  className="h-2 w-2 animate-bounce rounded-full bg-pink-400"
                  style={{ animationDelay: '300ms' }}
                />
              </div>
            </div>
          </Card>
        )}

        {/* 活动计划结果 */}
        {activityPlan && (
          <div className="space-y-6">
            {/* 活动计划详情标题 */}
            <h2 className="font-semibold text-slate-900 text-xl">
              活动计划详情
            </h2>

            {/* 操作栏 */}
            <div className="flex items-center justify-between rounded-xl bg-white p-4 shadow-sm ring-1 ring-slate-200/50">
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-5 w-5 text-emerald-500" />
                <span className="font-medium text-slate-900">
                  活动计划已生成
                </span>
                <span className="text-slate-500 text-sm">
                  {new Date(activityPlan.generatedAt).toLocaleString()}
                </span>
              </div>
            </div>

            {/* 活动步骤 */}
            <div className="space-y-4">
              {activityPlan.steps.map((step, index) => (
                <Card
                  className="overflow-hidden rounded-2xl border-0 shadow-sm ring-1 ring-slate-200/50"
                  key={step.id}
                >
                  <div className="relative">
                    <div className="p-4">
                      {/* 步骤标题 */}
                      <div className="mb-4">
                        {editingStep === step.id &&
                        editingField === 'stepTitle' ? (
                          <div className="flex gap-2">
                            <Input
                              className="flex-1"
                              onChange={setEditValue}
                              value={editValue}
                            />
                            <Button onClick={saveEdit} size="small">
                              保存
                            </Button>
                            <Button onClick={cancelEdit} size="small">
                              取消
                            </Button>
                          </div>
                        ) : (
                          <button
                            className="group flex w-full cursor-pointer items-center gap-2 text-left font-semibold text-lg text-slate-900 hover:text-indigo-600"
                            onClick={() =>
                              startEdit(step.id, 'stepTitle', step.stepTitle)
                            }
                            onKeyDown={(e) =>
                              handleKeyDown(e, () =>
                                startEdit(step.id, 'stepTitle', step.stepTitle)
                              )
                            }
                            type="button"
                          >
                            {step.stepTitle}
                            <Edit3 className="h-4 w-4 opacity-0 transition-opacity group-hover:opacity-100" />
                          </button>
                        )}
                      </div>

                      <div className="grid gap-4 md:grid-cols-2">
                        {/* 活动目标 */}
                        <div className="space-y-2">
                          <h4 className="flex items-center gap-2 font-medium text-slate-900 text-sm">
                            <Target className="h-4 w-4 text-emerald-500" />
                            活动目标
                          </h4>
                          {editingStep === step.id &&
                          editingField === 'activityGoals' ? (
                            <div className="space-y-2">
                              <TextArea
                                onChange={setEditValue}
                                rows={2}
                                value={editValue}
                              />
                              <div className="flex gap-2">
                                <Button onClick={saveEdit} size="small">
                                  保存
                                </Button>
                                <Button onClick={cancelEdit} size="small">
                                  取消
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <button
                              className="-m-2 w-full cursor-pointer rounded p-2 text-left text-slate-700 text-sm leading-relaxed hover:bg-slate-50"
                              onClick={() =>
                                startEdit(
                                  step.id,
                                  'activityGoals',
                                  step.activityGoals
                                )
                              }
                              onKeyDown={(e) =>
                                handleKeyDown(e, () =>
                                  startEdit(
                                    step.id,
                                    'activityGoals',
                                    step.activityGoals
                                  )
                                )
                              }
                              type="button"
                            >
                              {step.activityGoals}
                            </button>
                          )}
                        </div>

                        {/* 师生互动 */}
                        <div className="space-y-2">
                          <h4 className="flex items-center gap-2 font-medium text-slate-900 text-sm">
                            <MessageSquare className="h-4 w-4 text-blue-500" />
                            师生互动建议
                          </h4>
                          {editingStep === step.id &&
                          editingField === 'teacherInteraction' ? (
                            <div className="space-y-2">
                              <TextArea
                                onChange={setEditValue}
                                rows={2}
                                value={editValue}
                              />
                              <div className="flex gap-2">
                                <Button onClick={saveEdit} size="small">
                                  保存
                                </Button>
                                <Button onClick={cancelEdit} size="small">
                                  取消
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <button
                              className="-m-2 w-full cursor-pointer rounded p-2 text-left text-slate-700 text-sm leading-relaxed hover:bg-slate-50"
                              onClick={() =>
                                startEdit(
                                  step.id,
                                  'teacherInteraction',
                                  step.teacherInteraction
                                )
                              }
                              onKeyDown={(e) =>
                                handleKeyDown(e, () =>
                                  startEdit(
                                    step.id,
                                    'teacherInteraction',
                                    step.teacherInteraction
                                  )
                                )
                              }
                              type="button"
                            >
                              {step.teacherInteraction}
                            </button>
                          )}
                        </div>
                      </div>

                      {/* 活动描述 */}
                      <div className="mt-4 space-y-2">
                        <h4 className="flex items-center gap-2 font-medium text-slate-900 text-sm">
                          <Brush className="h-4 w-4 text-purple-500" />
                          活动描述
                        </h4>
                        {editingStep === step.id &&
                        editingField === 'activityDescription' ? (
                          <div className="space-y-2">
                            <TextArea
                              onChange={setEditValue}
                              rows={3}
                              value={editValue}
                            />
                            <div className="flex gap-2">
                              <Button onClick={saveEdit} size="small">
                                保存
                              </Button>
                              <Button onClick={cancelEdit} size="small">
                                取消
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <button
                            className="-m-2 w-full cursor-pointer rounded p-2 text-left text-slate-700 text-sm leading-relaxed hover:bg-slate-50"
                            onClick={() =>
                              startEdit(
                                step.id,
                                'activityDescription',
                                step.activityDescription
                              )
                            }
                            onKeyDown={(e) =>
                              handleKeyDown(e, () =>
                                startEdit(
                                  step.id,
                                  'activityDescription',
                                  step.activityDescription
                                )
                              )
                            }
                            type="button"
                          >
                            {step.activityDescription}
                          </button>
                        )}
                      </div>

                      {/* 环境创设 */}
                      <div className="mt-4 space-y-2">
                        <h4 className="flex items-center gap-2 font-medium text-slate-900 text-sm">
                          <Settings className="h-4 w-4 text-orange-500" />
                          环境创设
                        </h4>
                        {editingStep === step.id &&
                        editingField === 'environmentSetup' ? (
                          <div className="space-y-2">
                            <TextArea
                              onChange={setEditValue}
                              rows={2}
                              value={editValue}
                            />
                            <div className="flex gap-2">
                              <Button onClick={saveEdit} size="small">
                                保存
                              </Button>
                              <Button onClick={cancelEdit} size="small">
                                取消
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <button
                            className="-m-2 w-full cursor-pointer rounded p-2 text-left text-slate-700 text-sm leading-relaxed hover:bg-slate-50"
                            onClick={() =>
                              startEdit(
                                step.id,
                                'environmentSetup',
                                step.environmentSetup
                              )
                            }
                            onKeyDown={(e) =>
                              handleKeyDown(e, () =>
                                startEdit(
                                  step.id,
                                  'environmentSetup',
                                  step.environmentSetup
                                )
                              )
                            }
                            type="button"
                          >
                            {step.environmentSetup}
                          </button>
                        )}
                      </div>

                      {/* 所需材料 */}
                      <div className="mt-4 space-y-2">
                        <h4 className="flex items-center gap-2 font-medium text-slate-900 text-sm">
                          <Package className="h-4 w-4 text-pink-500" />
                          所需材料
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {step.materials.map((material, materialIdx) => (
                            <span
                              className="rounded-full bg-slate-100 px-3 py-1 text-slate-700 text-xs"
                              key={`material-${step.id}-${materialIdx}`}
                            >
                              {material}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* 步骤操作 */}
                      <div className="mt-4 flex justify-end gap-2 border-slate-100 border-t pt-3">
                        <Button
                          className="flex items-center gap-1 text-indigo-600"
                          onClick={optimizeStep}
                          size="small"
                        >
                          <Wand2 className="h-4 w-4" />
                          优化此步骤
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* 材料清单汇总 */}
            <Card className="rounded-2xl border-0 shadow-sm ring-1 ring-slate-200/50">
              <div className="p-2">
                <h3 className="mb-4 flex items-center gap-2 font-semibold text-lg text-slate-900">
                  <Package className="h-5 w-5 text-indigo-500" />
                  材料清单汇总
                  <span className="rounded-full bg-indigo-100 px-2 py-1 font-medium text-indigo-600 text-xs">
                    共 {activityPlan.allMaterials.length} 项
                  </span>
                </h3>
                <div className="grid gap-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                  {activityPlan.allMaterials.map((material) => (
                    <div
                      className="flex items-center gap-2 rounded-lg bg-slate-50 p-3 text-slate-700 text-sm"
                      key={material}
                    >
                      <div className="h-2 w-2 rounded-full bg-indigo-400" />
                      {material}
                    </div>
                  ))}
                </div>
              </div>
            </Card>

            {/* 换一种思路按钮 - 移到最底部 */}
            <div className="mt-8 text-center">
              <Button
                color="primary"
                fill="outline"
                onClick={showRegenerateDialog}
                shape="rounded"
                size="large"
              >
                <div className="flex items-center gap-3">
                  <RefreshCw className="h-5 w-5" />
                  <span>重新生成</span>
                </div>
              </Button>
            </div>
          </div>
        )}

        {/* 重新生成弹窗 */}
        {showRegenerateModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
            <div className="mx-4 w-full max-w-md rounded-2xl bg-white p-6 shadow-xl">
              <h3 className="mb-4 font-semibold text-lg text-slate-900">
                换一种思路
              </h3>
              <div className="space-y-4">
                <div>
                  <h4 className="mb-2 font-medium text-slate-900">补充信息</h4>
                  <p className="mb-3 text-slate-600 text-sm">
                    请描述您希望在新的活动计划中重点关注的方面或改进建议
                  </p>
                  <TextArea
                    className="w-full"
                    onChange={setSupplementInfo}
                    placeholder="例如：增加更多互动环节、注重培养创造力、适合室内活动等..."
                    rows={4}
                    value={supplementInfo}
                  />
                </div>
                <div className="flex gap-3 pt-2">
                  <Button
                    className="flex-1"
                    color="default"
                    onClick={cancelRegenerate}
                  >
                    取消
                  </Button>
                  <Button
                    className="flex-1"
                    color="primary"
                    disabled={isGenerating}
                    loading={isGenerating}
                    onClick={regeneratePlan}
                  >
                    {isGenerating ? '正在生成...' : '确定'}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
