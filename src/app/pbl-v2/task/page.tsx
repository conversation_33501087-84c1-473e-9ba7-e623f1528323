'use client';

import { type Active, MeasuringStrategy } from '@dnd-kit/core';
import {
  restrictToVerticalAxis,
  restrictToWindowEdges,
} from '@dnd-kit/modifiers';
import {
  type AnimateLayoutChanges,
  defaultAnimateLayoutChanges,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { Button, Dialog, FloatingBubble, TextArea, Toast } from 'antd-mobile';
import {
  ChevronDown,
  ChevronRight,
  Edit3,
  Eye,
  Inbox,
  Lightbulb,
  MessageSquare,
  Plus,
  Sparkles,
  Target,
} from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Sortable, type SortableProps } from '@/components/Sortable';
import { TaskCard } from './components/TaskCard';
import { TaskForm } from './components/TaskForm';
import { mockTasks, type TaskItem } from './types';

// 项目信息接口
interface ProjectInfo {
  projectId: string;
  projectName: string;
  description: string;
  learningGoals: string;
  drivingQuestion: string;
  ageGroup: string;
  theme?: string;
}

export default function TaskPage() {
  // 状态管理
  const [tasks, setTasks] = useState<TaskItem[]>([]);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<TaskItem | undefined>();
  const [expandedTasks, setExpandedTasks] = useState<Record<string, boolean>>(
    {}
  );

  // AI 生成相关状态
  const [showAiGenerate, setShowAiGenerate] = useState(false);
  const [aiRequirement, setAiRequirement] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [projectInfo, setProjectInfo] = useState<ProjectInfo | null>(null);

  // 模拟获取项目信息
  useEffect(() => {
    setProjectInfo({
      projectId: '111',
      projectName: '探索秋天的奥秘',
      description:
        '通过观察、收集、实验等方式，让幼儿深入了解秋天的特征，培养科学探索精神和自然观察能力。',
      learningGoals:
        '1. 认识秋天的自然现象和变化\n2. 培养观察和记录能力\n3. 学习分类和比较的科学方法\n4. 增强对大自然的热爱',
      drivingQuestion: '为什么树叶会变颜色？秋天还有哪些神奇的变化？',
      ageGroup: '4-5岁（中班）',
      theme: '自然科学',
    });
  }, []);

  const animateLayoutChanges: AnimateLayoutChanges = (args) =>
    defaultAnimateLayoutChanges({ ...args, wasDragging: true });

  // 生成新的任务 ID
  const generateTaskId = useCallback(() => {
    return (
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15)
    );
  }, []);

  // 新增任务
  const handleAddTask = useCallback(() => {
    setEditingTask(undefined);
    setIsFormOpen(true);
  }, []);

  // 手动创建任务
  const handleManualCreate = useCallback(() => {
    handleAddTask();
  }, [handleAddTask]);

  // 显示 AI 生成界面
  const handleAiGenerate = useCallback(() => {
    setShowAiGenerate(true);
  }, []);

  // 关闭 AI 生成界面
  const handleAiGenerateClose = useCallback(() => {
    setShowAiGenerate(false);
    setAiRequirement('');
  }, []);

  // AI 生成任务
  const handleAiGenerateSubmit = useCallback(async () => {
    setIsGenerating(true);

    // 模拟接口请求
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // 使用 mockTasks 数据，按 order 排序
    setTasks([...mockTasks].sort((a, b) => a.order - b.order));
    setIsGenerating(false);
    setShowAiGenerate(false);
    setAiRequirement('');

    Toast.show({
      icon: 'success',
      content: 'AI 生成任务成功！',
    });
  }, []);

  // 编辑任务
  const handleEditTask = useCallback((task: TaskItem) => {
    setEditingTask(task);
    setIsFormOpen(true);
  }, []);

  // 删除任务确认
  const handleDeleteTask = useCallback(
    (taskId: string) => {
      const task = tasks.find((t) => t.id === taskId);
      if (task) {
        Dialog.confirm({
          content: `确定要删除任务"${task.title}"吗？此操作无法撤销。`,
          onConfirm: () => {
            setTasks((prev) => prev.filter((t) => t.id !== taskId));
            Toast.show({
              icon: 'success',
              content: '任务删除成功',
            });
          },
        });
      }
    },
    [tasks]
  );

  // 提交表单（新增或编辑）
  const handleFormSubmit = useCallback(
    (taskData: Omit<TaskItem, 'id' | 'status'> & { id?: string }) => {
      console.log('🚀 ~ taskData:', taskData);
      if (editingTask) {
        // 编辑模式 - 保持原有状态
        setTasks((prev) =>
          prev.map((t) =>
            t.id === editingTask.id
              ? ({
                  ...taskData,
                  id: editingTask.id,
                  status: editingTask.status,
                } as TaskItem)
              : t
          )
        );
      } else {
        // 新增模式 - 默认状态为"未开始"，order 为当前最大值 + 1
        const maxOrder =
          tasks.length > 0 ? Math.max(...tasks.map((t) => t.order)) : 0;
        const newTask: TaskItem = {
          ...taskData,
          id: generateTaskId(),
          status: '未开始',
          order: maxOrder + 1,
        } as TaskItem;
        console.log('🚀 ~ newTask:', newTask);
        setTasks((prev) =>
          [...prev, newTask].sort((a, b) => a.order - b.order)
        );
      }
      Toast.show({
        icon: 'success',
        content: editingTask ? '任务更新成功' : '任务创建成功',
      });
    },
    [editingTask, generateTaskId]
  );

  // 关闭表单
  const handleFormClose = useCallback(() => {
    setIsFormOpen(false);
    setEditingTask(undefined);
  }, []);

  // 状态变更处理函数
  const handleStatusChange = useCallback(
    (taskId: string, newStatus: TaskItem['status']) => {
      setTasks((prev) =>
        prev.map((task) =>
          task.id === taskId ? { ...task, status: newStatus } : task
        )
      );

      const statusMessages = {
        未开始: '任务已重置为未开始',
        进行中: '任务已开始',
        已完成: '任务已完成',
      };

      Toast.show({
        icon: 'success',
        content: statusMessages[newStatus],
      });
    },
    []
  );

  // 切换单个任务的展开状态
  const handleToggleTaskExpand = useCallback((taskId: string) => {
    setExpandedTasks((prev) => ({
      ...prev,
      [taskId]: !prev[taskId],
    }));
  }, []);

  // 全局展开所有任务
  const handleExpandAll = useCallback(() => {
    const allExpanded = tasks.reduce(
      (acc, task) => {
        acc[task.id] = true;
        return acc;
      },
      {} as Record<string, boolean>
    );
    setExpandedTasks(allExpanded);
  }, [tasks]);

  // 全局折叠所有任务
  const handleCollapseAll = useCallback(() => {
    const allCollapsed = tasks.reduce(
      (acc, task) => {
        acc[task.id] = false;
        return acc;
      },
      {} as Record<string, boolean>
    );
    setExpandedTasks(allCollapsed);
  }, [tasks]);

  // 检查是否所有任务都已展开
  const areAllExpanded = useMemo(() => {
    return tasks.length > 0 && tasks.every((task) => expandedTasks[task.id]);
  }, [tasks, expandedTasks]);

  // 按 order 排序的任务列表
  const sortedTasks = useMemo(() => {
    return [...tasks].sort((a, b) => a.order - b.order);
  }, [tasks]);

  // dnd 可见项 id（按顺序排列的任务）
  const visibleIds = useMemo(() => sortedTasks.map((t) => t.id), [sortedTasks]);
  console.log('🚀 ~ visibleIds:', visibleIds);

  // 处理拖拽结束，更新任务的 order 字段
  const handleDragEnd = useCallback(
    (event: { active: Active; over: Active | null }) => {
      const { active, over } = event;

      if (!over || active.id === over.id) {
        return;
      }

      const oldIndex = sortedTasks.findIndex((task) => task.id === active.id);
      const newIndex = sortedTasks.findIndex((task) => task.id === over.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        // 重新排序任务
        const reorderedTasks = [...sortedTasks];
        const movedTask = reorderedTasks[oldIndex];
        if (movedTask) {
          reorderedTasks.splice(oldIndex, 1);
          reorderedTasks.splice(newIndex, 0, movedTask);

          // 更新所有任务的 order 字段
          const updatedTasks = reorderedTasks.map((task, index) => ({
            ...task,
            order: index + 1,
          }));

          setTasks(updatedTasks);

          Toast.show({
            icon: 'success',
            content: '任务顺序已更新',
          });
        }
      }
    },
    [sortedTasks]
  );

  // 处理拖拽结束，更新任务的 order 字段

  const renderTaskItem = ({
    dragOverlay,
    dragging,
    listeners,
    ref,
    style,
    transform,
    transition,
    value,
  }: {
    dragOverlay: boolean;
    dragging: boolean;
    listeners: Record<string, (event: Event) => void>;
    ref: React.Ref<HTMLElement>;
    style: React.CSSProperties | undefined;
    transform: {
      x: number;
      y: number;
      scaleX?: number;
      scaleY?: number;
    } | null;
    transition: string | null;
    value: string;
  }) => {
    const task = sortedTasks.find((t) => t.id === value);
    if (!task) {
      return null;
    }

    return (
      <div ref={ref as React.LegacyRef<HTMLDivElement>} style={style}>
        <TaskCard
          dragOverlay={dragOverlay}
          expanded={expandedTasks[task.id]}
          handle={true}
          isDragging={dragging}
          listeners={listeners}
          onDelete={handleDeleteTask}
          onEdit={handleEditTask}
          onStatusChange={handleStatusChange}
          onToggleExpand={handleToggleTaskExpand}
          task={task}
          transform={transform || undefined}
          transition={transition || undefined}
        />
      </div>
    );
  };

  const props: Partial<SortableProps> = {
    strategy: verticalListSortingStrategy,
    items: visibleIds,
    renderItem: renderTaskItem,
    onDragEnd: handleDragEnd,
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-4xl px-4">
        <div className="mb-4 pt-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600">
                一个 PBL
                项目可以拆分成多个活动，每个活动都是一次独立的教学任务。
              </p>
              {tasks.length > 0 && (
                <p className="text-gray-600">
                  拖拽活动卡片可以调整顺序，点击卡片展开详情
                </p>
              )}
            </div>
          </div>
          <div className="flex justify-end">
            {/* 全局展开/折叠按钮 */}
            {tasks.length > 0 && (
              <button
                className="flex items-center gap-2 rounded-lg bg-white px-4 py-2 font-medium text-gray-700 text-sm shadow-sm transition-all duration-200 hover:bg-gray-50 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                onClick={areAllExpanded ? handleCollapseAll : handleExpandAll}
                title={areAllExpanded ? '折叠所有活动' : '展开所有活动'}
                type="button"
              >
                {areAllExpanded ? (
                  <ChevronRight className="text-gray-500" size={16} />
                ) : (
                  <ChevronDown className="text-gray-500" size={16} />
                )}
                <span>{areAllExpanded ? '折叠全部' : '展开全部'}</span>
              </button>
            )}
          </div>
        </div>

        {tasks.length > 0 && (
          <div className="space-y-3 pb-8">
            <Sortable
              {...props}
              animateLayoutChanges={animateLayoutChanges}
              handle
              measuring={{ droppable: { strategy: MeasuringStrategy.Always } }}
              modifiers={[restrictToVerticalAxis, restrictToWindowEdges]}
            />
          </div>
        )}

        {tasks.length === 0 && showAiGenerate && (
          <div className="space-y-6 pb-8">
            {/* AI 生成界面 */}
            <div className="overflow-hidden rounded-lg bg-white shadow-sm">
              <div className="border-gray-200 border-b px-6 py-4">
                <div className="flex items-center justify-between">
                  <h2 className="flex items-center gap-2 font-semibold text-gray-900 text-lg">
                    <Sparkles className="h-5 w-5 text-indigo-600" />
                    AI 帮您生成活动
                  </h2>
                </div>
              </div>

              <div className="p-4">
                <div className="mb-6 overflow-hidden rounded-lg bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 p-4">
                  <div className="mb-4">项目基本信息</div>

                  <div className="mb-4 flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded bg-indigo-100 shadow-inner">
                        <Lightbulb className="h-4 w-4 text-indigo-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg text-slate-900">
                          {projectInfo?.projectName}
                        </h3>
                      </div>
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <div>
                        <h4 className="mb-1 flex items-center gap-2 font-medium text-slate-900 text-sm">
                          <Eye className="h-4 w-4 text-indigo-500" />
                          项目描述
                        </h4>
                        <p className="text-slate-700 text-sm leading-relaxed">
                          {projectInfo?.description}
                        </p>
                      </div>
                      <div>
                        <h4 className="mb-1 flex items-center gap-2 font-medium text-slate-900 text-sm">
                          <MessageSquare className="h-4 w-4 text-purple-500" />
                          驱动性问题
                        </h4>
                        <p className="text-slate-700 text-sm leading-relaxed">
                          {projectInfo?.drivingQuestion}
                        </p>
                      </div>
                    </div>
                    <div>
                      <h4 className="mb-1 flex items-center gap-2 font-medium text-slate-900 text-sm">
                        <Target className="h-4 w-4 text-emerald-500" />
                        学习目标
                      </h4>
                      <div className="space-y-1">
                        {projectInfo?.learningGoals.split('\n').map((goal) => (
                          <p
                            className="text-slate-700 text-sm leading-relaxed"
                            key={goal}
                          >
                            {goal}
                          </p>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mb-6">
                  <div className="mb-2 font-medium text-gray-700 text-sm">
                    生成要求（可选）
                  </div>
                  <TextArea
                    disabled={isGenerating}
                    onChange={setAiRequirement}
                    placeholder="请描述您希望生成的活动小节要求，例如：适合5-6岁幼儿的植物主题探索活动..."
                    rows={4}
                    value={aiRequirement}
                  />
                </div>

                <div className="flex justify-center gap-3">
                  <Button
                    className="px-4"
                    color="primary"
                    disabled={isGenerating}
                    loading={isGenerating}
                    onClick={handleAiGenerateSubmit}
                    shape="rounded"
                  >
                    {isGenerating ? '生成中...' : '立即生成'}
                  </Button>
                  <Button
                    color="default"
                    disabled={isGenerating}
                    onClick={handleAiGenerateClose}
                    shape="rounded"
                  >
                    以后再说
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {tasks.length === 0 && !showAiGenerate && (
          <div className="flex flex-col items-center justify-center py-20">
            <div className="mb-4 flex h-24 w-24 items-center justify-center rounded-full bg-gray-200 shadow-inner">
              <Inbox className="h-12 w-12 text-gray-500" />
            </div>
            <h3 className="mb-2 font-medium text-gray-900 text-lg">暂无活动</h3>
            <p className="mb-8 text-gray-500 text-sm">
              开始创建你的第一个 PBL 项目活动
            </p>
            <div className="flex gap-4">
              <Button
                className="flex items-center gap-2"
                color="primary"
                onClick={handleAiGenerate}
                shape="rounded"
              >
                <Sparkles className="mr-2 h-4 w-4" />
                AI 生成活动
              </Button>
              <Button
                className="flex items-center gap-2"
                color="default"
                onClick={handleManualCreate}
                shape="rounded"
              >
                <Edit3 className="mr-2 h-4 w-4" />
                手动创建
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* 浮动新增按钮 - 仅在有数据时显示 */}
      {tasks.length > 0 && (
        <FloatingBubble
          onClick={handleAddTask}
          style={{
            '--initial-position-bottom': '24px',
            '--initial-position-right': '24px',
            '--edge-distance': '24px',
          }}
        >
          <Plus size={24} />
        </FloatingBubble>
      )}

      {/* 任务表单 */}
      <TaskForm
        isOpen={isFormOpen}
        onClose={handleFormClose}
        onSubmit={handleFormSubmit}
        task={editingTask}
      />
    </div>
  );
}
