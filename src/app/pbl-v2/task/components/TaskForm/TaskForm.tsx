'use client';

import { Button, Form, Input, TextArea, Toast } from 'antd-mobile';
import clsx from 'clsx';
import { X } from 'lucide-react';
import type React from 'react';
import { useEffect, useState } from 'react';
import type { TaskItem } from '../../types';

interface TaskFormProps {
  task?: TaskItem;
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (task: Omit<TaskItem, 'id' | 'status'> & { id?: string }) => void;
}

export const TaskForm: React.FC<TaskFormProps> = ({
  task,
  isOpen,
  onClose,
  onSubmit,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const isEdit = !!task;

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      const taskData = {
        ...values,
        id: task?.id,
      };

      onSubmit(taskData);
      form.resetFields();
      onClose();

      Toast.show({
        icon: 'success',
        content: isEdit ? '活动更新成功' : '活动创建成功',
      });
    } catch (error) {
      console.error('表单验证失败:', error);
      Toast.show({
        icon: 'fail',
        content: '请检查表单内容',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  // 当表单打开时，设置初始值
  useEffect(() => {
    if (isOpen && task) {
      form.setFieldsValue({
        title: task.title,
        objective: task.objective,
        notes: task.notes,
      });
    }
  }, [isOpen, task, form]);

  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 flex items-end justify-center bg-black/50 backdrop-blur-sm">
      <div
        className={clsx(
          'w-full max-w-lg rounded-t-2xl bg-white shadow-2xl transition-all duration-300 ease-out',
          'slide-in-from-bottom-4 animate-in'
        )}
      >
        {/* 表单头部 */}
        <div className="flex items-center justify-between border-gray-100 border-b px-6 py-4">
          <h2 className="font-semibold text-gray-900 text-lg">
            {isEdit ? '编辑活动' : '新增活动'}
          </h2>
          <button
            className="flex h-8 w-8 items-center justify-center rounded-full text-gray-400 transition-colors hover:bg-gray-100 hover:text-gray-600"
            onClick={handleClose}
            type="button"
          >
            <X size={20} />
          </button>
        </div>

        {/* 表单内容 */}
        <div className="max-h-[70vh] overflow-y-auto">
          <Form form={form} layout="vertical" requiredMarkStyle="none">
            <Form.Item
              label="活动名称"
              name="title"
              rules={[
                { required: true, message: '请输入活动名称' },
                { max: 100, message: '活动名称不能超过100个字符' },
              ]}
            >
              <Input className="rounded-lg" placeholder="请输入活动名称" />
            </Form.Item>

            <Form.Item
              label="活动目标"
              name="objective"
              rules={[
                { required: true, message: '请输入活动目标' },
                { max: 500, message: '活动目标不能超过500个字符' },
              ]}
            >
              <TextArea
                className="rounded-lg"
                maxLength={500}
                placeholder="请描述活动的具体目标和期望达成的效果"
                rows={3}
                showCount
              />
            </Form.Item>

            <Form.Item
              label="活动说明"
              name="notes"
              rules={[
                { required: true, message: '请输入活动说明' },
                { max: 1000, message: '活动说明不能超过1000个字符' },
              ]}
            >
              <TextArea
                className="rounded-lg"
                maxLength={1000}
                placeholder="请详细描述活动的具体内容、操作步骤和注意事项"
                rows={4}
                showCount
              />
            </Form.Item>
          </Form>
        </div>

        {/* 表单底部按钮 */}
        <div className="sticky bottom-0 flex gap-3 border-gray-100 border-t bg-white/80 px-6 py-4 backdrop-blur supports-[backdrop-filter]:bg-white/60">
          <Button className="flex-1" color="default" onClick={handleClose}>
            取消
          </Button>
          <Button
            className="flex-1"
            color="primary"
            loading={loading}
            onClick={handleSubmit}
          >
            {isEdit ? '更新' : '创建'}
          </Button>
        </div>
      </div>
    </div>
  );
};
