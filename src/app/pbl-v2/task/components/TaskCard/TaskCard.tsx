'use client';

import { Badge } from 'antd-mobile';
import clsx from 'clsx';
import {
  ChevronDown,
  ChevronRight,
  GripVertical,
  Play,
  Square,
  SquarePen,
  Trash2,
} from 'lucide-react';
import type React from 'react';
import { useState } from 'react';
import type { TaskItem } from '../../types';

interface TaskCardProps {
  task: TaskItem;
  isDragging?: boolean;
  dragOverlay?: boolean;
  handle?: boolean;
  listeners?: Record<string, (event: Event) => void>;
  style?: React.CSSProperties;
  transform?: { x: number; y: number; scaleX?: number; scaleY?: number };
  transition?: string;
  onEdit?: (task: TaskItem) => void;
  onDelete?: (taskId: string) => void;
  expanded?: boolean;
  onToggleExpand?: (taskId: string) => void;
  onStatusChange?: (taskId: string, newStatus: TaskItem['status']) => void;
}

const statusBadgeColors = {
  未开始: '#718096',
  进行中: '#16a34a',
  已完成: '#10B981',
};

export const TaskCard: React.FC<TaskCardProps> = ({
  task,
  isDragging = false,
  dragOverlay = false,
  handle = false,
  listeners,
  style,
  transform,
  transition,
  onEdit,
  onDelete,
  expanded,
  onToggleExpand,
  onStatusChange,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // 使用外部控制的展开状态，如果没有提供则使用内部状态
  const effectiveExpanded = expanded !== undefined ? expanded : isExpanded;

  const toggleExpanded = () => {
    if (onToggleExpand) {
      onToggleExpand(task.id);
    } else {
      setIsExpanded(!isExpanded);
    }
  };

  return (
    <div
      className={clsx(
        'rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-300 ease-out',
        'hover:-translate-y-1 hover:border-gray-300 hover:shadow-lg',
        isDragging && 'rotate-2 scale-105 opacity-60 shadow-xl',
        dragOverlay &&
          'rotate-1 scale-105 border-blue-400 shadow-2xl ring-2 ring-blue-200'
      )}
      style={{
        ...style,
        transform: transform
          ? `translate3d(${Math.round(transform.x)}px, ${Math.round(transform.y)}px, 0) ${transform.scaleX ? `scaleX(${transform.scaleX})` : ''} ${transform.scaleY ? `scaleY(${transform.scaleY})` : ''}`
          : undefined,
        transition,
      }}
    >
      {/* 卡片头部 - 始终可见 */}
      <button
        className="flex w-full cursor-pointer items-center gap-1 px-2 py-4 text-left "
        onClick={toggleExpanded}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            toggleExpanded();
          }
        }}
        type="button"
      >
        {/* 拖拽手柄 */}
        {handle && (
          <div
            className="flex h-6 w-6 cursor-grab items-center justify-center text-gray-400 hover:text-gray-600 active:cursor-grabbing"
            {...listeners}
          >
            <GripVertical size={16} />
          </div>
        )}

        {/* 展开/折叠图标 */}
        <div className="flex h-6 w-6 items-center justify-center text-gray-500">
          {effectiveExpanded ? (
            <ChevronDown size={16} />
          ) : (
            <ChevronRight size={16} />
          )}
        </div>

        {/* 任务标题 */}
        <div className="min-w-0 flex-1">
          <h3 className="font-medium text-base text-gray-900">{task.title}</h3>
        </div>

        {/* 状态标签 */}
        <Badge
          className="flex-shrink-0 p-1"
          color={statusBadgeColors[task.status]}
          content={task.status}
        />
      </button>

      {/* 展开内容 */}
      {effectiveExpanded && (
        <div className="slide-in-from-top-2 animate-in border-gray-100 border-t px-5 pb-5 duration-300">
          <div className="space-y-5 pt-5">
            {/* 任务目标 */}
            <div className="space-y-2">
              <h4 className="flex items-center font-semibold text-gray-800 text-sm">
                <div className="mr-2 h-1.5 w-1.5 rounded-full bg-blue-500" />
                任务目标
              </h4>
              <p className="pl-4 text-gray-700 text-sm leading-relaxed">
                {task.objective}
              </p>
            </div>

            {/* 说明 */}
            <div className="space-y-2">
              <h4 className="flex items-center font-semibold text-gray-800 text-sm">
                <div className="mr-2 h-1.5 w-1.5 rounded-full bg-green-500" />
                备注
              </h4>
              <p className="pl-4 text-gray-700 text-sm leading-relaxed">
                {task.notes}
              </p>
            </div>

            {/* 底部操作区域 */}
            <div className="space-y-4 border-gray-100 border-t pt-4">
              {/* 状态显示 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {/* 状态控制按钮 */}
                  {onStatusChange && (
                    <div className="flex justify-center">
                      {task.status === '未开始' && (
                        <button
                          className="flex w-full items-center justify-center gap-1 rounded-full bg-green-500 px-4 py-1 font-medium text-white transition-all duration-200 hover:bg-green-600 active:scale-95"
                          onClick={(e) => {
                            e.stopPropagation();
                            onStatusChange(task.id, '进行中');
                          }}
                          type="button"
                        >
                          <Play size={16} />
                          立即开始
                        </button>
                      )}

                      {task.status === '进行中' && (
                        <button
                          className="flex w-full items-center justify-center gap-1 rounded-full bg-blue-500 px-4 py-1 font-medium text-white transition-all duration-200 hover:bg-blue-600 active:scale-95"
                          onClick={(e) => {
                            e.stopPropagation();
                            onStatusChange(task.id, '已完成');
                          }}
                          type="button"
                        >
                          <Square size={16} />
                          结束任务
                        </button>
                      )}
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-3">
                  {/* 操作按钮 */}
                  <div className="flex items-center gap-1">
                    {onEdit && (
                      <button
                        className="flex items-center gap-1 rounded-full bg-blue-100 px-3 py-1 text-blue-600 transition-all duration-200 hover:bg-blue-50 hover:text-blue-600"
                        onClick={(e) => {
                          e.stopPropagation();
                          onEdit(task);
                        }}
                        title="编辑任务"
                        type="button"
                      >
                        <SquarePen size={12} />
                        <span className="text-xs">编辑</span>
                      </button>
                    )}

                    {onDelete && (
                      <button
                        className="flex h-8 w-8 items-center justify-center rounded-lg text-red-500 transition-all duration-200 hover:bg-red-50 hover:text-red-600"
                        onClick={(e) => {
                          e.stopPropagation();
                          onDelete(task.id);
                        }}
                        title="删除任务"
                        type="button"
                      >
                        <Trash2 size={14} />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
