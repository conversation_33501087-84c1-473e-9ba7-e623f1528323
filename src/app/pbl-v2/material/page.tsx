'use client';

import { Tabs, Toast } from 'antd-mobile';
import clsx from 'clsx';
import {
  AudioLines as AudioIcon,
  FileWarning as FileIcon,
  Image as ImageIcon,
  Play as PlayIcon,
  Plus as PlusIcon,
  Video as VideoIcon,
} from 'lucide-react';
import type React from 'react';
import { useMemo, useState } from 'react';

// 类型定义
type MaterialType = 'image' | 'video' | 'audio';

type Material = {
  id: string;
  type: MaterialType;
  name: string;
  size: number; // bytes
  uploader: {
    name: string;
    avatar: string;
  };
  uploadedAt: string; // ISO
  thumbnail?: string; // 图片/视频缩略图
};

// 工具函数
const formatSize = (bytes: number) => {
  if (bytes < 1024) {
    return `${bytes} B`;
  }
  if (bytes < 1024 * 1024) {
    return `${(bytes / 1024).toFixed(1)} KB`;
  }
  if (bytes < 1024 * 1024 * 1024) {
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  }
  return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;
};

const formatDate = (iso: string) => {
  const d = new Date(iso);
  const y = d.getFullYear();
  const m = `${d.getMonth() + 1}`.padStart(2, '0');
  const day = `${d.getDate()}`.padStart(2, '0');
  const hh = `${d.getHours()}`.padStart(2, '0');
  const mm = `${d.getMinutes()}`.padStart(2, '0');
  return `${y}-${m}-${day} ${hh}:${mm}`;
};

// 使用稳定可用的图片占位服务，避免 404
// picsum 提供稳定的随机图，使用 seed 保持 SSR/CSR 一致
const placeholderImg = (seed: string, w = 320, h = 220) =>
  `https://picsum.photos/seed/${encodeURIComponent(seed)}/${w}/${h}`;

// 头像使用 i.pravatar，seed 保持一致性
const placeholderAvatar = (seed: string, size = 80) =>
  `https://i.pravatar.cc/${size}?u=${encodeURIComponent(seed)}`;

// 模拟数据
const mockMaterials: Material[] = [
  {
    id: 'mat-001',
    type: 'image',
    name: '户外观察-蝴蝶翅膀纹理.jpg',
    size: 1_548_221,
    uploader: { name: '王老师', avatar: placeholderAvatar('teacher-wang', 80) },
    uploadedAt: '2025-07-20T09:21:00.000Z',
    thumbnail: placeholderImg('butterfly-observe-1234', 320, 220),
  },
  {
    id: 'mat-002',
    type: 'video',
    name: '角落探索-积木搭建.mp4',
    size: 48_225_300,
    uploader: { name: '李老师', avatar: placeholderAvatar('teacher-li', 80) },
    uploadedAt: '2025-07-21T13:10:00.000Z',
    thumbnail: placeholderImg('block-building-5678', 320, 220),
  },
  {
    id: 'mat-003',
    type: 'audio',
    name: '晨间谈话-主题讨论.m4a',
    size: 8_200_000,
    uploader: { name: '周老师', avatar: placeholderAvatar('teacher-zhou', 80) },
    uploadedAt: '2025-07-22T08:02:00.000Z',
  },
  {
    id: 'mat-004',
    type: 'image',
    name: '种植角-幼苗生长记录.png',
    size: 2_322_111,
    uploader: { name: '王老师', avatar: placeholderAvatar('teacher-wang', 80) },
    uploadedAt: '2025-07-18T10:45:00.000Z',
    thumbnail: placeholderImg('plant-seedling-9012', 320, 220),
  },
  {
    id: 'mat-005',
    type: 'video',
    name: '音乐活动-节奏律动.mov',
    size: 96_884_112,
    uploader: { name: '赵老师', avatar: placeholderAvatar('teacher-zhao', 80) },
    uploadedAt: '2025-07-25T15:30:00.000Z',
    thumbnail: placeholderImg('rhythm-music-3456', 320, 220),
  },
  {
    id: 'mat-006',
    type: 'audio',
    name: '区域观察-角色扮演对话.mp3',
    size: 5_784_331,
    uploader: { name: '孙老师', avatar: placeholderAvatar('teacher-sun', 80) },
    uploadedAt: '2025-07-26T07:55:00.000Z',
  },
  {
    id: 'mat-007',
    type: 'image',
    name: '美工角-颜色混合尝试.jpeg',
    size: 1_112_901,
    uploader: { name: '李老师', avatar: placeholderAvatar('teacher-li', 80) },
    uploadedAt: '2025-07-17T12:10:00.000Z',
    thumbnail: placeholderImg('color-mix-7890', 320, 220),
  },
  {
    id: 'mat-008',
    type: 'video',
    name: '户外活动-跳远练习.mp4',
    size: 58_004_110,
    uploader: { name: '王老师', avatar: placeholderAvatar('teacher-wang', 80) },
    uploadedAt: '2025-07-28T09:12:00.000Z',
    // 故意无缩略图，测试占位
  },
  {
    id: 'mat-009',
    type: 'image',
    name: '科学角-磁铁吸引现象.jpg',
    size: 1_889_000,
    uploader: { name: '周老师', avatar: placeholderAvatar('teacher-zhou', 80) },
    uploadedAt: '2025-07-27T16:02:00.000Z',
    thumbnail: placeholderImg('magnet-science-1122', 320, 220),
  },
  {
    id: 'mat-010',
    type: 'audio',
    name: '午后阅读-故事复述.aac',
    size: 4_220_220,
    uploader: { name: '赵老师', avatar: placeholderAvatar('teacher-zhao', 80) },
    uploadedAt: '2025-07-21T11:22:00.000Z',
  },
  {
    id: 'mat-011',
    type: 'image',
    name: '自然角-叶片纹理描摹.png',
    size: 2_014_644,
    uploader: { name: '孙老师', avatar: placeholderAvatar('teacher-sun', 80) },
    uploadedAt: '2025-07-23T10:01:00.000Z',
    thumbnail: placeholderImg('leaf-texture-4321', 320, 220),
  },
  {
    id: 'mat-012',
    type: 'video',
    name: '体育活动-滚翻练习.mp4',
    size: 71_540_998,
    uploader: { name: '李老师', avatar: placeholderAvatar('teacher-li', 80) },
    uploadedAt: '2025-07-29T14:40:00.000Z',
    thumbnail: placeholderImg('roll-practice-6543', 320, 220),
  },
];

const Thumb: React.FC<{ type: MaterialType; thumbnail?: string }> = ({
  type,
  thumbnail,
}) => {
  const base =
    'relative w-24 h-16 rounded-xl ring-1 ring-black/5 overflow-hidden flex items-center justify-center shrink-0 bg-gradient-to-b from-white to-slate-50';
  if (type === 'image' || type === 'video') {
    if (thumbnail) {
      return (
        <div className={base}>
          <img
            alt="thumbnail"
            className="h-full w-full object-cover transition-transform duration-300 ease-out group-hover:scale-[1.02]"
            loading="lazy"
            src={thumbnail}
          />
          {type === 'video' ? (
            <span className="absolute inset-0">
              <span className="absolute inset-0 bg-black/35 transition-opacity duration-200 group-hover:bg-black/40" />
              <span className="absolute inset-0 flex items-center justify-center">
                <PlayIcon className="translate-x-[1px] text-white" size={18} />
              </span>
            </span>
          ) : null}
        </div>
      );
    }
    return (
      <div className={clsx(base, 'text-slate-400')}>
        {type === 'image' ? <ImageIcon size={28} /> : <VideoIcon size={28} />}
      </div>
    );
  }
  return (
    <div className={clsx(base, 'text-slate-400')}>
      <AudioIcon size={28} />
    </div>
  );
};

const EmptyBlock: React.FC<{ message?: string }> = ({
  message = '没有符合条件的素材',
}) => {
  return (
    <div className="flex items-center justify-center py-20 text-slate-500">
      <div className="flex flex-col items-center gap-3">
        <FileIcon className="text-slate-300" size={40} />
        <p className="text-sm">{message}</p>
      </div>
    </div>
  );
};

export default function MaterialPage() {
  const [activeKey, setActiveKey] = useState<'all' | MaterialType>('all');
  const [keyword] = useState('');

  const data = useMemo(() => mockMaterials, []);
  const filtered = useMemo(() => {
    const k = keyword.trim();
    return data.filter((m) => {
      const typeOK = activeKey === 'all' ? true : m.type === activeKey;
      const kwOK = k ? m.name.toLowerCase().includes(k.toLowerCase()) : true;
      return typeOK && kwOK;
    });
  }, [data, activeKey, keyword]);

  const onItemClick = (item: Material) => {
    Toast.show({
      content: `已选择：${item.name}`,
      duration: 800,
    });
  };

  return (
    <div className="min-h-screen bg-slate-50 px-4">
      <div className="-mx-4 bg-white px-4">
        <div className="py-4">
          <h1 className="font-bold text-slate-900 text-xl">
            幼儿园生态探索项目
          </h1>
        </div>
      </div>

      <div className="-mx-4 sticky top-0 z-10 bg-white/70">
        <Tabs
          activeKey={activeKey}
          className="[&_.adm-tabs-tab-active]:text-slate-900 [&_.adm-tabs-tab]:text-slate-600"
          onChange={(k) => setActiveKey(k as 'all' | MaterialType)}
          stretch
        >
          <Tabs.Tab key="all" title="全部" />
          <Tabs.Tab key="image" title="图片" />
          <Tabs.Tab key="video" title="视频" />
          <Tabs.Tab key="audio" title="音频" />
        </Tabs>
      </div>

      <ul className="mt-3 space-y-3">
        {filtered.length === 0 ? (
          <li>
            <EmptyBlock />
          </li>
        ) : (
          filtered.map((item) => (
            <li key={item.id}>
              <button
                className={clsx(
                  'group w-full text-left',
                  'rounded-2xl border border-slate-100 bg-white',
                  'hover:border-slate-200 hover:shadow-[0_6px_24px_-12px_rgba(15,23,42,0.18)]',
                  'transition-all duration-300 ease-out'
                )}
                onClick={() => onItemClick(item)}
                type="button"
              >
                <div className="flex items-center gap-3.5 p-3.5">
                  <Thumb thumbnail={item.thumbnail} type={item.type} />

                  <div className="min-w-0 flex-1">
                    <div className="flex items-start gap-2">
                      <p className="truncate font-medium text-slate-900 text-sm">
                        {item.name}
                      </p>
                    </div>

                    <div className="mt-1 flex items-center gap-2 text-slate-500 text-xs">
                      <span>{formatSize(item.size)}</span>
                      <span className="h-1 w-1 rounded-full bg-slate-300" />
                      <span>{formatDate(item.uploadedAt)}</span>
                    </div>

                    <div className="mt-2 flex items-center gap-2">
                      <img
                        alt={item.uploader.name}
                        className="h-5 w-5 rounded-full object-cover ring-1 ring-black/5"
                        loading="lazy"
                        src={item.uploader.avatar}
                      />
                      <span className="text-slate-600 text-xs">
                        {item.uploader.name}
                      </span>
                    </div>
                  </div>
                </div>
              </button>
            </li>
          ))
        )}
      </ul>

      <div className="h-12" />
      {/* 右下角悬浮“新增素材”按钮，跳转到 /pbl/material/create */}
      <div className="pointer-events-none fixed inset-x-0 bottom-0 z-40">
        <div className="mx-auto max-w-3xl px-4 pb-safe">
          <div className="pointer-events-auto mb-4 flex justify-end">
            <a
              aria-label="新增素材"
              className={clsx(
                'group inline-flex items-center gap-2 rounded-full',
                // 深色渐变底
                'bg-gradient-to-br from-indigo-500 to-blue-700 text-white',
                // 轮廓与阴影
                'border border-white/10 shadow-[0_12px_36px_-14px_rgba(15,23,42,0.55)]',
                // 内边距与字号
                'px-4 py-2 font-medium text-sm',
                // 细腻高光边与玻璃态
                'ring-1 ring-white/10 backdrop-blur supports-[backdrop-filter]:bg-opacity-90',
                // 交互反馈
                'transition hover:brightness-[1.05] active:scale-[0.98]'
              )}
              href="/pbl/material/create"
            >
              <span className="relative inline-flex h-5 w-5 items-center justify-center">
                {/* lucide-react 风格加号路径 */}
                <PlusIcon />
              </span>
              <span>新增素材</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
