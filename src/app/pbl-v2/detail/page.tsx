'use client';

import clsx from 'clsx';
import {
  BookOpen,
  Calendar,
  CheckCircle,
  ChevronDown,
  Clock,
  Edit3,
  MessageSquare,
  Pause,
  Play,
  Target,
  Users,
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useMemo, useState } from 'react';
import {
  type Activity,
  type Child,
  mockActivities,
  mockChildren,
  mockProjects,
  type Project,
  type TimelineEvent,
} from '../../../data/mockData';
import QuickActions from './components/QuickActions';
import ProjectProgress from './components/ProjectProgress';
import ProjectRecords from './components/ProjectRecords';
import DataOverview from './components/DataOverview';

function ProjectHomePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const projectId = searchParams?.get('projectId') || '';

  // 展开状态管理
  const [isDetailsExpanded, setIsDetailsExpanded] = useState(false);

  // 获取项目数据
  const project = useMemo(() => {
    return mockProjects.find((p: Project) => p.id === projectId);
  }, [projectId]);

  // 获取项目相关活动
  const projectActivities = useMemo(() => {
    return mockActivities
      .filter((activity: Activity) => activity.projectId === projectId)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 3);
  }, [projectId]);

  // 获取参与的儿童
  const participatingChildren = useMemo(() => {
    return mockChildren.filter(
      (child: Child) => child.classId === project?.classId
    );
  }, [project?.classId]);

  if (!project) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-slate-50 via-white to-slate-50">
        <div className="text-center">
          <h2 className="mb-2 font-semibold text-gray-900 text-xl">
            项目未找到
          </h2>
          <p className="mb-4 text-gray-600">请检查项目ID是否正确</p>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: Project['status']) => {
    switch (status) {
      case 'active':
        return 'bg-emerald-100 text-emerald-800 ring-1 ring-inset ring-emerald-200';
      case 'completed':
        return 'bg-blue-100 text-blue-800 ring-1 ring-inset ring-blue-200';
      case 'paused':
        return 'bg-amber-100 text-amber-800 ring-1 ring-inset ring-amber-200';
      case 'draft':
        return 'bg-gray-100 text-gray-800 ring-1 ring-inset ring-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 ring-1 ring-inset ring-gray-200';
    }
  };

  const getStatusText = (status: Project['status']) => {
    switch (status) {
      case 'active':
        return '进行中';
      case 'completed':
        return '已完成';
      case 'paused':
        return '已暂停';
      case 'draft':
        return '草稿';
      default:
        return '未知';
    }
  };

  const getStatusIcon = (status: Project['status']) => {
    switch (status) {
      case 'active':
        return <Play className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'paused':
        return <Pause className="h-4 w-4" />;
      case 'draft':
        return <Edit3 className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
    });
  };

  
  const progressWidth = `${project.progress}%`;

  return (
    <div className="min-h-screen bg-slate-50">
      {/* 顶部英雄区 */}
      <div className="relative mb-4 overflow-hidden bg-white/70 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        {/* 背景渐变与装饰 */}
        <div className="pointer-events-none absolute inset-0">
          <div className="-left-24 -top-24 absolute h-72 w-72 rounded-full bg-gradient-to-br from-blue-400/20 via-purple-400/10 to-pink-400/10 blur-3xl" />
          {/* <div className="-bottom-24 -right-24 absolute h-64 w-64 rounded-full bg-gradient-to-tr from-cyan-400/20 via-indigo-400/10 to-fuchsia-400/20 blur-3xl" /> */}
        </div>

        <div className="relative px-6 pt-4">
          <div
            className={clsx(
              'absolute top-4 right-4 inline-flex items-center rounded-full px-3 py-1 font-medium text-xs shadow-sm ring-1',
              getStatusColor(project.status)
            )}
          >
            <span className="mr-1 opacity-90">
              {getStatusIcon(project.status)}
            </span>
            <span>{getStatusText(project.status)}</span>
          </div>

          {/* 标题与元信息 */}
          <div className="pb-4">
            <h1 className="mb-4 font-bold text-gray-900 text-xl tracking-tight">
              {project.title}
            </h1>

            <div className="mb-2">
              <div className="space-y-3">
                <div>
                  <p className="text-slate-700 text-sm leading-relaxed">
                    {project.description}
                    {/* 展开按钮 */}
                    <button
                      className="ml-2 inline-flex items-center transition-all"
                      onClick={() => setIsDetailsExpanded(!isDetailsExpanded)}
                      type="button"
                    >
                      <span className="text-slate-500 text-xs hover:text-slate-800">
                        更多
                      </span>
                      <ChevronDown
                        className={clsx(
                          'h-4 w-4 text-slate-500 transition-transform duration-200',
                          isDetailsExpanded && 'rotate-180'
                        )}
                      />
                    </button>
                  </p>
                </div>

                {/* 展开的详细信息 */}
                {isDetailsExpanded && (
                  <div className="slide-in-from-top-2 animate-in duration-200">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <h3 className="mb-2 flex items-center gap-2 font-medium text-slate-900 text-sm">
                          <MessageSquare className="h-4 w-4 text-purple-500" />
                          驱动性问题
                        </h3>
                        <p className="text-slate-700 text-sm leading-relaxed">
                          {project.drivingQuestion}
                        </p>
                      </div>

                      <div>
                        <h3 className="mb-2 flex items-center gap-2 font-medium text-slate-900 text-sm">
                          <Target className="h-4 w-4 text-emerald-500" />
                          学习目标
                        </h3>
                        <div className="space-y-1">
                          {project.learningGoals.split('\n').map((goal) => (
                            <p
                              className="text-slate-700 text-sm leading-relaxed"
                              key={goal}
                            >
                              {goal}
                            </p>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 标签 */}
            <div className="mb-4 flex flex-wrap gap-2">
              {project.tags.map((tag: string) => (
                <span
                  className="inline-flex items-center rounded-full bg-white px-2 py-1 text-stone-700 text-xs ring-1 ring-sky-100"
                  key={tag}
                >
                  {tag}
                </span>
              ))}
            </div>

            {/* 基本信息 */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center text-gray-600">
                <Calendar className="mr-2 h-4 w-4" />
                <span>
                  {formatDate(project.startDate)} -{' '}
                  {formatDate(project.endDate)}
                </span>
              </div>
              <div className="flex items-center text-gray-600">
                <Users className="mr-2 h-4 w-4" />
                <span>
                  {project.className} · {project.childrenCount}人
                </span>
              </div>
            </div>
          </div>

          {/* 进度 */}
          <div className="pb-4">
            <div className="mb-2 flex items-center justify-between">
              <span className="font-medium text-gray-700 text-sm">
                项目进度
              </span>
              <span className="text-gray-500 text-sm">{project.progress}%</span>
            </div>
            <div className="relative h-2 w-full rounded-full bg-gray-100">
              <div
                className="h-full rounded-full bg-gradient-to-r from-indigo-300 via-indigo-500 to-purple-600 transition-all duration-500"
                style={{ width: progressWidth }}
              />
              <div className="pointer-events-none absolute inset-0 animate-pulse rounded-full bg-gradient-to-r from-white/0 via-white/20 to-white/0" />
            </div>
          </div>
        </div>
      </div>

      <QuickActions />
      
      <ProjectProgress timeline={project.timeline} formatDate={formatDate} />
      
      <ProjectRecords activities={projectActivities} formatDate={formatDate} />

      {/* 成员管理 */}
      <div className="hidden px-4 py-4">
        <div className="rounded-2xl bg-white shadow-sm ring-1 ring-gray-100">
          <div className="border-gray-100 border-b p-4">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-base text-gray-900">
                项目成员
              </h3>
              <span className="text-gray-500 text-sm">
                {project.groups.length} 个小组
              </span>
            </div>
          </div>

          <div className=" p-4">
            <div className="space-y-4">
              {project.groups.map((group) => (
                <div
                  className="rounded-xl border border-gray-200 p-3 transition-colors hover:bg-gray-50"
                  key={group.id}
                >
                  <div className="mb-3 flex items-center justify-between">
                    <div className="flex items-center">
                      <span
                        className="mr-2 inline-block h-3 w-3 rounded-full ring-2 ring-white"
                        style={{ backgroundColor: group.color }}
                      />
                      <span className="font-medium text-gray-900 text-sm">
                        {group.name}
                      </span>
                    </div>
                    <span className="text-gray-500 text-xs">
                      {group.children.length} 名成员
                    </span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {group.children.map((childId) => {
                      const child = participatingChildren.find(
                        (c) => c.id === childId
                      );
                      if (!child) {
                        return null;
                      }
                      return (
                        <div
                          className="hover:-translate-y-0.5 flex items-center gap-2 rounded-lg bg-gray-50 px-2 py-1 ring-1 ring-gray-200 transition-all"
                          key={child.id}
                        >
                          <img
                            alt={child.name}
                            className="h-6 w-6 rounded-full object-cover ring-1 ring-gray-200"
                            src={child.avatar}
                          />
                          <span className="text-gray-700 text-xs">
                            {child.name}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      <DataOverview project={project} />
    </div>
  );
}

export default ProjectHomePage;
