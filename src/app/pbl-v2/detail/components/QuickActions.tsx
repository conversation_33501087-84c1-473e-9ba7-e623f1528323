'use client';

import { CalendarPlus, Camera, FileText, FolderOpen } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function QuickActions() {
  const router = useRouter();

  return (
    <div className="mb-4 px-4">
      <div className="rounded-2xl bg-white p-4 shadow-sm ring-1 ring-gray-100">
        <h3 className="mb-4 font-semibold text-base text-gray-900">快捷操作</h3>
        <div className="grid grid-cols-4 gap-4">
          <button
            className="group hover:-translate-y-0.5 flex flex-col items-center gap-2 transition-all active:translate-y-0 active:opacity-90"
            onClick={() => {
              router.push('/pbl/record/create');
            }}
            type="button"
          >
            <div className="flex h-12 w-12 items-center justify-center rounded-full border border-blue-300 bg-blue-50 shadow-[0_1px_0_rgba(0,0,0,0.02)] transition-all group-hover:border-blue-400 group-hover:bg-blue-100 group-hover:shadow-md">
              <Camera className="h-5 w-5 text-blue-600 transition-transform group-active:scale-95" />
            </div>
            <span className="font-medium text-gray-700 text-xs">添加记录</span>
          </button>

          <button
            className="group hover:-translate-y-0.5 flex flex-col items-center gap-2 transition-all active:translate-y-0 active:opacity-90"
            onClick={() => {
              // router.push('/pbl-v2/report');
              window.location.href = 'http://192.168.9.24:3000/editor';
            }}
            type="button"
          >
            <div className="flex h-12 w-12 items-center justify-center rounded-full border border-purple-300 bg-purple-50 shadow-[0_1px_0_rgba(0,0,0,0.02)] transition-all group-hover:border-purple-400 group-hover:bg-purple-100 group-hover:shadow-md">
              <FileText className="h-5 w-5 text-purple-600 transition-transform group-active:scale-95" />
            </div>
            <span className="font-medium text-gray-700 text-xs">项目报告</span>
          </button>
          <button
            className="group hover:-translate-y-0.5 flex flex-col items-center gap-2 transition-all active:translate-y-0 active:opacity-90"
            onClick={() => {
              router.push('/pbl-v2/task');
            }}
            type="button"
          >
            <div className="flex h-12 w-12 items-center justify-center rounded-full border border-emerald-300 bg-emerald-50 shadow-[0_1px_0_rgba(0,0,0,0.02)] transition-all group-hover:border-emerald-400 group-hover:bg-emerald-100 group-hover:shadow-md">
              <CalendarPlus className="h-5 w-5 text-emerald-600 transition-transform group-active:scale-95" />
            </div>
            <span className="font-medium text-gray-700 text-xs">活动流程</span>
          </button>
          <button
            className="group hover:-translate-y-0.5 flex flex-col items-center gap-2 transition-all active:translate-y-0 active:opacity-90"
            onClick={() => {
              router.push('/pbl-v2/material');
            }}
            type="button"
          >
            <div className="flex h-12 w-12 items-center justify-center rounded-full border border-orange-300 bg-orange-50 shadow-[0_1px_0_rgba(0,0,0,0.02)] transition-all group-hover:border-orange-400 group-hover:bg-orange-100 group-hover:shadow-md">
              <FolderOpen className="h-5 w-5 text-orange-600 transition-transform group-active:scale-95" />
            </div>
            <span className="font-medium text-gray-700 text-xs">相关素材</span>
          </button>
        </div>
      </div>
    </div>
  );
}
