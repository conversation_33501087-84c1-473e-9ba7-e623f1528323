'use client';

import type { TimelineEvent } from '../../../../data/mockData';

interface ProjectProgressProps {
  timeline: TimelineEvent[];
  formatDate: (dateString: string) => string;
}

export default function ProjectProgress({
  timeline,
  formatDate,
}: ProjectProgressProps) {
  return (
    <div className="mb-4 px-4">
      <div className="rounded-2xl bg-white shadow-sm ring-1 ring-gray-100">
        <div className="border-gray-100 border-b p-4">
          <h3 className="font-semibold text-base text-gray-900">项目进度</h3>
        </div>
        <div className="flex w-full flex-col items-start p-4 ">
          {timeline.map((event: TimelineEvent, index: number) => {
            const isLast = index >= timeline.length - 1;
            return (
              <div className="group flex gap-x-3" key={event.id}>
                <div className="relative">
                  {!isLast && (
                    <div className="-translate-x-1/2 absolute top-0 left-1/2 h-full w-0.5 bg-slate-200" />
                  )}
                  <span className="relative z-10 grid h-3 w-3 place-items-center rounded-full bg-green-500 text-slate-800" />
                </div>
                <div className="-translate-y-1.5 pb-6 text-slate-600 data-[orientation=horizontal]:py-4">
                  <div className="flex justify-between font-bold text-slate-800 text-sm antialiased dark:text-white">
                    <span> {formatDate(event.date)}</span>
                    <span className="text-gray-500 text-xs">10 条记录</span>
                  </div>
                  <small className="mt-5 font-sans text-slate-600 text-sm antialiased">
                    {event.description}
                  </small>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
