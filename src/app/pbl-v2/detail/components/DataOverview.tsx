'use client';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'lucide-react';
import { type Project } from '../../../../data/mockData';

interface DataOverviewProps {
  project: Project;
}

export default function DataOverview({ project }: DataOverviewProps) {
  return (
    <div className="px-4 pb-4">
      <div className="rounded-2xl bg-white shadow-sm ring-1 ring-gray-100">
        <div className="border-gray-100 border-b p-4">
          <h3 className="font-semibold text-base text-gray-900">数据概览</h3>
        </div>

        <div className="p-4">
          <div className="grid grid-cols-2 gap-4">
            {/* <div className="rounded-xl bg-gradient-to-br from-blue-50 to-indigo-50 p-3 ring-1 ring-blue-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-blue-700 text-xs">
                    活动完成数
                  </p>
                  <p className="font-bold text-blue-900 text-lg">
                    {project.completedActivities}/{project.activitiesCount}
                  </p>
                </div>
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100">
                  <CheckCircle className="h-4 w-4 text-blue-600" />
                </div>
              </div>
            </div> */}

            <div className="rounded-xl bg-gradient-to-br from-emerald-50 to-green-50 p-3 ring-1 ring-emerald-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-emerald-700 text-xs">
                    幼儿参与度
                  </p>
                  <p className="font-bold text-emerald-900 text-lg">
                    {Math.round(
                      (project.completedActivities /
                        project.activitiesCount) *
                        100
                    )}
                    %
                  </p>
                </div>
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-emerald-100">
                  <UserCheck className="h-4 w-4 text-emerald-600" />
                </div>
              </div>
            </div>

            <div className="rounded-xl bg-gradient-to-br from-purple-50 to-fuchsia-50 p-3 ring-1 ring-purple-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-purple-700 text-xs">
                    观察记录数
                  </p>
                  <p className="font-bold text-lg text-purple-900">
                    {project.observationCount}
                  </p>
                </div>
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-purple-100">
                  <Eye className="h-4 w-4 text-purple-600" />
                </div>
              </div>
            </div>

            {/* <div className="rounded-xl bg-gradient-to-br from-orange-50 to-amber-50 p-3 ring-1 ring-orange-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-orange-700 text-xs">
                    家长参与率
                  </p>
                  <p className="font-bold text-lg text-orange-900">
                    {project.parentParticipationRate}%
                  </p>
                </div>
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-orange-100">
                  <Heart className="h-4 w-4 text-orange-600" />
                </div>
              </div>
            </div> */}
          </div>
        </div>
      </div>
    </div>
  );
}