'use client';

import {
  Activity as ActivityIcon,
  Camera,
  Image,
  Video,
  MessageSquare,
  Target,
  Star,
  BookOpen,
} from 'lucide-react';
import { type Activity } from '../../../../data/mockData';

interface ProjectRecordsProps {
  activities: Activity[];
  formatDate: (dateString: string) => string;
}

export default function ProjectRecords({ activities, formatDate }: ProjectRecordsProps) {
  const getActivityTypeIcon = (type: Activity['type']) => {
    switch (type) {
      case 'observation':
        return <Target className="h-4 w-4" />;
      case 'creation':
        return <Star className="h-4 w-4" />;
      case 'discussion':
        return <MessageSquare className="h-4 w-4" />;
      case 'experiment':
        return <ActivityIcon className="h-4 w-4" />;
      default:
        return <BookOpen className="h-4 w-4" />;
    }
  };

  const getActivityTypeText = (type: Activity['type']) => {
    switch (type) {
      case 'observation':
        return '观察记录';
      case 'creation':
        return '创作活动';
      case 'discussion':
        return '讨论交流';
      case 'experiment':
        return '实验探索';
      default:
        return '其他活动';
    }
  };

  return (
    <div className="mb-4 px-4">
      <div className="rounded-2xl bg-white shadow-sm ring-1 ring-gray-100">
        <div className="border-gray-100 border-b p-4">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-base text-gray-900">
              项目记录
            </h3>
            <div className="pl-2 font-normal text-gray-600 text-sm">
              查看全部
            </div>
          </div>
        </div>

        {activities.length > 0 ? (
          <div className="divide-y divide-gray-100">
            {activities.map((activity: Activity) => (
              <div className="p-4" key={activity.id}>
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0">
                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-50 text-blue-600 ring-1 ring-blue-100">
                      {getActivityTypeIcon(activity.type)}
                    </div>
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="mb-1 flex items-center justify-between">
                      <h4 className="font-medium text-gray-900 text-sm">
                        {activity.title}
                      </h4>
                      <span className="text-gray-500 text-xs">
                        {formatDate(activity.date)}
                      </span>
                    </div>
                    <p className="mb-2 text-gray-600 text-sm">
                      {activity.description}
                    </p>

                    {/* 标签与参与 */}
                    <div className="mb-2 flex items-center gap-2">
                      <span className="inline-flex items-center rounded-full bg-gray-50 px-2 py-1 text-gray-700 text-xs ring-1 ring-gray-200">
                        {getActivityTypeText(activity.type)}
                      </span>
                      {activity.participants.length > 0 && (
                        <span className="text-gray-500 text-xs">
                          {activity.participants.length} 名幼儿参与
                        </span>
                      )}
                    </div>

                    {/* 媒体信息 */}
                    {(activity.photos.length > 0 ||
                      activity.videos.length > 0) && (
                      <div className="flex items-center gap-4 text-gray-500 text-xs">
                        {activity.photos.length > 0 && (
                          <div className="flex items-center">
                            <Image className="mr-1 h-3 w-3" />
                            {activity.photos.length} 张照片
                          </div>
                        )}
                        {activity.videos.length > 0 && (
                          <div className="flex items-center">
                            <Video className="mr-1 h-3 w-3" />
                            {activity.videos.length} 个视频
                          </div>
                        )}
                      </div>
                    )}

                    {/* 照片预览 */}
                    {activity.photos.length > 0 && (
                      <div className="mt-2 flex gap-2">
                        {activity.photos.slice(0, 3).map((photo, index) => (
                          <img
                            alt={`活动照片 ${index + 1}`}
                            className="h-12 w-12 rounded-lg object-cover ring-1 ring-gray-200"
                            key={photo}
                            src={photo}
                          />
                        ))}
                        {activity.photos.length > 3 && (
                          <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-gray-50 text-gray-500 text-xs ring-1 ring-gray-200">
                            +{activity.photos.length - 3}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-8 text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-50 text-gray-400 ring-1 ring-gray-100">
              <ActivityIcon className="h-8 w-8" />
            </div>
            <h4 className="mb-2 font-medium text-gray-900 text-sm">
              暂无活动记录
            </h4>
            <p className="mb-4 text-gray-500 text-sm">开始记录第一个活动吧</p>
            <button
              className="hover:-translate-y-0.5 inline-flex items-center rounded-xl bg-blue-600 px-4 py-2 font-medium text-sm text-white shadow-sm transition-all hover:bg-blue-700 hover:shadow-md active:translate-y-0"
              type="button"
            >
              <Camera className="mr-2 h-4 w-4" />
              记录活动
            </button>
          </div>
        )}
      </div>
    </div>
  );
}