'use client';

import {
  DatePicker,
  Input,
  Card as MobileCard,
  Space,
  TextArea,
  Toast,
} from 'antd-mobile';
import clsx from 'clsx';
import {
  ArrowLeft,
  Calendar,
  HelpCircle,
  ImagePlus,
  Loader2,
  Plus,
  Sparkles,
  Tag,
  Target,
  X,
} from 'lucide-react';
import { useCallback, useMemo, useRef, useState } from 'react';
import {
  type AIGeneratedProject,
  generateProjectFromKeyword,
} from '../../../data/mockData';

// 顶层正则常量，避免在函数内重复创建
const IMAGE_TYPE_REGEX = /^image\//;

interface ProjectForm {
  topic: string;
  briefIntro?: string;
  title: string;
  description: string;
  objectivesText: string;
  drivingQuestions: string[];
  tags: string[]; // 新增：内容标签
  startDate: Date | null;
  endDate: Date | null;
}

function SectionTitle({
  icon,
  title,
  hint,
  accent = 'from-indigo-500/80 via-fuchsia-500/70 to-pink-500/70',
}: {
  icon: React.ReactNode;
  title: string;
  hint?: string;
  accent?: string;
}) {
  return (
    <div className="mb-4 flex items-center">
      <div
        className={clsx(
          'mr-3 flex aspect-square h-9 w-9 shrink-0 items-center justify-center overflow-hidden rounded-lg',
          'bg-gradient-to-br text-white shadow-[0_8px_24px_-8px_rgba(99,102,241,0.45)]',
          accent
        )}
      >
        {icon}
      </div>
      <div className="flex flex-col">
        <div className="font-semibold text-base text-gray-900 leading-none">
          {title}
        </div>
        {hint ? (
          <div className="mt-2 text-gray-500 text-xs leading-3">{hint}</div>
        ) : null}
      </div>
    </div>
  );
}

function CapsuleBadge({ index }: { index: number }) {
  return (
    <span
      className={clsx(
        'inline-flex select-none items-center justify-center',
        'h-6 min-w-6 rounded-full border border-gray-200 bg-white px-2',
        'font-medium text-gray-700 text-xs shadow-sm'
      )}
    >
      {index}
    </span>
  );
}

type NeoButtonProps = {
  children?: React.ReactNode;
  className?: string;
  color?: 'default' | 'primary' | 'danger';
  variant?: 'solid' | 'outline' | 'ghost';
  size?: 'mini' | 'default' | 'large';
  block?: boolean;
  loading?: boolean;
  disabled?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit';
};

function NeoButton({
  children,
  className,
  color = 'default',
  variant = 'solid',
  size = 'default',
  block = false,
  loading = false,
  disabled = false,
  onClick,
  type = 'button',
}: NeoButtonProps) {
  const base =
    'relative inline-flex items-center justify-center select-none rounded-xl font-medium transition-all duration-200 active:scale-[0.98] focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500/40 disabled:opacity-60 disabled:cursor-not-allowed';
  const sizeCls = clsx(
    size === 'mini' && 'h-7 px-2.5 text-xs',
    size === 'default' && 'h-10 px-3.5 text-sm',
    size === 'large' && 'h-12 px-4 text-base'
  );
  const blockCls = block ? 'w-full' : '';

  const palette = {
    default: {
      solid:
        'bg-white text-gray-700 border border-gray-200 shadow-sm hover:border-gray-300 hover:text-gray-900',
      outline:
        'bg-transparent text-gray-700 border border-gray-200 hover:border-gray-300 hover:bg-white/60',
      ghost:
        'bg-transparent text-gray-700 hover:bg-gray-50 border border-transparent',
    },
    primary: {
      solid:
        'text-white bg-gradient-to-br from-purple-400 to-blue-600 shadow-[0_10px_30px_-12px_rgba(99,102,241,0.35)] hover:brightness-[1.05]',
      outline:
        'text-indigo-600 border border-indigo-200/80 bg-white/80 hover:bg-white hover:border-indigo-300',
      ghost: 'text-indigo-600 hover:bg-indigo-50/70 border border-transparent',
    },
    danger: {
      solid:
        'text-white bg-gradient-to-br from-rose-500 via-orange-500 to-amber-500 shadow-[0_10px_30px_-12px_rgba(244,63,94,0.35)] hover:brightness-[1.05]',
      outline:
        'text-rose-600 border border-rose-200/80 bg-white/80 hover:bg-white hover:border-rose-300',
      ghost: 'text-rose-600 hover:bg-rose-50/70 border border-transparent',
    },
  } as const;

  const cls = clsx(
    base,
    sizeCls,
    blockCls,
    palette[color][variant],
    loading && 'pointer-events-none'
  );

  return (
    <button
      className={clsx(cls, className)}
      disabled={disabled || loading}
      onClick={onClick}
      type={type}
    >
      <span
        className={clsx(
          'inline-flex items-center gap-1.5',
          loading && 'opacity-90'
        )}
      >
        {loading && <Loader2 className="h-4 w-4 animate-spin" />}
        {children}
      </span>
    </button>
  );
}

function CreateProjectPage() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(false);
  const [form, setForm] = useState<ProjectForm>({
    topic: '',
    briefIntro: '',
    title: '',
    description: '',
    objectivesText: '',
    drivingQuestions: [''],
    tags: [],
    startDate: null,
    endDate: null,
  });

  // 封面图本地预览（仅 UI，不含上传逻辑）
  const [coverPreview, setCoverPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const triggerPickCover = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handlePickCover: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const file = e.target.files?.[0];
    if (!file) {
      return;
    }
    // 简单校验类型与大小（UI 级反馈）
    const isImg = IMAGE_TYPE_REGEX.test(file.type);
    if (!isImg) {
      Toast.show('请选择图片文件');
      e.target.value = '';
      return;
    }
    if (file.size > 5 * 1024 * 1024) {
      Toast.show('图片大小不超过 5MB');
      e.target.value = '';
      return;
    }
    const reader = new FileReader();
    reader.onload = () => {
      setCoverPreview(String(reader.result || ''));
    };
    reader.readAsDataURL(file);
  };

  const removeCover = useCallback(() => {
    setCoverPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  const topicSamples = useMemo(
    () => ['春天', '动物', '颜色', '水', '低碳城市'],
    []
  );

  const handleGenerateProject = useCallback(async () => {
    if (!form.topic.trim()) {
      Toast.show('请输入项目主题');
      return;
    }

    setIsGenerating(true);
    try {
      const generated: AIGeneratedProject = await generateProjectFromKeyword(
        form.topic
      );
      setForm((prev) => ({
        ...prev,
        title: generated.title,
        description: generated.description,
        objectivesText: generated.objectives.join('\n'),
        drivingQuestions: generated.drivingQuestions,
      }));
      setHasGenerated(true);
      Toast.show('项目内容已生成，您可以进行修改');
    } catch {
      Toast.show('生成失败，请重试');
    } finally {
      setIsGenerating(false);
    }
  }, [form.topic]);

  const handleManualCreate = useCallback(() => {
    setHasGenerated(true);
    Toast.show('已切换到手动创建模式');
  }, []);

  // 学习目标已改为多行文本框，上述数组操作不再需要

  const handleQuestionChange = (index: number, value: string) => {
    const newQuestions = [...form.drivingQuestions];
    newQuestions[index] = value;
    setForm((prev) => ({ ...prev, drivingQuestions: newQuestions }));
  };

  const addQuestion = () => {
    setForm((prev) => ({
      ...prev,
      drivingQuestions: [...prev.drivingQuestions, ''],
    }));
  };

  const removeQuestion = (index: number) => {
    if (form.drivingQuestions.length > 1) {
      const newQuestions = form.drivingQuestions.filter((_, i) => i !== index);
      setForm((prev) => ({ ...prev, drivingQuestions: newQuestions }));
    } else {
      Toast.show('至少保留一个驱动性问题');
    }
  };

  const handleSubmit = () => {
    if (!form.title.trim()) {
      Toast.show('请输入项目标题');
      return;
    }
    if (!form.description.trim()) {
      Toast.show('请输入项目描述');
      return;
    }
    if (!(form.startDate && form.endDate)) {
      Toast.show('请选择项目开始和结束时间');
      return;
    }
    if (form.startDate >= form.endDate) {
      Toast.show('结束时间必须晚于开始时间');
      return;
    }
    Toast.show('项目创建成功！');
  };

  const titleCount = form.title.trim().length;
  const descCount = form.description.trim().length;

  return (
    <div
      className={clsx(
        'relative min-h-screen',
        // 背景：纸感噪点 + 流体渐变
        'bg-[radial-gradient(1200px_600px_at_100%_-10%,rgba(168,85,247,0.10),transparent),radial-gradient(1200px_600px_at_-10%_10%,rgba(59,130,246,0.10),transparent)]',
        'antialiased'
      )}
    >
      {/* 顶部导航 */}
      <div className="sticky top-0 z-30 border-white/60 border-b bg-white/60 backdrop-blur supports-[backdrop-filter]:bg-white/70">
        <div className="mx-auto max-w-3xl px-4">
          <div className="flex h-14 items-center justify-between">
            <NeoButton
              className={clsx('group !px-0 text-gray-600 active:text-gray-900')}
              onClick={() => history.back()}
              variant="ghost"
            >
              <span className="inline-flex items-center rounded-full border border-gray-200 bg-white px-2 py-1 shadow-sm transition">
                <ArrowLeft className="group-active:-translate-x-0.5 mr-1 h-4 w-4 transition-transform" />
                <span className="text-sm">返回</span>
              </span>
            </NeoButton>
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-indigo-400/40 via-fuchsia-400/40 to-pink-400/40 opacity-50 blur-xl" />
              <div className="relative font-semibold text-gray-900">
                创建 PBL 项目
              </div>
            </div>
            <div className="w-14" />
          </div>
        </div>
      </div>

      {/* 页面主体 */}
      <div className="mx-auto max-w-3xl px-4 pt-4 pb-28">
        {/* AI 生成 */}
        <MobileCard className="overflow-hidden rounded-2xl border border-white/60 bg-white/80 shadow-[0_10px_30px_-12px_rgba(99,102,241,0.20)] backdrop-blur supports-[backdrop-filter]:bg-white/70">
          <div className="p-2">
            <SectionTitle
              accent="from-violet-500/80 via-sky-500/70 to-emerald-500/70"
              hint="输入项目主题，AI 帮您生成项目基本信息"
              icon={<Sparkles className="h-5 w-5 shrink-0" />}
              title="AI 项目助手"
            />
            <Space block className="gap-3" direction="vertical">
              <div>
                <div className="mb-2 flex items-center justify-between">
                  <div className="font-medium text-gray-700 text-sm">
                    项目主题
                  </div>
                </div>
                <div className="flex flex-col items-center gap-2">
                  <Input
                    clearable
                    onChange={(value) =>
                      setForm((prev) => ({ ...prev, topic: value }))
                    }
                    placeholder="例如：春天、动物、颜色、水…"
                    value={form.topic}
                  />
                </div>

                <div className="mt-2 flex flex-wrap gap-2">
                  {topicSamples.map((k) => (
                    <button
                      className={clsx(
                        'rounded-full border border-gray-200 bg-white px-2.5 py-1 text-gray-600 text-xs',
                        'hover:border-gray-300 hover:text-gray-800',
                        'transition active:scale-[0.98]'
                      )}
                      key={k}
                      onClick={() => setForm((prev) => ({ ...prev, topic: k }))}
                      type="button"
                    >
                      {k}
                    </button>
                  ))}
                </div>

                <div className="mt-3">
                  <div className="mb-2 font-medium text-gray-700 text-sm">
                    简单说明（可选）
                  </div>
                  <TextArea
                    maxLength={300}
                    onChange={(value) =>
                      setForm((prev) => ({ ...prev, briefIntro: value }))
                    }
                    placeholder="可补充项目背景、目标、受众等（可选）"
                    rows={3}
                    showCount
                    value={form.briefIntro}
                  />
                </div>
                <div className="mt-4 flex justify-center gap-3">
                  <NeoButton
                    className={clsx('min-w-[112px]')}
                    color="primary"
                    disabled={!form.topic.trim() || isGenerating}
                    loading={isGenerating}
                    onClick={handleGenerateProject}
                    variant="solid"
                  >
                    {isGenerating ? (
                      <span className="inline-flex items-center">生成中…</span>
                    ) : (
                      <span className="inline-flex items-center">
                        <Sparkles className="mr-1 h-4 w-4" />
                        生成项目
                      </span>
                    )}
                  </NeoButton>
                  <NeoButton
                    className={clsx('min-w-[112px]')}
                    color="default"
                    disabled={isGenerating}
                    onClick={handleManualCreate}
                    variant="outline"
                  >
                    <span className="inline-flex items-center">
                      <Target className="mr-1 h-4 w-4" />
                      手动创建
                    </span>
                  </NeoButton>
                </div>
              </div>

              {hasGenerated && (
                <div className="rounded-xl border border-emerald-200 bg-emerald-50/80 px-3 py-2 text-emerald-700 text-sm">
                  ✨ 已生成初稿，可在下方进行编辑与细化
                </div>
              )}
            </Space>
          </div>
        </MobileCard>

        {/* 基本信息 */}
        {hasGenerated && (
          <div className="mt-4 space-y-4">
            <MobileCard className="rounded-2xl border border-white/60 bg-white/80 shadow-[0_10px_30px_-12px_rgba(99,102,241,0.15)] backdrop-blur supports-[backdrop-filter]:bg-white/70">
              <div className="p-2">
                <SectionTitle
                  accent="from-amber-500/80 via-rose-500/70 to-purple-500/70"
                  hint="为项目命名并补充描述信息"
                  icon={<Sparkles className="h-5 w-5 shrink-0" />}
                  title="项目基本信息"
                />
                <Space block className="gap-3" direction="vertical">
                  <div>
                    <div className="mb-2 flex items-center justify-between">
                      <div className="font-medium text-gray-700 text-sm">
                        项目标题 <span className="text-rose-500">*</span>
                      </div>
                      <div
                        className={clsx(
                          'text-xs',
                          titleCount ? 'text-gray-400' : 'text-gray-300'
                        )}
                      >
                        {titleCount}/60
                      </div>
                    </div>
                    <Input
                      clearable
                      onChange={(value) =>
                        setForm((prev) => ({
                          ...prev,
                          title: value.slice(0, 60),
                        }))
                      }
                      placeholder="请输入项目标题（不超过 30 字）"
                      value={form.title}
                    />
                  </div>

                  <div>
                    <div className="mb-2 flex items-center justify-between">
                      <div className="font-medium text-gray-700 text-sm">
                        项目描述 <span className="text-rose-500">*</span>
                      </div>
                      <div
                        className={clsx(
                          'text-xs',
                          descCount ? 'text-gray-400' : 'text-gray-300'
                        )}
                      >
                        {descCount}/300
                      </div>
                    </div>
                    <TextArea
                      onChange={(value) =>
                        setForm((prev) => ({
                          ...prev,
                          description: value.slice(0, 300),
                        }))
                      }
                      placeholder="请描述项目的主要内容（建议 100~300 字）"
                      rows={4}
                      showCount
                      value={form.description}
                    />
                  </div>
                </Space>
              </div>
            </MobileCard>

            {/* 驱动性问题 */}
            <MobileCard className="rounded-2xl border border-white/60 bg-white/80 shadow-[0_10px_30px_-12px_rgba(99,102,241,0.15)] backdrop-blur supports-[backdrop-filter]:bg-white/70">
              <div className="p-2">
                <SectionTitle
                  accent="from-teal-500/80 via-emerald-500/70 to-lime-500/70"
                  hint="提出能引发探索与讨论的问题"
                  icon={<HelpCircle className="h-5 w-5 shrink-0" />}
                  title="驱动性问题"
                />
                <Space block className="gap-3" direction="vertical">
                  {form.drivingQuestions.map((question, index) => (
                    <div
                      className="group flex items-center gap-2"
                      key={`dq-${index}-${question}`}
                    >
                      <CapsuleBadge index={index + 1} />
                      <div className="flex-1">
                        <Input
                          clearable
                          onChange={(value) =>
                            handleQuestionChange(index, value)
                          }
                          onEnterPress={() => addQuestion()}
                          placeholder={`驱动性问题 ${index + 1}`}
                          value={question}
                        />
                      </div>
                      {form.drivingQuestions.length > 1 && (
                        <NeoButton
                          className="opacity-90 transition hover:opacity-100"
                          color="danger"
                          onClick={() => removeQuestion(index)}
                          size="mini"
                          variant="ghost"
                        >
                          <X className="h-4 w-4" />
                        </NeoButton>
                      )}
                    </div>
                  ))}
                  <NeoButton
                    className="w-full"
                    color="primary"
                    onClick={addQuestion}
                    variant="outline"
                  >
                    <span className="inline-flex items-center">
                      <Plus className="mr-1 h-4 w-4" />
                      添加驱动性问题
                    </span>
                  </NeoButton>
                </Space>
              </div>
            </MobileCard>

            {/* 学习目标 */}
            <MobileCard className="rounded-2xl border border-white/60 bg-white/80 shadow-[0_10px_30px_-12px_rgba(99,102,241,0.15)] backdrop-blur supports-[backdrop-filter]:bg-white/70">
              <div className="p-2">
                <SectionTitle
                  accent="from-sky-500/80 via-cyan-500/70 to-emerald-500/70"
                  hint="请填写项目希望达成的学习成果"
                  icon={<Target className="h-5 w-5 shrink-0" />}
                  title="学习目标"
                />
                <Space block className="gap-3" direction="vertical">
                  <TextArea
                    maxLength={800}
                    onChange={(value) =>
                      setForm((prev) => ({ ...prev, objectivesText: value }))
                    }
                    placeholder="请以多行方式填写学习目标"
                    rows={5}
                    showCount
                    value={form.objectivesText}
                  />
                </Space>
              </div>
            </MobileCard>

            {/* 内容标签 */}
            <MobileCard className="mt-4 rounded-2xl border border-white/60 bg-white/80 shadow-[0_10px_30px_-12px_rgba(99,102,241,0.15)] backdrop-blur supports-[backdrop-filter]:bg-white/70">
              <div className="p-2">
                <SectionTitle
                  accent="from-emerald-500/80 via-teal-500/70 to-cyan-500/70"
                  hint="选择主题标签，可点击推荐或自定义输入"
                  icon={<Tag className="h-5 w-5 shrink-0" />}
                  title="内容标签"
                />
                <Space block className="gap-3" direction="vertical">
                  {/* 推荐标签 */}
                  <div>
                    <div className="mb-2 font-medium text-gray-700 text-sm">
                      推荐标签
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {[
                        '自然',
                        '低碳',
                        '探究',
                        '团队',
                        '艺术',
                        '科学',
                        '社区',
                        '环保',
                      ].map((t) => {
                        const selected = form.tags.includes(t);
                        return (
                          <button
                            className={clsx(
                              'rounded-full border px-2.5 py-1 text-xs transition active:scale-[0.98]',
                              selected
                                ? 'border-emerald-300 bg-emerald-50 text-emerald-700'
                                : 'border-gray-200 bg-white text-gray-600 hover:border-gray-300 hover:text-gray-800'
                            )}
                            key={`rec-${t}`}
                            onClick={() =>
                              setForm((prev) => {
                                if (selected) {
                                  return {
                                    ...prev,
                                    tags: prev.tags.filter((x) => x !== t),
                                  };
                                }
                                if (prev.tags.length >= 5) {
                                  Toast.show('最多添加 5 个标签');
                                  return prev;
                                }
                                return { ...prev, tags: [...prev.tags, t] };
                              })
                            }
                            type="button"
                          >
                            {t}
                          </button>
                        );
                      })}
                    </div>
                  </div>

                  {/* 自定义标签输入 */}
                  <div>
                    <div className="mb-2 font-medium text-gray-700 text-sm">
                      自定义标签
                    </div>
                    <div className="relative flex items-center gap-2">
                      <Input
                        clearable
                        onChange={() => {
                          // 占位处理，避免 Biome 未使用参数报错
                          return;
                        }}
                        placeholder="输入标签（≤ 8 字），点右侧“添加”"
                      />
                      <NeoButton
                        color="primary"
                        onClick={() => {
                          // 通过 DOM 查询同层 input 的值
                          const container = document.activeElement?.closest?.(
                            '.relative.flex.items-center.gap-2'
                          ) as HTMLElement | null;
                          const input = container?.querySelector(
                            'input'
                          ) as HTMLInputElement | null;
                          const raw = (input?.value || '').trim().slice(0, 8);
                          if (!raw) {
                            Toast.show('请输入标签内容');
                            return;
                          }
                          const safe = raw.replace(/[^\u4e00-\u9fa5\w-]/g, '');
                          if (!safe) {
                            Toast.show('无效的标签内容');
                            return;
                          }
                          setForm((prev) => {
                            if (prev.tags.includes(safe)) {
                              Toast.show('标签已存在');
                              return prev;
                            }
                            if (prev.tags.length >= 5) {
                              Toast.show('最多添加 5 个标签');
                              return prev;
                            }
                            return { ...prev, tags: [...prev.tags, safe] };
                          });
                          if (input) {
                            input.value = '';
                          }
                        }}
                        variant="outline"
                      >
                        <Plus className="h-4 w-4" />
                      </NeoButton>
                    </div>
                  </div>

                  {/* 已选择标签（胶囊展示，可删除） */}
                  <div>
                    <div className="mb-2 font-medium text-gray-700 text-sm">
                      已选择
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {form.tags.map((tag) => (
                        <span
                          className={clsx(
                            'inline-flex items-center gap-1 rounded-full border px-2 py-1 text-xs shadow-sm transition',
                            'border-gray-400 bg-white text-gray-700 hover:border-gray-300'
                          )}
                          key={`sel-${tag}`}
                        >
                          <span className="select-none">#</span>
                          <span>{tag}</span>
                          <button
                            aria-label={`移除标签 ${tag}`}
                            className="ml-1 rounded-full p-0.5 text-gray-400 transition hover:text-rose-500"
                            onClick={() =>
                              setForm((prev) => ({
                                ...prev,
                                tags: prev.tags.filter((t) => t !== tag),
                              }))
                            }
                            type="button"
                          >
                            <X className="h-3.5 w-3.5" />
                          </button>
                        </span>
                      ))}
                    </div>
                  </div>
                </Space>
              </div>
            </MobileCard>

            {/* 项目封面 */}
            <MobileCard className="rounded-2xl border border-white/60 bg-white/80 shadow-[0_10px_30px_-12px_rgba(99,102,241,0.15)] backdrop-blur supports-[backdrop-filter]:bg-white/70">
              <div className="p-2">
                <SectionTitle
                  accent="from-fuchsia-500/80 via-purple-500/70 to-indigo-500/70"
                  hint="上传项目封面图，大小 ≤ 5MB"
                  icon={<ImagePlus className="h-5 w-5 shrink-0" />}
                  title="项目封面"
                />

                <div className="space-y-3">
                  {/* 预览区/占位区 */}
                  <div
                    className={clsx(
                      'relative overflow-hidden rounded-xl border',
                      coverPreview
                        ? 'border-gray-200 bg-white'
                        : 'border-gray-300 border-dashed bg-gradient-to-br from-gray-50 to-white'
                    )}
                  >
                    {coverPreview ? (
                      <div className="group relative">
                        {/* 封面图 */}
                        <img
                          alt="项目封面预览"
                          className="aspect-video w-full object-cover transition-transform duration-300 group-hover:scale-[1.01]"
                          src={coverPreview}
                        />
                        {/* 遮罩动作条 */}
                        <div className="pointer-events-none absolute inset-0 flex items-end justify-end bg-gradient-to-t from-black/30 via-black/0 to-transparent p-2 opacity-0 transition-opacity duration-200 group-hover:opacity-100">
                          <div className="pointer-events-auto flex gap-2">
                            <NeoButton
                              color="default"
                              onClick={triggerPickCover}
                              size="mini"
                              variant="outline"
                            >
                              <span className="inline-flex items-center">
                                <ImagePlus className="mr-1 h-3.5 w-3.5" />
                                更换
                              </span>
                            </NeoButton>
                            <NeoButton
                              color="danger"
                              onClick={removeCover}
                              size="mini"
                              variant="solid"
                            >
                              <span className="inline-flex items-center">
                                <X className="mr-1 h-3.5 w-3.5" />
                                移除
                              </span>
                            </NeoButton>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <button
                        className={clsx(
                          'group flex w-full flex-col items-center justify-center gap-2 py-10 outline-none transition',
                          'hover:bg-gray-50 active:scale-[0.99]'
                        )}
                        onClick={triggerPickCover}
                        type="button"
                      >
                        <div className="rounded-lg border border-gray-400 p-2 text-gray-500">
                          <ImagePlus className="h-6 w-6" />
                        </div>
                        <div className="font-medium text-gray-600 text-sm">
                          点击上传封面图
                        </div>
                        <div className="text-gray-500 text-xs">
                          支持 jpg/png/webp，推荐 16:9
                        </div>
                      </button>
                    )}

                    {/* 隐藏的文件选择 */}
                    <input
                      accept="image/*"
                      className="sr-only"
                      onChange={handlePickCover}
                      ref={fileInputRef}
                      type="file"
                    />
                  </div>

                  {/* 次要操作行（当无图时展示一个按钮） */}
                  {!coverPreview && (
                    <div className="flex justify-end">
                      <NeoButton
                        color="primary"
                        onClick={triggerPickCover}
                        size="mini"
                        variant="outline"
                      >
                        选择图片
                      </NeoButton>
                    </div>
                  )}
                </div>
              </div>
            </MobileCard>

            {/* 项目时间 */}
            <MobileCard className="rounded-2xl border border-white/60 bg-white/80 shadow-[0_10px_30px_-12px_rgba(99,102,241,0.15)] backdrop-blur supports-[backdrop-filter]:bg-white/70">
              <div className="p-2">
                <SectionTitle
                  accent="from-orange-500/80 via-amber-500/70 to-rose-500/70"
                  hint="选择项目起止日期，确保时间安排合理"
                  icon={<Calendar className="h-5 w-5 shrink-0" />}
                  title="项目时间"
                />
                <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
                  <div>
                    <div className="mb-2 font-medium text-gray-700 text-sm">
                      开始时间 <span className="text-rose-500">*</span>
                    </div>
                    <DatePicker
                      onConfirm={(date) =>
                        setForm((prev) => ({ ...prev, startDate: date }))
                      }
                      value={form.startDate}
                    >
                      {(value) => (
                        <NeoButton
                          block
                          className={clsx(
                            'justify-start border border-gray-200 text-left'
                          )}
                          color="default"
                          variant="outline"
                        >
                          {value
                            ? value.toLocaleDateString('zh-CN')
                            : '选择开始时间'}
                        </NeoButton>
                      )}
                    </DatePicker>
                  </div>
                  <div>
                    <div className="mb-2 font-medium text-gray-700 text-sm">
                      结束时间 <span className="text-rose-500">*</span>
                    </div>
                    <DatePicker
                      onConfirm={(date) =>
                        setForm((prev) => ({ ...prev, endDate: date }))
                      }
                      value={form.endDate}
                    >
                      {(value) => (
                        <NeoButton
                          block
                          className={clsx(
                            'justify-start border border-gray-200 text-left'
                          )}
                          color="default"
                          variant="outline"
                        >
                          {value
                            ? value.toLocaleDateString('zh-CN')
                            : '选择结束时间'}
                        </NeoButton>
                      )}
                    </DatePicker>
                  </div>
                </div>
                {form.startDate &&
                form.endDate &&
                form.startDate >= form.endDate ? (
                  <div className="mt-2 rounded-md border border-rose-200 bg-rose-50 px-2 py-1.5 text-rose-700 text-xs">
                    结束时间必须晚于开始时间
                  </div>
                ) : null}
              </div>
            </MobileCard>
          </div>
        )}

        {/* 底部提交条 */}
        {hasGenerated && (
          <div className="pointer-events-none fixed inset-x-0 bottom-0 z-40">
            <div className="mx-auto max-w-3xl px-4 pb-safe">
              <div className="pointer-events-auto mb-3 rounded-2xl">
                <div className="flex flex-col items-center gap-2">
                  <NeoButton
                    block
                    className={clsx('w-full sm:w-auto sm:min-w-[160px]')}
                    color="primary"
                    onClick={handleSubmit}
                    size="large"
                    variant="solid"
                  >
                    创建项目
                  </NeoButton>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default CreateProjectPage;
