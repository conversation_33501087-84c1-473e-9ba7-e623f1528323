import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

// 配置 API 路由的运行时
export const runtime = 'nodejs';

// 支持的图片格式
const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/svg+xml',
  'image/bmp',
];

// 辅助函数：根据 MIME 类型获取文件扩展名
function getExtensionFromMimeType(mimeType: string): string {
  const mimeToExt: Record<string, string> = {
    'image/jpeg': '.jpg',
    'image/jpg': '.jpg',
    'image/png': '.png',
    'image/gif': '.gif',
    'image/webp': '.webp',
    'image/svg+xml': '.svg',
    'image/bmp': '.bmp',
  };
  return mimeToExt[mimeType.toLowerCase()] || '';
}

/**
 * POST /api/image/file
 * 接收图片链接，返回图片文件数据
 *
 * 请求体：
 * {
 *   "imageUrl": "https://example.com/image.jpg",
 *   "returnType": "json" | "file" (可选，默认为 "json")
 * }
 *
 * 响应：
 * - 成功：返回 JSON 格式的图片数据或二进制文件
 * - 失败：返回 JSON 错误信息
 */
export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json();
    const { imageUrl, returnType = 'json' } = body;

    // 参数验证
    if (!imageUrl || typeof imageUrl !== 'string') {
      return NextResponse.json(
        {
          error: 'Bad Request',
          message: '请提供有效的 imageUrl 参数',
        },
        { status: 400 },
      );
    }

    // 验证 URL 格式
    let url: URL;
    try {
      url = new URL(imageUrl);
      // 只允许 http 和 https 协议
      if (!['http:', 'https:'].includes(url.protocol)) {
        throw new Error('不支持的协议');
      }
    } catch {
      return NextResponse.json(
        {
          error: 'Bad Request',
          message: '无效的 URL 格式',
        },
        { status: 400 },
      );
    }

    // 下载图片
    const imageResponse = await fetch(imageUrl, {
      // 设置超时时间为 30 秒
      signal: AbortSignal.timeout(30000),
    });

    if (!imageResponse.ok) {
      return NextResponse.json(
        {
          error: 'Fetch Error',
          message: `无法获取图片: ${imageResponse.status} ${imageResponse.statusText}`,
        },
        { status: imageResponse.status },
      );
    }

    // 获取 Content-Type
    const contentType =
      imageResponse.headers.get('content-type') || 'application/octet-stream';

    // 验证是否为图片类型
    if (!SUPPORTED_IMAGE_TYPES.includes(contentType.toLowerCase())) {
      return NextResponse.json(
        {
          error: 'Unsupported Media Type',
          message: `不支持的文件类型: ${contentType}`,
        },
        { status: 415 },
      );
    }

    // 获取图片数据
    const imageBuffer = await imageResponse.arrayBuffer();

    // 获取文件名
    const urlPath = url.pathname;
    const fileName = urlPath.split('/').pop() || 'image';

    // 确保文件名有正确的扩展名
    const fileExtension = fileName.includes('.')
      ? ''
      : getExtensionFromMimeType(contentType);
    const finalFileName =
      fileExtension && !fileName.endsWith(fileExtension)
        ? `${fileName}${fileExtension}`
        : fileName;

    // 根据 returnType 决定返回格式
    if (returnType === 'file') {
      // 返回二进制文件
      return new NextResponse(imageBuffer, {
        status: 200,
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${encodeURIComponent(finalFileName)}"`,
          'Content-Length': imageBuffer.byteLength.toString(),
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Access-Control-Allow-Origin': '*',
        },
      });
    }
    // 返回 JSON 格式的键值对
    const base64Data = Buffer.from(imageBuffer).toString('base64');
    const dataUrl = `data:${contentType};base64,${base64Data}`;

    return NextResponse.json({
      success: true,
      data: {
        filename: finalFileName,
        contentType,
        size: imageBuffer.byteLength,
        base64: base64Data,
        dataUrl,
        url: imageUrl,
      },
    });
  } catch (error) {
    console.error('处理图片请求时出错:', error);

    // 处理超时错误
    if (error instanceof Error && error.name === 'TimeoutError') {
      return NextResponse.json(
        {
          error: 'Timeout Error',
          message: '请求超时',
        },
        { status: 408 },
      );
    }

    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: '服务器内部错误',
      },
      { status: 500 },
    );
  }
}

/**
 * GET /api/image/file?url=https://example.com/image.jpg&returnType=json
 * 通过查询参数获取图片文件
 *
 * 查询参数：
 * - url: 图片链接（必需）
 * - returnType: "json" | "file" (可选，默认为 "json")
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const imageUrl = searchParams.get('url');
    const returnType = searchParams.get('returnType') || 'json';

    // 参数验证
    if (!imageUrl) {
      return NextResponse.json(
        {
          error: 'Bad Request',
          message: '请提供 url 查询参数',
        },
        { status: 400 },
      );
    }

    // 验证 URL 格式
    let url: URL;
    try {
      url = new URL(imageUrl);
      if (!['http:', 'https:'].includes(url.protocol)) {
        throw new Error('不支持的协议');
      }
    } catch {
      return NextResponse.json(
        {
          error: 'Bad Request',
          message: '无效的 URL 格式',
        },
        { status: 400 },
      );
    }

    // 下载图片
    const imageResponse = await fetch(imageUrl, {
      signal: AbortSignal.timeout(30000),
    });

    if (!imageResponse.ok) {
      return NextResponse.json(
        {
          error: 'Fetch Error',
          message: `无法获取图片: ${imageResponse.status} ${imageResponse.statusText}`,
        },
        { status: imageResponse.status },
      );
    }

    // 获取 Content-Type
    const contentType =
      imageResponse.headers.get('content-type') || 'application/octet-stream';

    // 验证是否为图片类型
    if (!SUPPORTED_IMAGE_TYPES.includes(contentType.toLowerCase())) {
      return NextResponse.json(
        {
          error: 'Unsupported Media Type',
          message: `不支持的文件类型: ${contentType}`,
        },
        { status: 415 },
      );
    }

    // 获取图片数据
    const imageBuffer = await imageResponse.arrayBuffer();

    // 获取文件名
    const urlPath = url.pathname;
    const fileName = urlPath.split('/').pop() || 'image';

    // 根据 returnType 决定返回格式
    if (returnType === 'file') {
      // 返回二进制文件
      return new NextResponse(imageBuffer, {
        status: 200,
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `inline; filename="${encodeURIComponent(fileName)}"`,
          'Content-Length': imageBuffer.byteLength.toString(),
          'Cache-Control': 'public, max-age=3600',
          'Access-Control-Allow-Origin': '*',
        },
      });
    }
    // 返回 JSON 格式的键值对
    const base64Data = Buffer.from(imageBuffer).toString('base64');
    const dataUrl = `data:${contentType};base64,${base64Data}`;

    return NextResponse.json({
      success: true,
      data: {
        filename: fileName,
        contentType,
        size: imageBuffer.byteLength,
        base64: base64Data,
        dataUrl,
        url: imageUrl,
      },
    });
  } catch (error) {
    console.error('处理图片请求时出错:', error);

    if (error instanceof Error && error.name === 'TimeoutError') {
      return NextResponse.json(
        {
          error: 'Timeout Error',
          message: '请求超时',
        },
        { status: 408 },
      );
    }

    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: '服务器内部错误',
      },
      { status: 500 },
    );
  }
}

/**
 * OPTIONS 请求处理（用于 CORS 预检）
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400',
    },
  });
}
