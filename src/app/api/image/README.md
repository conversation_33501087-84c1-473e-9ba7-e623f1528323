# 图片文件 API 文档

这个 API 接口用于从外部系统获取图片文件。它接收一个图片链接，下载图片并返回文件数据。

## 接口地址

```
POST https://your-domain.com/api/image/file
GET  https://your-domain.com/api/image/file?url={imageUrl}
```

## 接口说明

### POST 方法

适用于需要在请求体中传递参数的场景。

**请求头：**

```
Content-Type: application/json
```

**请求体：**

```json
{
  "imageUrl": "https://example.com/image.jpg"
}
```

**响应：**

- 成功 (200)：返回图片文件的二进制数据
- 失败：返回 JSON 格式的错误信息

### GET 方法

适用于简单的 URL 参数传递场景。

**请求示例：**

```
GET /api/image/file?url=https://example.com/image.jpg
```

**响应：**

- 成功 (200)：返回图片文件的二进制数据
- 失败：返回 JSON 格式的错误信息

## 响应头说明

成功响应包含以下响应头：

- `Content-Type`：图片的 MIME 类型
- `Content-Disposition`：文件名信息
- `Content-Length`：文件大小
- `Access-Control-Allow-Origin`：CORS 支持

## 错误响应

错误响应为 JSON 格式：

```json
{
  "error": "错误类型",
  "message": "详细错误信息"
}
```

常见错误码：

- 400：请求参数错误
- 408：请求超时
- 415：不支持的文件类型
- 500：服务器内部错误

## 支持的图片格式

- JPEG/JPG
- PNG
- GIF
- WebP
- SVG
- BMP

## 调用示例

### JavaScript (Fetch API)

```javascript
// POST 方法
async function getImageFile(imageUrl) {
  const response = await fetch("https://your-domain.com/api/image/file", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ imageUrl }),
  });

  if (response.ok) {
    const blob = await response.blob();
    // 处理图片 blob
    return blob;
  } else {
    const error = await response.json();
    throw new Error(error.message);
  }
}

// GET 方法
async function getImageFileSimple(imageUrl) {
  const response = await fetch(
    `https://your-domain.com/api/image/file?url=${encodeURIComponent(imageUrl)}`
  );

  if (response.ok) {
    const blob = await response.blob();
    return blob;
  } else {
    const error = await response.json();
    throw new Error(error.message);
  }
}

// 使用示例：下载图片并创建下载链接
async function downloadImage() {
  try {
    const imageUrl = "https://example.com/sample.jpg";
    const blob = await getImageFile(imageUrl);

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "downloaded-image.jpg";
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  } catch (error) {
    console.error("下载失败:", error);
  }
}
```

### Python

```python
import requests
import json
import base64

# POST 方法 - 获取 JSON 格式数据
def get_image_data(image_url):
    response = requests.post(
        'https://your-domain.com/api/image/file',
        json={
            'imageUrl': image_url,
            'returnType': 'json'
        }
    )

    if response.status_code == 200:
        result = response.json()
        return result['data']
    else:
        raise Exception(response.json()['message'])

# POST 方法 - 获取二进制文件
def get_image_file(image_url):
    response = requests.post(
        'https://your-domain.com/api/image/file',
        json={
            'imageUrl': image_url,
            'returnType': 'file'
        }
    )

    if response.status_code == 200:
        return response.content  # 二进制数据
    else:
        raise Exception(response.json()['message'])

# 使用示例：从 JSON 响应保存图片
def save_image_from_json():
    try:
        image_url = 'https://example.com/sample.jpg'
        data = get_image_data(image_url)

        print(f"文件名: {data['filename']}")
        print(f"文件大小: {data['size']} bytes")
        print(f"文件类型: {data['contentType']}")

        # 将 base64 数据解码并保存
        image_binary = base64.b64decode(data['base64'])
        with open(data['filename'], 'wb') as f:
            f.write(image_binary)
        print('图片保存成功')
    except Exception as e:
        print(f'下载失败: {e}')

# 使用示例：直接获取并保存二进制文件
def save_image_direct():
    try:
        image_url = 'https://example.com/sample.jpg'
        image_data = get_image_file(image_url)

        with open('downloaded_image.jpg', 'wb') as f:
            f.write(image_data)
        print('图片保存成功')
    except Exception as e:
        print(f'下载失败: {e}')
```

### cURL

```bash
# POST 方法 - 获取 JSON 格式数据
curl -X POST https://your-domain.com/api/image/file \
  -H "Content-Type: application/json" \
  -d '{"imageUrl":"https://example.com/image.jpg","returnType":"json"}' \
  | jq .

# POST 方法 - 获取二进制文件
curl -X POST https://your-domain.com/api/image/file \
  -H "Content-Type: application/json" \
  -d '{"imageUrl":"https://example.com/image.jpg","returnType":"file"}' \
  -o output.jpg

# GET 方法 - 获取 JSON 格式数据
curl "https://your-domain.com/api/image/file?url=https://example.com/image.jpg"

# GET 方法 - 获取二进制文件
curl -o output.jpg "https://your-domain.com/api/image/file?url=https://example.com/image.jpg&returnType=file"
```

### Java

```java
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

public class ImageDownloader {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    // 数据类
    public static class ImageData {
        public String filename;
        public String contentType;
        public long size;
        public String base64;
        public String dataUrl;
        public String url;
    }

    // POST 方法 - 获取 JSON 格式数据
    public ImageData getImageData(String imageUrl) throws Exception {
        HttpClient client = HttpClient.newHttpClient();
        String json = String.format("{\"imageUrl\":\"%s\",\"returnType\":\"json\"}", imageUrl);

        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create("https://your-domain.com/api/image/file"))
            .header("Content-Type", "application/json")
            .POST(HttpRequest.BodyPublishers.ofString(json))
            .build();

        HttpResponse<String> response = client.send(request,
            HttpResponse.BodyHandlers.ofString());

        if (response.statusCode() == 200) {
            JsonNode root = objectMapper.readTree(response.body());
            JsonNode data = root.get("data");

            ImageData imageData = new ImageData();
            imageData.filename = data.get("filename").asText();
            imageData.contentType = data.get("contentType").asText();
            imageData.size = data.get("size").asLong();
            imageData.base64 = data.get("base64").asText();
            imageData.dataUrl = data.get("dataUrl").asText();
            imageData.url = data.get("url").asText();

            return imageData;
        } else {
            throw new Exception("Failed to get image: " + response.statusCode());
        }
    }

    // POST 方法 - 获取二进制文件
    public byte[] getImageFile(String imageUrl) throws Exception {
        HttpClient client = HttpClient.newHttpClient();
        String json = String.format("{\"imageUrl\":\"%s\",\"returnType\":\"file\"}", imageUrl);

        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create("https://your-domain.com/api/image/file"))
            .header("Content-Type", "application/json")
            .POST(HttpRequest.BodyPublishers.ofString(json))
            .build();

        HttpResponse<byte[]> response = client.send(request,
            HttpResponse.BodyHandlers.ofByteArray());

        if (response.statusCode() == 200) {
            return response.body();
        } else {
            throw new Exception("Failed to get image: " + response.statusCode());
        }
    }

    // 使用示例
    public void downloadAndSaveImage() {
        try {
            String imageUrl = "https://example.com/sample.jpg";

            // 方式1：使用 JSON 响应
            ImageData data = getImageData(imageUrl);
            System.out.println("文件名: " + data.filename);
            System.out.println("文件大小: " + data.size + " bytes");
            System.out.println("文件类型: " + data.contentType);

            // 解码 base64 并保存
            byte[] imageBytes = Base64.getDecoder().decode(data.base64);
            Files.write(Paths.get(data.filename), imageBytes);
            System.out.println("图片保存成功");

            // 方式2：直接获取二进制文件
            byte[] imageData = getImageFile(imageUrl);
            Files.write(Paths.get("downloaded2.jpg"), imageData);

        } catch (Exception e) {
            System.err.println("下载失败: " + e.getMessage());
        }
    }
}
```

### PHP

```php
<?php
// POST 方法 - 获取 JSON 格式数据
function getImageData($imageUrl) {
    $ch = curl_init('https://your-domain.com/api/image/file');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
        'imageUrl' => $imageUrl,
        'returnType' => 'json'
    ]));

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 200) {
        $result = json_decode($response, true);
        return $result['data'];
    } else {
        $error = json_decode($response, true);
        throw new Exception($error['message']);
    }
}

// POST 方法 - 获取二进制文件
function getImageFile($imageUrl) {
    $ch = curl_init('https://your-domain.com/api/image/file');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
        'imageUrl' => $imageUrl,
        'returnType' => 'file'
    ]));

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 200) {
        return $response;
    } else {
        $error = json_decode($response, true);
        throw new Exception($error['message']);
    }
}

// 使用示例
try {
    $imageUrl = 'https://example.com/sample.jpg';

    // 方式1：使用 JSON 响应
    $data = getImageData($imageUrl);
    echo "文件名: " . $data['filename'] . "\n";
    echo "文件大小: " . $data['size'] . " bytes\n";
    echo "文件类型: " . $data['contentType'] . "\n";

    // 解码 base64 并保存
    $imageData = base64_decode($data['base64']);
    file_put_contents($data['filename'], $imageData);
    echo "图片保存成功\n";

    // 在网页中显示图片
    echo '<img src="' . $data['dataUrl'] . '" alt="Downloaded image">';

    // 方式2：直接获取二进制文件
    $imageData = getImageFile($imageUrl);
    file_put_contents('downloaded2.jpg', $imageData);

} catch (Exception $e) {
    echo "下载失败: " . $e->getMessage() . "\n";
}
?>
```

## 注意事项

1. **超时设置**：请求超时时间为 30 秒
2. **CORS 支持**：API 支持跨域访问，可以从浏览器直接调用
3. **文件大小**：建议对大文件使用流式处理
4. **缓存策略**：
   - POST 请求不缓存
   - GET 请求缓存 1 小时
5. **安全性**：建议在生产环境中添加认证机制

## 部署后的实际地址

部署后，将 `your-domain.com` 替换为实际的域名即可使用。例如：

- 测试环境：`https://test.example.com/api/image/file`
- 生产环境：`https://api.example.com/api/image/file`
