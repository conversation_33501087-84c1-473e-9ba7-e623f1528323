'use client';

import { ActionSheet } from 'antd-mobile';
import { Image, Music, Video } from 'lucide-react';
import { cn } from '@/lib/utils';

export type MediaType = 'image' | 'video' | 'audio';

interface MediaTypeOption {
  type: MediaType;
  label: string;
  icon: React.ReactNode;
  description: string;
  accept: string;
}

const mediaTypeOptions: MediaTypeOption[] = [
  {
    type: 'image',
    label: '图片',
    icon: <Image className="h-6 w-6" />,
    description: '支持 JPG, PNG, WEBP 格式',
    accept: 'image/*',
  },
  {
    type: 'video',
    label: '视频',
    icon: <Video className="h-6 w-6" />,
    description: '支持 MP4, MOV 格式',
    accept: 'video/*',
  },
  {
    type: 'audio',
    label: '音频',
    icon: <Music className="h-6 w-6" />,
    description: '支持 M4A, WAV, MP3 格式',
    accept: 'audio/*',
  },
];

interface MediaTypeSelectorProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (type: MediaType) => void;
  currentType?: MediaType;
}

const MediaTypeSelector: React.FC<MediaTypeSelectorProps> = ({
  visible,
  onClose,
  onSelect,
  currentType,
}) => {
  const handleSelect = (type: MediaType) => {
    onSelect(type);
    onClose();
  };

  const actions = mediaTypeOptions.map((option) => ({
    key: option.type,
    text: (
      <div className="flex items-center space-x-4 py-2">
        <div
          className={cn(
            'flex h-12 w-12 items-center justify-center rounded-full transition-colors',
            currentType === option.type
              ? 'bg-blue-100 text-blue-600'
              : 'bg-gray-100 text-gray-600'
          )}
        >
          {option.icon}
        </div>
        <div className="flex-1 text-left">
          <div
            className={cn(
              'font-medium text-base',
              currentType === option.type ? 'text-blue-600' : 'text-gray-800'
            )}
          >
            {option.label}
          </div>
          <div className="text-gray-500 text-sm">{option.description}</div>
        </div>
        {currentType === option.type && (
          <div className="h-2 w-2 rounded-full bg-blue-600" />
        )}
      </div>
    ),
    onClick: () => handleSelect(option.type),
  }));

  return (
    <ActionSheet
      actions={actions}
      cancelText="取消"
      extra={
        <div className="pb-2 text-center">
          <div className="font-medium text-gray-800 text-lg">选择素材类型</div>
          <div className="mt-1 text-gray-500 text-sm">
            一次只能上传一种类型的素材
          </div>
        </div>
      }
      onClose={onClose}
      visible={visible}
    />
  );
};

export default MediaTypeSelector;
export { mediaTypeOptions };
export type { MediaTypeOption };
