'use client';

import { CloudUpload, Image, Info, Music, Video } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { MediaType } from './MediaTypeSelector';

interface FileUploadCardProps {
  isUploading: boolean;
  onFileSelect: () => void;
  className?: string;
  multiple?: boolean;
  selectedMediaType?: MediaType;
}

const getMediaTypeInfo = (type?: MediaType) => {
  switch (type) {
    case 'image':
      return {
        icon: Image,
        label: '图片',
        description: '点击选择图片文件',
        formats: 'JPG, PNG, WEBP',
      };
    case 'video':
      return {
        icon: Video,
        label: '视频',
        description: '点击选择视频文件',
        formats: 'MP4, MOV',
      };
    case 'audio':
      return {
        icon: Music,
        label: '音频',
        description: '点击选择音频文件',
        formats: 'M4A, MP3',
      };
    default:
      return {
        icon: CloudUpload,
        label: '素材文件',
        description: '点击选择素材类型',
        formats: '图片、视频、音频',
      };
  }
};

const FileUploadCard: React.FC<FileUploadCardProps> = ({
  isUploading,
  onFileSelect,
  className,
  multiple = false,
  selectedMediaType,
}) => {
  const mediaInfo = getMediaTypeInfo(selectedMediaType);
  const IconComponent = mediaInfo.icon;

  return (
    <div
      className={cn(
        'mx-auto flex w-full max-w-md flex-col items-center',
        className
      )}
    >
      <button
        className="group relative flex aspect-square w-56 flex-col items-center justify-center overflow-hidden rounded-2xl border-2 border-blue-300 border-dashed bg-white transition-all duration-300 hover:border-blue-500 disabled:cursor-not-allowed disabled:opacity-70"
        disabled={isUploading}
        onClick={onFileSelect}
        type="button"
      >
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />

        <div className="relative z-10 flex flex-col items-center justify-center p-6 text-center">
          <div
            className={cn(
              'mb-6 flex h-16 w-16 items-center justify-center rounded-full transition-transform duration-300 group-hover:scale-110',
              selectedMediaType ? 'bg-blue-100' : 'bg-blue-50'
            )}
          >
            <IconComponent
              className={cn(
                'h-8 w-8 transition-colors group-hover:text-blue-600',
                selectedMediaType ? 'text-blue-600' : 'text-blue-500'
              )}
              strokeWidth={1.5}
            />
          </div>

          <h3 className="mb-2 font-medium text-gray-800 text-xl transition-colors group-hover:text-blue-700">
            {selectedMediaType ? `添加${mediaInfo.label}` : '添加素材文件'}
          </h3>

          <p className="text-gray-500 text-sm transition-colors group-hover:text-gray-700">
            {mediaInfo.description}
          </p>
        </div>
      </button>

      <div className="mt-4 flex items-center justify-center text-gray-500 text-sm">
        <Info className="mr-2 h-4 w-4" />
        <span className="text-xs">
          支持 {mediaInfo.formats} 格式，最大 1000MB{multiple ? '，可多选' : ''}
        </span>
      </div>
    </div>
  );
};

export default FileUploadCard;
