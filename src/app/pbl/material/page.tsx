'use client';

import { useInfiniteQuery } from '@tanstack/react-query';
import { InfiniteScroll, PullToRefresh, Tabs } from 'antd-mobile';
import clsx from 'clsx';
import { compare } from 'compare-versions';
import {
  AudioLines as AudioIcon,
  Image as ImageIcon,
  Play as PlayIcon,
  Plus,
  UserCircle,
  Video as VideoIcon,
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { getMaterialList } from '@/api/pbl';
import StatusBadge from '@/app/pbl/material/components/StatusBadge';
import Empty from '@/components/Empty';
import { useCommonStore } from '@/store/useCommonStore';
import { getMessage, navigationToNativePage } from '@/utils';

// import Script from 'next/script';

interface Material {
  id: string;
  observationId: string;
  title: string;
  type: 'video' | 'audio' | 'image';
  size: string;
  author: string;
  date: string;
  duration?: string; // Optional for images
  status: number; // 0-待处理 1-处理中 2-已处理 3-处理失败
  thumbnail?: string; // 添加缩略图字段
}

// 定义接口返回的数据类型
interface ApiMaterial {
  instId: string;
  mediaId: string;
  observationId: string;
  type: number; // 1=图片，2=视频，3=音频
  url: string;
  fileSize: string;
  cover?: string;
  duration?: number;
  videoPlayType: number;
  handleError: string;
  createUserId: string;
  createTime: string;
  updateTime: string;
  projectId: string;
  source: number;
  name: string;
  deptId: string;
  createUser: {
    id: string;
    name: string;
    avatar: string;
  };
  handleStatus: number;
  handleStatusDesc: string;
}

// Thumb 组件
const Thumb: React.FC<{ type: MaterialType; thumbnail?: string }> = ({
  type,
  thumbnail,
}) => {
  const base =
    'relative w-16 h-16 rounded-lg ring-1 ring-black/5 overflow-hidden flex items-center justify-center shrink-0 bg-gradient-to-b from-white to-slate-50';
  if (type === 'image' || type === 'video') {
    if (thumbnail) {
      return (
        <div className={base}>
          <img
            alt="thumbnail"
            className="h-full w-full object-cover transition-transform duration-300 ease-out group-hover:scale-[1.02]"
            loading="lazy"
            src={thumbnail}
          />
          {type === 'video' ? (
            <span className="absolute inset-0">
              <span className="absolute inset-0 bg-black/35 transition-opacity duration-200 group-hover:bg-black/40" />
              <span className="absolute inset-0 flex items-center justify-center">
                <PlayIcon className="translate-x-[1px] text-white" size={18} />
              </span>
            </span>
          ) : null}
        </div>
      );
    }
    return (
      <div className={clsx(base, 'text-slate-400')}>
        {type === 'image' ? <ImageIcon size={28} /> : <VideoIcon size={28} />}
      </div>
    );
  }
  return (
    <div className={clsx(base, 'text-slate-400')}>
      <AudioIcon size={28} />
    </div>
  );
};

// 转换为 UI 使用的数据类型
const mapApiToUiMaterial = (apiMaterial: ApiMaterial): Material => {
  // 类型映射：1=图片，2=视频，3=音频
  const typeMap: Record<number, 'image' | 'video' | 'audio'> = {
    1: 'image',
    2: 'video',
    3: 'audio',
  };

  // 格式化文件大小
  const formatFileSize = (size: string) => {
    const sizeNum = Number.parseInt(size, 10);
    if (sizeNum < 1024 * 1024) {
      return `${(sizeNum / 1024).toFixed(1)}KB`;
    }
    return `${(sizeNum / (1024 * 1024)).toFixed(1)}MB`;
  };

  // 格式化时长
  const formatDuration = (seconds?: number) => {
    if (!seconds) {
      return;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 生成缩略图URL
  const getThumbnail = () => {
    // 如果有 cover，直接使用
    if (apiMaterial.cover) {
      return apiMaterial.cover;
    }

    if (apiMaterial.type === 1) {
      // 1 = 图片
      return `${apiMaterial.url}??x-image-process=image/resize,m_fill,w_200,h_200,limit_0/format,jpg`;
    }
    if (apiMaterial.type === 2) {
      // 2 = 视频
      return `${apiMaterial.url}?x-workflow-graph-name=video-thumbnail`;
    }

    // 其他情况返回undefined
    return;
  };

  return {
    id: apiMaterial.mediaId,
    observationId: apiMaterial.observationId,
    title: apiMaterial.name,
    type: typeMap[apiMaterial.type] || 'video',
    size: formatFileSize(apiMaterial.fileSize),
    author: apiMaterial.createUser.name,
    date: apiMaterial.createTime,
    duration: formatDuration(apiMaterial.duration),
    status: apiMaterial.handleStatus,
    thumbnail: getThumbnail(), // 使用新的缩略图生成逻辑
  };
};

type MaterialType = 'video' | 'audio' | 'image';

export default function App() {
  const searchParams = useSearchParams();
  const projectId = searchParams?.get('projectId') || undefined;
  const classId = searchParams?.get('classId') || undefined;
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '素材管理';
    }
  }, []);

  const router = useRouter();
  const [activeKey, setActiveKey] = useState<'all' | MaterialType>('all');
  const pageSize = 10;
  const version = useCommonStore((state) => state.version);
  const brand = useCommonStore((state) => state.brand);

  const {
    data: infiniteMaterialData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    refetch,
  } = useInfiniteQuery({
    queryKey: ['materialList', projectId],
    queryFn: async ({ pageParam }) => {
      const response = await getMaterialList({
        projectId,
        page: pageParam,
        perPage: pageSize,
      });

      const responseData = response as unknown as Record<string, unknown>;
      let materials: unknown[] = [];
      if (Array.isArray(responseData)) {
        materials = responseData;
      } else if (Array.isArray(responseData.list as unknown[])) {
        materials = responseData.list as unknown[];
      }

      return {
        data: materials,
        page: pageParam,
        hasMore: materials.length >= pageSize,
      };
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (!lastPage.hasMore) {
        return;
      }
      return lastPage.page + 1;
    },
  });

  useEffect(() => {
    getMessage(onMessage);
  }, []);

  // 获取到原生通知
  const onMessage = useCallback(
    (event: { data: string }) => {
      console.log('获取到原生通知 data: ', event);
      try {
        const data = JSON.parse(event.data);
        console.log('🚀 ~ data:', data);
        if (data.activity_on_resume) {
          refetch();
        }
      } catch (error) {
        console.log('onMessage', error);
      }
    },
    [refetch]
  );

  // 将 API 数据转换为 UI 数据
  const materialsList = infiniteMaterialData?.pages
    ? infiniteMaterialData.pages.flatMap((page) => {
        if (page?.data) {
          return page.data.map((item) =>
            mapApiToUiMaterial(item as ApiMaterial)
          );
        }
        return [];
      })
    : [];

  // 根据选择的类型本地过滤
  const filteredMaterials =
    activeKey === 'all'
      ? materialsList
      : materialsList.filter((material) => material.type === activeKey);

  // 按 observationId 分组
  const groupedMaterials = filteredMaterials.reduce(
    (acc, material) => {
      const key = material.observationId;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(material);
      return acc;
    },
    {} as Record<string, Material[]>
  );

  // 加载更多数据
  const loadMore = async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  };

  // 渲染单个素材项的辅助函数
  const renderMaterialItem = (material: Material) => (
    <button
      className="group media-item mb-3 w-full overflow-hidden rounded-xl bg-white text-left shadow-sm transition-shadow duration-200 hover:shadow-md"
      key={material.id}
      onClick={() => router.push(`/pbl/material/detail?id=${material.id}`)}
      type="button"
    >
      <div className="flex items-start p-3">
        <div className="relative">
          <Thumb thumbnail={material.thumbnail} type={material.type} />
          {material.duration && (
            <span className="-bottom-1 -right-1 absolute rounded bg-white px-1 text-gray-500 text-xs shadow">
              {material.duration}
            </span>
          )}
        </div>
        <div className="ml-4 flex-1">
          <div className="flex items-start justify-between">
            <h3 className="flex-1 whitespace-break-spaces break-all text-base text-gray-800">
              {material.title || '未命名文件'}
            </h3>
            {material.type === 'video' && (
              <div className="flex items-center space-x-2">
                <StatusBadge status={material.status} />
              </div>
            )}
          </div>
          <div className="mt-1 flex items-center text-gray-500 text-xs">
            <span>{material.size}</span>
          </div>
          <div className="mt-2 flex items-center text-gray-500 text-xs">
            <UserCircle className="mr-1 h-3 w-3 text-gray-600" />
            <span className="mr-3">{material.author}</span>
            <span>{material.date}</span>
          </div>
        </div>
      </div>
    </button>
  );

  // 处理下拉刷新
  const handleRefresh = async () => {
    await refetch();
  };

  if (isLoading) {
    return (
      <div className="py-8 text-center">
        <div className="text-gray-500">加载中...</div>
      </div>
    );
  }

  return (
    <main className="min-h-screen bg-slate-50">
      <PullToRefresh
        canReleaseText="释放立即刷新"
        completeDelay={500}
        completeText="刷新成功"
        onRefresh={handleRefresh}
      >
        <div className="-mx-4 sticky top-0 z-10 mb-4 bg-white/70 backdrop-blur">
          <Tabs
            activeKey={activeKey}
            className="[&_.adm-tabs-tab-active]:text-slate-900 [&_.adm-tabs-tab]:text-slate-600"
            onChange={(k) => setActiveKey(k as 'all' | MaterialType)}
            stretch
          >
            <Tabs.Tab key="all" title="全部" />
            <Tabs.Tab key="image" title="图片" />
            <Tabs.Tab key="video" title="视频" />
            <Tabs.Tab key="audio" title="音频" />
          </Tabs>
        </div>
        {Object.keys(groupedMaterials).length === 0 && !isLoading ? (
          <main className="flex flex-col items-center justify-center bg-slate-50 pt-20">
            <Empty title="暂无素材" />
          </main>
        ) : (
          <div className="mb-12">
            {Object.entries(groupedMaterials).map(
              ([observationId, materialsInGroup]) => {
                if (materialsInGroup.length === 1) {
                  const singleMaterial = materialsInGroup[0];
                  if (singleMaterial) {
                    return renderMaterialItem(singleMaterial);
                  }
                  return null;
                }
                return (
                  <div
                    className="mb-4 rounded-xl border border-indigo-50 bg-indigo-50 p-2 pb-0 shadow-sm"
                    key={observationId}
                  >
                    {materialsInGroup.map((material) =>
                      renderMaterialItem(material)
                    )}
                  </div>
                );
              }
            )}
            {/* 无限滚动加载组件 */}
            <InfiniteScroll
              className="!py-2"
              hasMore={!!hasNextPage}
              loadMore={loadMore}
              threshold={250}
            >
              {(() => {
                if (isFetchingNextPage) {
                  return (
                    <div className="py-3 text-center">
                      <span className="text-gray-500 text-sm">
                        加载更多数据...
                      </span>
                    </div>
                  );
                }
                if (hasNextPage) {
                  return (
                    <div className="py-3 text-center">
                      <span className="text-gray-500 text-sm">
                        上拉加载更多
                      </span>
                    </div>
                  );
                }
                if (
                  Object.keys(groupedMaterials).length > 0 &&
                  filteredMaterials.length > 0
                ) {
                  return (
                    <div className="py-3 text-center">
                      <span className="text-gray-500 text-sm">
                        没有更多数据了
                      </span>
                    </div>
                  );
                }
                return null;
              })()}
            </InfiniteScroll>
          </div>
        )}
      </PullToRefresh>
      <button
        className="fixed right-6 bottom-10 flex h-14 w-14 items-center justify-center rounded-full bg-violet-500 text-white shadow-lg transition-colors hover:bg-violet-600"
        onClick={() => {
          if (
            (brand === '1' &&
              compare(version, '1.33.0', '>') &&
              compare(version, '1.34.0', '<')) ||
            (brand === '2' &&
              compare(version, '6.21.2', '>=') &&
              compare(version, '6.22.0', '<'))
          ) {
            navigationToNativePage(
              'app://app/pbl/addMaterials?videoCompressBitrate=10000000'
            );
          } else {
            // 构建查询参数，只有参数存在时才添加
            const params = new URLSearchParams();
            if (projectId) {
              params.append('projectId', projectId);
            }
            if (classId) {
              params.append('classId', classId);
            }

            const queryString = params.toString();
            const url = `/pbl/material/create${queryString ? `?${queryString}` : ''}`;

            router.push(url);
          }
        }}
        type="button"
      >
        <Plus className="h-6 w-6" />
      </button>
    </main>
  );
}
