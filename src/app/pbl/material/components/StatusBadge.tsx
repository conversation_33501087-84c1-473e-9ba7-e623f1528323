import type React from 'react'; // 使用类型导入

// 定义 Status 类型，与 mockMaterials.ts 中的保持一致
type Status = number;

interface StatusBadgeProps {
  status: Status | string; // 允许传入 string 以处理未知状态
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  let bgColor = 'bg-yellow-50';
  let textColor = 'text-yellow-500';
  let text = '未知';

  // 0-待处理 1-处理中 2-已处理 3-处理失败

  switch (status) {
    case 0:
      bgColor = 'bg-amber-50';
      textColor = 'text-amber-500';
      text = '待处理';
      break;
    case 1:
      bgColor = 'bg-blue-50';
      textColor = 'text-blue-500';
      text = '处理中';
      break;
    case 2:
      bgColor = 'bg-green-50';
      textColor = 'text-green-500';
      text = '已处理';
      break;
    case 3:
      bgColor = 'bg-red-50';
      textColor = 'text-red-500';
      text = '处理失败';
      break;
    default:
      // 保持默认的 "未知" 状态样式
      break;
  }

  return (
    <span
      className={`inline-block text-nowrap break-normal rounded-full px-2.5 py-1 text-xs shadow-sm ${bgColor} ${textColor}`}
    >
      {text}
    </span>
  );
};

export default StatusBadge;
