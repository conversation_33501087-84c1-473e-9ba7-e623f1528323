'use client';

import { useInfiniteQuery } from '@tanstack/react-query';
import {
  Dialog,
  ErrorBlock,
  InfiniteScroll,
  PullToRefresh,
  Toast,
} from 'antd-mobile';
import { Loader2, Plus } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { deleteProject, getProjectList } from '@/api/pbl';
import ProjectCard from './components/ProjectCard';

interface Project {
  instId?: string;
  deptId?: string;
  projectId: string;
  projectName: string;
  description: string;
  theme?: string;
  drivingQuestion?: string;
  learningGoals?: string;
  keyword?: string;
  startDate: string;
  endDate: string;
  status?: number;
  delFlag?: number;
  createUserId?: string;
  createTime: string;
  updateTime: string;
}

// 空状态组件
const EmptyState = ({ onCreateProject }: { onCreateProject: () => void }) => (
  <div className="flex min-h-[60vh] flex-col items-center justify-center px-8 text-center">
    <div className="mb-6 text-6xl">📋</div>
    <h3 className="mb-2 font-medium text-gray-900 text-lg">还没有项目</h3>
    <p className="mb-6 text-gray-500 text-sm">
      创建你的第一个 PBL 项目，开始精彩的学习之旅
    </p>
    <button
      className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-400 px-6 py-2 font-medium text-white shadow-lg transition-all hover:shadow-xl"
      onClick={onCreateProject}
      type="button"
    >
      <div className="flex items-center">
        <Plus size={18} />
        <span className="ml-1">创建项目</span>
      </div>
    </button>
  </div>
);

// 错误状态组件
const ErrorState = ({ onRetry }: { onRetry: () => void }) => (
  <div className="flex min-h-[60vh] flex-col items-center justify-center px-8 text-center">
    <ErrorBlock
      description="请检查网络连接后重试"
      status="default"
      title="加载失败"
    />
    <button
      className="mt-4 rounded-lg bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
      onClick={onRetry}
      type="button"
    >
      重新加载
    </button>
  </div>
);

const pageSize = 10;

export default function PBLPage() {
  const router = useRouter();

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = 'PBL 项目';
    }
  }, []);

  // 使用 useInfiniteQuery 获取项目列表
  const {
    data: infiniteProjectData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    refetch,
  } = useInfiniteQuery({
    queryKey: ['projectList'],
    queryFn: async ({ pageParam }) => {
      const response = await getProjectList({
        page: pageParam.toString(),
        perPage: pageSize.toString(),
      });

      // 处理 API 响应数据结构
      const responseData = response?.data || response;
      const projectList = responseData?.list || [];

      return {
        data: projectList,
        page: pageParam,
        hasMore: projectList.length >= pageSize,
      };
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      // 如果没有更多数据，返回 undefined 表示不需要加载下一页
      if (
        !(lastPage.hasMore && lastPage.data) ||
        lastPage.data.length < pageSize
      ) {
        return;
      }
      return lastPage.page + 1;
    },
    staleTime: 60 * 1000, // 1分钟内不会自动重新获取数据
    refetchOnWindowFocus: false, // 窗口聚焦时不重新获取数据
  });

  // 加载更多数据的处理函数
  const loadMore = async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  };

  // 下拉刷新处理函数
  const handleRefresh = async () => {
    await refetch();
  };

  // 删除项目处理函数
  const handleDelete = async (projectId: string) => {
    const result = await Dialog.confirm({
      content: '确定要删除该项目吗？',
    });

    if (result) {
      try {
        await deleteProject(projectId);
        Toast.show({
          icon: 'success',
          content: '删除成功',
        });
        // 删除成功后刷新数据
        refetch();
      } catch (_error) {
        Toast.show({
          icon: 'fail',
          content: '删除失败',
        });
      }
    }
  };

  // 从 infiniteProjectData 中提取项目列表数据
  const projectsList = infiniteProjectData?.pages
    ? infiniteProjectData.pages.flatMap((page) => page?.data || [])
    : [];

  return (
    <div className="min-h-screen bg-slate-50">
      <PullToRefresh
        canReleaseText="释放立即刷新"
        completeDelay={500}
        completeText="刷新成功"
        onRefresh={handleRefresh}
      >
        <div className="p-4">
          {(() => {
            // 初始加载状态
            if (isLoading) {
              return (
                <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-slate-50 via-white to-slate-50">
                  <div className="text-center">
                    <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-4 border-indigo-200 border-t-indigo-600" />
                    <p className="text-gray-600">正在加载项目列表...</p>
                  </div>
                </div>
              );
            }

            // 错误状态
            if (isError) {
              return <ErrorState onRetry={() => refetch()} />;
            }

            // 空状态
            if (projectsList.length === 0) {
              return (
                <EmptyState
                  onCreateProject={() => router.push('/pbl/create')}
                />
              );
            }

            // 正常状态 - 显示项目列表
            return (
              <div className="mb-[100px]">
                <div className="space-y-4">
                  {projectsList.map((project: Project) => (
                    <ProjectCard
                      key={project.projectId}
                      onDelete={handleDelete}
                      project={project}
                    />
                  ))}
                </div>

                {/* 无限滚动组件 */}
                <InfiniteScroll
                  className="!py-4"
                  hasMore={!!hasNextPage}
                  loadMore={loadMore}
                  threshold={250}
                >
                  {(() => {
                    if (isFetchingNextPage) {
                      return (
                        <div className="flex items-center justify-center py-4">
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          <span className="text-gray-500 text-sm">
                            加载更多数据...
                          </span>
                        </div>
                      );
                    }
                    if (hasNextPage) {
                      return (
                        <div className="py-3 text-center">
                          <span className="text-gray-500 text-sm">
                            上拉加载更多
                          </span>
                        </div>
                      );
                    }
                    return (
                      <div className="py-3 text-center">
                        <span className="text-gray-400 text-sm">
                          没有更多数据了
                        </span>
                      </div>
                    );
                  })()}
                </InfiniteScroll>
              </div>
            );
          })()}
        </div>
      </PullToRefresh>

      {/* 新建项目按钮 */}
      <div className="fixed right-0 bottom-0 left-0 flex items-center justify-center pb-3">
        <button
          className="rounded-3xl bg-gradient-to-r from-indigo-600 to-indigo-400 px-6 py-2 font-medium text-base text-white shadow-lg transition-all hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-blue-300"
          onClick={() => router.push('/pbl/create')}
          type="button"
        >
          <div className="flex items-center justify-center">
            <Plus size={20} />
            <span className="ml-1">新建项目</span>
          </div>
        </button>
      </div>
    </div>
  );
}
