import clsx from 'clsx';
import { useAtom } from 'jotai';
import { ChevronDown } from 'lucide-react';
import React, { memo, useEffect, useMemo, useState } from 'react';
import { getObservationEvaluation } from '@/api/pbl';
import { evaluationAtom } from '@/store/pbl';

// 定义评级等级的类型
interface RatingLevel {
  id: string;
  level: number;
  emoji: string;
  label: string;
  description: string;
}

// 定义单个量表数据的类型
interface ScaleData {
  id: string;
  title: string;
  subtitle: string;
  levels: RatingLevel[];
}

// RatingScale 组件 Props 类型定义
interface RatingScaleProps {
  scaleTitle: string;
  scaleSubtitle: string;
  levels: RatingLevel[];
  onSelectLevel: (levelId: string) => void;
  studentId: string; // 添加 studentId prop
}

// 内部 RatingScale 组件保持不变，但添加 Props 类型
function RatingScale({
  scaleTitle,
  scaleSubtitle,
  levels,
  onSelectLevel,
  studentId, // 接收 studentId
}: RatingScaleProps) {
  // 使用 atom 获取特定学生的数据
  const [allEvaluations] = useAtom(evaluationAtom);
  const studentEvaluations = allEvaluations[studentId] || {};

  const [isOpen, setIsOpen] = useState(false);

  // 获取当前量表中已选中的等级 ID（如果有）
  const selectedLevelId = useMemo(() => {
    // 检查 studentEvaluations 中是否有 level.id 为 true 的项
    return levels.find((level) => studentEvaluations[level.id])?.id || '';
  }, [levels, studentEvaluations]); // 依赖 studentEvaluations
  return (
    <div className="mb-4 overflow-hidden rounded-lg border border-gray-200 bg-white shadow-subtle">
      {/* 可点击的标题栏 */}
      <button
        className="flex w-full items-center justify-between bg-gray-50 px-4 py-3 text-left transition-colors duration-150"
        onClick={(e) => {
          e.stopPropagation();
          setIsOpen(!isOpen);
        }}
        type="button"
      >
        <div>
          <h3 className="font-semibold text-base text-brand-text-primary">
            {scaleTitle}
          </h3>
          <p className="text-stone-500 text-xs">{scaleSubtitle}</p>
        </div>
        <ChevronDown
          className="h-5 w-5 text-medium-text transition-transform duration-300"
          style={{ transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)' }}
        />
      </button>

      {/* 可折叠的内容区域 */}
      <div
        className={clsx(
          'overflow-hidden transition-all duration-500 ease-in-out',
          isOpen ? 'max-h-[1200px]' : 'max-h-0'
        )}
      >
        <div className="p-4">
          {/* Vertical list layout */}
          <div className="flex flex-col">
            {levels.map((level) => (
              <div
                className={clsx(
                  'mb-2 flex cursor-pointer items-center space-x-3 rounded-lg border-2 p-3 transition-colors duration-150',
                  selectedLevelId === level.id
                    ? 'border-indigo-400 bg-indigo-50'
                    : 'border-gray-300 hover:border-gray-400'
                )}
                key={level.level}
                onClick={(e) => {
                  e.stopPropagation();
                  // 调用 onSelectLevel 函数，传递当前等级的 ID
                  // 如果当前等级已被选中，则传递空字符串表示取消选择
                  onSelectLevel(selectedLevelId === level.id ? '' : level.id);
                }}
              >
                <div
                  aria-hidden="true"
                  className={clsx(
                    'flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full text-[1.8rem] leading-none'
                  )}
                >
                  {level.emoji}
                </div>
                <div className="flex-1 pt-1">
                  <span
                    className={clsx(
                      'block font-semibold text-sm',
                      selectedLevelId === level.id
                        ? 'text-brand-primary'
                        : 'text-brand-text-primary'
                    )}
                  >
                    {level.label} (等级 {level.level})
                  </span>
                  <p className="mt-0.5 text-brand-text-secondary text-xs leading-snug">
                    {level.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// --- 主组件 ---
function LeuvenScales({ studentId }: { studentId: string }) {
  const [, setAllEvaluations] = useAtom(evaluationAtom); // 获取 setter 用于更新

  const scalesData = useMemo<ScaleData[]>(
    () => [
      {
        id: 'wellBeing',
        title: '幸福感',
        subtitle: '孩子感觉自在、放松和自信的程度',
        levels: [
          {
            id: '66',
            level: 1,
            emoji: '😥',
            label: '极低',
            description: '持续表现出痛苦、焦虑或恐惧等负面情绪。',
          },
          {
            id: '67',
            level: 2,
            emoji: '🙁',
            label: '低',
            description: '经常显得不自在、紧张或不开心，较少主动探索。',
          },
          {
            id: '68',
            level: 3,
            emoji: '😐',
            label: '中等',
            description: '情绪和行为状态有波动，有时放松有时紧张。',
          },
          {
            id: '69',
            level: 4,
            emoji: '🙂',
            label: '高',
            description: '大部分时间感觉舒适、放松、自信，享受活动。',
          },
          {
            id: '70',
            level: 5,
            emoji: '😄',
            label: '极高',
            description: '表现出极大的愉悦、自信和活力，能自我表达。',
          },
        ],
      },
      {
        id: 'involvement',
        title: '投入度',
        subtitle: '孩子在活动中专注、兴趣和能量的程度',
        levels: [
          {
            id: '71',
            level: 1,
            emoji: '😴',
            label: '极低',
            description: '完全没有参与活动，或进行无目的、短暂的活动。',
          },
          {
            id: '72',
            level: 2,
            emoji: '👀',
            label: '低',
            description: '注意力短暂，容易分心，参与肤浅，缺乏兴趣。',
          },
          {
            id: '73',
            level: 3,
            emoji: '🤔',
            label: '中等',
            description: '有时能投入活动，但专注度不够持续或深入。',
          },
          {
            id: '74',
            level: 4,
            emoji: '✨',
            label: '高',
            description: '大部分时间能专注、持续地参与活动，表现出兴趣。',
          },
          {
            id: '75',
            level: 5,
            emoji: '🔥',
            label: '极高',
            description: '强烈的专注、能量和持续性，完全沉浸在活动中。',
          },
        ],
      },
    ],
    []
  );

  useEffect(() => {
    getObservationEvaluation({
      dimensionType: 3,
    }).then((res) => {
      console.log(res);
    });
  }, []);

  return (
    <div>
      <div className="flex flex-col overflow-y-auto">
        {scalesData.map((scale) => (
          <RatingScale
            key={scale.id}
            levels={scale.levels}
            onSelectLevel={(levelId) => {
              setAllEvaluations((prevAll) => {
                const currentStudentEvals = { ...(prevAll[studentId] || {}) };

                // 将当前量表的所有 level ID 设为 false (使用 for...of)
                for (const level of scale.levels) {
                  currentStudentEvals[level.id] = false;
                }

                // 如果传入了 levelId (非空)，则将其设为 true
                if (levelId) {
                  currentStudentEvals[levelId] = true;
                }

                return {
                  ...prevAll,
                  [studentId]: currentStudentEvals,
                };
              });
            }}
            scaleSubtitle={scale.subtitle}
            scaleTitle={scale.title} // 传递 studentId
            studentId={studentId}
          />
        ))}
      </div>
    </div>
  );
}

export default memo(LeuvenScales);
