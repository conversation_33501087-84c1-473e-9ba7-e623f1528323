import clsx from 'clsx';
import { Check, ChevronDown, ChevronRight, ChevronUp } from 'lucide-react';
import { useCallback, useState } from 'react';

import { bloomEvaluationData } from './data';

// Define data interfaces
interface EvaluationItemData {
  id: string | number;
  title: string;
  description: string;
  examples?: string[];
  isSelected?: boolean;
}

interface EvaluationCategoryData {
  level: string;
  color: string;
  items: EvaluationItemData[];
}

// Props interfaces
interface EvaluationItemProps {
  item: EvaluationItemData;
  onUpdate: (id: string | number, isSelected: boolean) => void;
}

interface EvaluationCategoryProps {
  category: EvaluationCategoryData;
  onUpdate: (id: string | number, isSelected: boolean) => void;
}

// 评价项目组件
function EvaluationItem({ item, onUpdate }: EvaluationItemProps) {
  const [isExamplesExpanded, setIsExamplesExpanded] = useState(false); // 示例默认折叠

  // 点击整个卡片切换选中状态
  const handleSelectToggle = () => {
    onUpdate(item.id, !item.isSelected); // 更新 App 中的状态
  };

  // 点击切换示例展开/折叠
  const toggleExamples = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation(); // 阻止事件冒泡到卡片点击事件
    setIsExamplesExpanded(!isExamplesExpanded);
  };

  return (
    <div
      className={clsx(
        'relative mb-4 rounded-xl border bg-white p-4 shadow-md',
        item.isSelected
          ? 'border-indigo-400 bg-indigo-50' // Highlight background for selected row
          : 'border-gray-300' // Hover effect for non-selected rows
      )}
      onClick={handleSelectToggle} // 点击卡片切换选中
    >
      <h4 className="mb-1.5 font-semibold">{item.title}</h4>
      <p className="mb-2 text-sm">{item.description}</p>
      <div
        className={clsx(
          'absolute top-2 right-2 flex size-5 items-center justify-center rounded-full p-1 shadow-sm',
          item.isSelected
            ? 'bg-indigo-500' // Highlight background for selected row
            : 'bg-gray-300'
        )}
      >
        <Check className="size-5 text-white" />
      </div>

      {/* 示例区域 - 可折叠 */}
      {item.examples && item.examples.length > 0 && (
        <div>
          <button
            className="mb-2 flex items-center font-medium text-blue-600 text-xs hover:text-blue-700 focus:outline-none"
            onClick={toggleExamples}
            type="button"
          >
            {isExamplesExpanded ? (
              <ChevronDown className="mr-1 size-3.5" />
            ) : (
              <ChevronRight className="mr-1 size-3.5" />
            )}
            {isExamplesExpanded ? '隐藏示例' : '显示示例'} (
            {item.examples?.length || 0})
          </button>
          {/* 示例列表 - 条件渲染 + 过渡效果 */}
          <div
            className={clsx(
              'overflow-hidden transition-all duration-300 ease-in-out',
              isExamplesExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0' // 使用 max-height 和 opacity 实现过渡
            )}
            // style={{ maxHeight: examplesMaxHeight }} // 使用 JS 计算高度的动画方式
          >
            <ul className="my-1 list-inside list-disc space-y-1 pl-1 text-gray-500 text-xs">
              {item.examples?.map((example) => (
                <li key={example}>{example}</li>
              ))}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}

// 评价分类组件
function EvaluationCategory({ category, onUpdate }: EvaluationCategoryProps) {
  const [isExpanded, setIsExpanded] = useState(true); // 分类默认展开

  // 切换分类展开/折叠
  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  // Tailwind color classes mapping
  const colorClasses: { [key: string]: string } = {
    blue: 'text-blue-600 border-blue-200 hover:bg-blue-50',
    green: 'text-green-600 border-green-200 hover:bg-green-50',
    yellow: 'text-yellow-600 border-yellow-200 hover:bg-yellow-50',
    purple: 'text-purple-600 border-purple-200 hover:bg-purple-50',
    pink: 'text-pink-600 border-pink-200 hover:bg-pink-50',
    red: 'text-red-600 border-red-200 hover:bg-red-50',
    gray: 'text-gray-600 border-gray-200 hover:bg-gray-50',
  };
  // Explicitly check and assign default value
  const initialCategoryColorClass = colorClasses[category.color];
  const categoryColorClass = initialCategoryColorClass ?? colorClasses.gray; // Use nullish coalescing for default
  const headerTextColor = categoryColorClass.split(' ')[0]; // Get the text color class

  return (
    <section className="mb-4">
      {/* 可点击的分类标题 */}
      <div
        className={clsx(
          'flex cursor-pointer items-center justify-between rounded-t-lg border-b-2 p-3 transition-colors duration-200',
          categoryColorClass,
          !isExpanded && 'rounded-b-lg' // 折叠时底部也加圆角
        )}
        onClick={toggleExpand}
      >
        <h3
          className={clsx('font-semibold font-serif text-lg', headerTextColor)}
        >
          {category.level}
        </h3>
        {isExpanded ? (
          <ChevronUp
            className={clsx(
              'size-5 transition-transform duration-300',
              headerTextColor
            )}
          />
        ) : (
          <ChevronDown
            className={clsx(
              'size-5 transition-transform duration-300',
              headerTextColor
            )}
          />
        )}
      </div>

      {/* 可折叠的内容区域 */}
      <div
        className={clsx(
          'overflow-hidden transition-all duration-300 ease-in-out',
          isExpanded ? 'opacity-100' : 'max-h-0 opacity-0' // 使用足够大的 max-height
        )}
      >
        <div className="px-1 pt-4">
          {category.items.map((item) => (
            <EvaluationItem item={item} key={item.id} onUpdate={onUpdate} />
          ))}
        </div>
      </div>
    </section>
  );
}

function Bloom() {
  const [evaluationState, setEvaluationState] = useState<
    EvaluationCategoryData[]
  >(
    // 初始化时将 isSelected 状态从模拟数据同步
    bloomEvaluationData.map((cat) => ({
      ...cat,
      items: cat.items.map((it) => ({
        ...it,
        isSelected: it.isSelected ?? false,
      })), // 确保 isSelected 存在
    }))
  );

  // 更新评价选中状态的回调函数
  const handleEvaluationUpdate = useCallback(
    (itemId: string | number, newSelectedState: boolean) => {
      setEvaluationState((prevState) =>
        prevState.map((category) => ({
          ...category,
          items: category.items.map((item) =>
            item.id === itemId
              ? { ...item, isSelected: newSelectedState }
              : item
          ),
        }))
      );
    },
    []
  );

  return (
    <div className="h-full w-full bg-gray-50">
      {/* 主要内容区域 */}
      <main>
        {evaluationState.map((category) => (
          <EvaluationCategory
            category={category}
            key={category.level}
            onUpdate={handleEvaluationUpdate}
          />
        ))}
      </main>
    </div>
  );
}

export default Bloom;
