/** biome-ignore-all lint/suspicious/noExplicitAny: <explanation> */
'use client';

import { SpinLoading, Toast } from 'antd-mobile';
import { useSetAtom } from 'jotai';
import {
  Calendar,
  CalendarRange,
  ClipboardList,
  Edit3,
  Image as ImageIcon,
  MapPin,
  Video,
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useImmer } from 'use-immer';
import { getObservationDetail, updateObservation } from '@/api/pbl';
import { mediaAtom } from '@/store/pbl';
import Media from '../detail/components/Media';
import EditTextDialog from '../detail/day/components/EditTextDialog';
import AssociatedStudents from './components/AssociatedStudents';
import Conversation from './components/Conversation';
import FooterActions from './components/FooterActions';
import ImageGrid from './components/ImageGrid';

interface AbilityTarget {
  id: number;
  name: string;
  completed: boolean;
}

interface AbilityCategory {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  targets: AbilityTarget[];
  subCategories?: {
    name: string;
    targets: AbilityTarget[];
  }[];
}

interface Student {
  id: number; // 与StudentCard组件中的类型保持一致
  name: string;
  avatar: string;
  abilities: string[];
  abilityCategories?: AbilityCategory[];
  progress?: string;
  evaluation: {
    observationId: string;
    observationText: string; // 与StudentCard组件中的类型保持一致
    abilities: {
      abilityId: string;
    }[];
  };
}

interface MediaItem {
  mediaId?: number;
  url: string;
  name?: string;
  duration?: string | number;
  type: number | string;
  cover?: string;
}

// 定义与mediaAtom中medias数组类型匹配的接口

interface Record {
  date: string;
  source: string;
  content: string;
  nextStepPlan: string;
  students: Student[];
  medias: MediaItem[];
  createUser: {
    name: string;
    avatar: string;
  };
  createTime: string;
  deptId: string;
  conversation: ConversationItem[];
  tags: {
    tagId: number;
    tagName: string;
  }[];
  zone: {
    zoneName: string;
  };
  videoImages: string[];
}

// 为StudentCard组件定义兼容的Student类型

interface ConversationItem {
  speaker: string;
  text: string;
}

export default function App() {
  const searchParams = useSearchParams();
  const observationId = searchParams?.get('observationId');

  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [record, setRecord] = useImmer<Record>({
    date: '',
    content: '',
    nextStepPlan: '',
    students: [] as Student[],
    medias: [] as MediaItem[],
    createUser: {
      name: '',
      avatar: '',
    },
    createTime: '',
    deptId: '',
    conversation: [],
    zone: {
      zoneName: '',
    },
    source: '1',
    tags: [],
    videoImages: [],
  });

  const [isEditRecordDialogVisible, setIsEditRecordDialogVisible] =
    useState(false);
  const [isEditSuggestDialogVisible, setIsEditSuggestDialogVisible] =
    useState(false);
  const setMedia = useSetAtom(mediaAtom);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '观察记录详情';
    }
  }, []);

  useEffect(() => {
    if (observationId) {
      setLoading(true);
      getObservationDetail(observationId)
        .then((res) => {
          try {
            const data = res.data || res;
            let videoImages: string[] = [];
            if (data.cdnVedioimg) {
              videoImages = JSON.parse(data.cdnVedioimg);
            }
            setRecord({
              ...data,
              videoImages,
            });
          } catch (error) {
            console.error(error);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [observationId]);

  const onRecordUpdate = async (text: string | undefined) => {
    if (!text) {
      return;
    }
    if (!observationId) {
      return;
    }
    await updateObservation(observationId, {
      title: '',
      content: text,
      nextStepPlan: record?.nextStepPlan,
      observationId,
    });
    Toast.show({
      content: '更新成功',
    });
  };

  const onSuggestUpdate = async (text: string | undefined) => {
    if (!text) {
      return;
    }
    if (!observationId) {
      return;
    }

    await updateObservation(observationId, {
      title: '',
      nextStepPlan: text,
      content: record?.content,
      observationId,
    });
    Toast.show({
      content: '更新成功',
    });
  };

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <SpinLoading color="primary" />
      </div>
    );
  }

  return (
    <div className="relative flex h-screen flex-col">
      {/* 可滚动内容区域 */}
      <div className="flex-grow overflow-y-auto pb-20">
        <div className="my-6 px-4">
          <div className="mb-6 flex items-center gap-2">
            <div className="flex items-center">
              <picture>
                <img
                  alt="老师头像"
                  className="h-10 w-10 rounded-full"
                  src={record.createUser?.avatar || ''}
                />
              </picture>
            </div>
            <div>
              <span className="text-base">{record.createUser?.name}</span>
              <div className="flex items-center gap-1 text-gray-500 text-xs">
                <Calendar className="h-3 w-3" />
                <span>{record.createTime}</span>
              </div>
            </div>
          </div>
          {/* 观察地点 */}
          {record.zone?.zoneName && (
            <div className="mb-6 flex items-center">
              <h3 className="flex items-center gap-2 font-bold text-base text-gray-700">
                <MapPin className="h-5 w-5 text-green-500" />
                <span>观察地点：</span>
              </h3>
              <div className="text-base text-gray-700">
                {record.zone.zoneName}
              </div>
            </div>
          )}
          {/* 观察记录内容 */}
          <div className="mb-6">
            <h3 className="mb-2 flex items-center gap-2 font-bold text-base text-gray-700">
              <ClipboardList className="h-5 w-5 text-amber-500" />
              <span>记录内容</span>
            </h3>
            <div className="rounded-2xl bg-amber-50 p-4 text-base text-gray-700 leading-relaxed">
              {record.content}
              <button
                className="inline-block p-1 text-gray-500 transition-colors hover:text-primary"
                onClick={() => setIsEditRecordDialogVisible(true)}
                type="button"
              >
                <Edit3 className="h-4 w-4 text-indigo-500" />
              </button>
            </div>
          </div>

          {/* 标签 */}
          {record.tags && Array.isArray(record.tags) && (
            <div className="mb-6 flex flex-wrap gap-2">
              {record.tags?.map((tag: any, index) => {
                const tagColors = [
                  'bg-green-50 text-green-700',
                  'bg-orange-50 text-orange-700',
                  'bg-purple-50 text-purple-700',
                  'bg-blue-50 text-blue-700',
                  'bg-pink-50 text-pink-700',
                ];
                return (
                  <span
                    className={`mr-2 rounded-full px-3 py-1.5 text-sm ${
                      tagColors[index % tagColors.length]
                    }`}
                    key={tag.tagId}
                  >
                    #{tag.tagName}
                  </span>
                );
              })}
            </div>
          )}
        </div>

        <AssociatedStudents
          deptId={record.deptId}
          observationId={observationId || ''}
          students={record.students || []}
        />
        <div className="px-4">
          <h3 className="mb-3 flex items-center justify-between font-bold text-gray-700 text-md">
            <div className="flex items-center gap-2">
              <Video className="h-5 w-5 text-green-500" />
              <span>关联文件</span>
              <span className="font-normal text-gray-500 text-xs">
                ({record.medias.length})
              </span>
            </div>
            <div>
              <button
                className="flex items-center gap-1 rounded-full bg-gray-50 px-3 py-1.5 text-gray-600 text-xs transition-colors hover:bg-gray-100"
                onClick={() => {
                  setMedia({
                    observationId: observationId || '',
                    deptId: record.deptId,
                    medias: record.medias.map((media: any) => {
                      let mediaType: string;
                      if (media.type === 1) {
                        mediaType = 'image';
                      } else if (media.type === 2) {
                        mediaType = 'video';
                      } else {
                        mediaType = 'audio';
                      }

                      return {
                        ...media,
                        status: 'completed',
                        id: media.mediaId,
                        type: mediaType,
                      };
                    }),
                  });
                  router.push('/pbl/record/update/media');
                }}
                type="button"
              >
                {record.medias.length > 0 ? '修改文件' : '添加文件'}
              </button>
            </div>
          </h3>
          <Media media={record.medias} />
        </div>

        {/* 视频截帧图片 */}
        {record.videoImages.length > 0 && (
          <div className="my-6 px-4">
            <h3 className="mb-3 flex items-center gap-2 font-bold text-gray-700 text-md">
              <ImageIcon className="h-5 w-5 text-pink-500" />
              <span>相关图片</span>
            </h3>
            <ImageGrid videoImages={record.videoImages} />
          </div>
        )}

        {/* 下一步计划 */}
        {!!record.nextStepPlan && (
          <div className="mb-6 px-4">
            <h2 className="mb-2 flex items-center gap-2 font-semibold text-base">
              <CalendarRange className="h-4 w-4 text-teal-400" />
              下一步计划
            </h2>

            <div className="rounded-lg border border-teal-200 bg-teal-50 p-3">
              <div className="whitespace-pre-line text-gray-700 text-sm">
                {record.nextStepPlan}
                <button
                  className="inline-block p-1 text-gray-500 transition-colors hover:text-primary"
                  onClick={() => setIsEditSuggestDialogVisible(true)}
                  type="button"
                >
                  <Edit3 className="h-4 w-4 text-blue-500" />
                </button>
              </div>
            </div>
          </div>
        )}
        <Conversation conversation={record.conversation} />
      </div>
      <FooterActions
        currentContent={record.content}
        nextStepPlan={record.nextStepPlan}
        observationId={observationId || ''}
        onContentUpdate={(newContent) => {
          setRecord((draft) => {
            draft.content = newContent;
          });
        }}
        source={record.source}
      />
      {/* 编辑记录内容对话框 */}
      <EditTextDialog
        emptyMessage="记录内容不能为空"
        initialContent={record.content}
        onClose={() => setIsEditRecordDialogVisible(false)}
        onSave={async (content) => {
          await onRecordUpdate(content);
        }}
        onSuccess={(newContent) => {
          setRecord((draft) => {
            draft.content = newContent;
          });
        }}
        placeholder="请输入记录内容"
        title="编辑记录内容"
        visible={isEditRecordDialogVisible}
      />

      {/* 编辑下一步计划对话框 */}
      <EditTextDialog
        emptyMessage="下一步计划不能为空"
        initialContent={record.nextStepPlan}
        onClose={() => setIsEditSuggestDialogVisible(false)}
        onSave={async (content) => {
          await onSuggestUpdate(content);
        }}
        onSuccess={(newContent) => {
          setRecord((draft) => {
            draft.nextStepPlan = newContent;
          });
        }}
        placeholder="请输入下一步计划"
        title="编辑下一步计划"
        visible={isEditSuggestDialogVisible}
      />
    </div>
  );
}
