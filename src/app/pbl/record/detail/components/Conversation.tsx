'use client';

import { ChevronDown, ChevronUp, MessageSquare } from 'lucide-react';
import { useState } from 'react';

interface ConversationItem {
  speaker: string;
  text: string;
}

interface ConversationProps {
  conversation: ConversationItem[];
}

const Conversation = ({ conversation }: ConversationProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!conversation.length) {
    return null;
  }

  return (
    <div className="mb-6 px-4">
      <h3 className="mb-3 flex items-center gap-2 font-bold text-gray-700 text-md">
        <MessageSquare className="h-5 w-5 text-blue-500" />
        <span>人物对话</span>
        <span className="font-normal text-gray-500 text-xs">
          ({conversation.length})
        </span>
      </h3>
      <div className="space-y-4">
        {(isExpanded ? conversation : conversation.slice(-5)).map(
          (item: ConversationItem, index: number) => {
            const isTeacher = item.speaker === '老师';
            return (
              <div
                className={`flex ${isTeacher ? 'justify-start' : 'justify-end'}`}
                key={`${item.speaker}-${index}`}
              >
                <div className="max-w-[85%]">
                  <div
                    className={`mb-1 flex items-center gap-2 ${isTeacher ? '' : 'justify-end'}`}
                  >
                    <div
                      className={`rounded py-0.5 font-bold text-sm ${isTeacher ? ' text-blue-700' : ' text-green-700'}`}
                    >
                      {item.speaker}
                    </div>
                  </div>
                  <div
                    className={`break-words rounded-2xl p-3 ${
                      isTeacher
                        ? 'rounded-tl-none border border-blue-100 bg-blue-50'
                        : 'rounded-tr-none border border-green-100 bg-green-50'
                    }`}
                  >
                    <p className="whitespace-pre-line text-sm leading-relaxed">
                      {item.text}
                    </p>
                  </div>
                </div>
              </div>
            );
          }
        )}
        {conversation.length > 5 &&
          (isExpanded ? (
            <button
              className="flex w-full items-center justify-center gap-1 rounded-lg bg-gray-50 px-4 py-2 text-gray-500 text-sm transition-colors hover:text-gray-700"
              onClick={() => setIsExpanded(false)}
              type="button"
            >
              <ChevronUp className="h-4 w-4" />
              收起对话
            </button>
          ) : (
            <button
              className="flex w-full items-center justify-center gap-1 rounded-lg bg-gray-50 px-4 py-2 text-gray-500 text-sm transition-colors hover:text-gray-700"
              onClick={() => setIsExpanded(true)}
              type="button"
            >
              <ChevronDown className="h-4 w-4" />
              显示全部 {conversation.length} 条对话
            </button>
          ))}
      </div>
    </div>
  );
};

export default Conversation;
