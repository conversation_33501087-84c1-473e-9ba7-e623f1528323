'use client';

import { Image, ImageViewer } from 'antd-mobile';
import { useState } from 'react';

interface ImageGridProps {
  videoImages: string[];
}

/** 图片网格：每行3列，最多显示9张，超过则在第9张叠加蒙版显示"{剩余}+"，支持点击预览 */
const ImageGrid = ({ videoImages }: ImageGridProps) => {
  const [showAll, setShowAll] = useState(false);
  const [viewerVisible, setViewerVisible] = useState(false);
  const [imageIndex, setImageIndex] = useState(0);

  const maxShow = 9;
  const showList = showAll ? videoImages : videoImages.slice(0, maxShow);
  const leftover =
    videoImages.length > maxShow ? videoImages.length - maxShow : 0;

  const handleExpandClick = () => {
    setShowAll(true);
  };

  const handleImageClick = (index: number) => {
    if (!showAll && index === maxShow - 1 && leftover > 0) {
      // 如果点击的是展开按钮，则展开所有图片
      handleExpandClick();
    } else {
      // 否则打开图片预览器
      // 在非展开模式下，点击的索引就是正确的预览索引
      // 因为预览器使用的是完整的videoImages数组
      setImageIndex(index);
      setViewerVisible(true);
    }
  };

  if (!videoImages || videoImages.length === 0) {
    return null;
  }

  return (
    <>
      <div className="grid grid-cols-3 gap-2">
        {showList.map((img, idx) => {
          const isLastCover = !showAll && idx === maxShow - 1 && leftover > 0;
          return (
            <button
              className="relative cursor-pointer overflow-hidden rounded-lg border-none bg-transparent p-0"
              key={img}
              onClick={() => handleImageClick(idx)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleImageClick(idx);
                }
              }}
              type="button"
            >
              <Image
                alt=""
                className="h-24 w-full object-cover"
                fit="cover"
                src={img}
                style={{ height: '96px' }}
              />
              {isLastCover && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/50 transition-colors hover:bg-black/60">
                  <span className="font-semibold text-lg text-white">
                    {leftover}+
                  </span>
                </div>
              )}
            </button>
          );
        })}
      </div>

      {/* 图片预览器 */}
      {viewerVisible && (
        <ImageViewer.Multi
          defaultIndex={imageIndex}
          images={videoImages}
          onClose={() => setViewerVisible(false)}
          visible={true}
        />
      )}
    </>
  );
};

export default ImageGrid;
