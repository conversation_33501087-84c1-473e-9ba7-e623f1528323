'use client';

import { Dialog, Toast } from 'antd-mobile';
import { RefreshCw, Trash2 } from 'lucide-react';
// import {  Share2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { deleteObservation } from '@/api/pbl';
// import { share } from '@/utils/index';
import RegenerateModal from './RegenerateModal';

interface FooterActionsProps {
  source: string;
  observationId: string;
  currentContent: string;
  nextStepPlan: string;
  onContentUpdate: (newContent: string) => void;
}

function FooterActions({
  observationId,
  currentContent,
  source,
  nextStepPlan,
  onContentUpdate,
}: FooterActionsProps) {
  const router = useRouter();
  const [showRegenerateModal, setShowRegenerateModal] = useState(false);

  const handleRegenerate = () => {
    setShowRegenerateModal(true);
  };

  const handleDelete = () => {
    Dialog.confirm({
      content: '删除后记录将无法恢复',
      cancelText: '取消',
      confirmText: <div className="text-red-400">确认删除</div>,
      onConfirm: async () => {
        await deleteObservation(observationId);
        Toast.show({
          icon: 'success',
          content: '删除成功',
          position: 'bottom',
        });
        router.back();
      },
    });
  };

  return (
    <div className="sticky bottom-0">
      <div className="flex items-center justify-around rounded-2xl bg-card p-2 shadow-md">
        {/* <button
          className="flex flex-col items-center rounded-lg text-muted-foreground transition-colors hover:text-primary"
          onClick={() => {
            const shareData = {
              type: 0,
              title: '老师分享了新的观察记录',
              description: '',
              thumbImage: '',
              url: `${window.location.href}`,
            };
            share(shareData);
          }}
          type="button"
        >
          <Share2 className="mb-1 h-5 w-5" />
          <span className="text-xs">分享</span>
        </button> */}
        {source === '1' && (
          <button
            className="flex flex-col items-center rounded-lg text-primary transition-colors hover:text-primary-700"
            onClick={handleRegenerate}
            type="button"
          >
            <RefreshCw className="mb-1 h-5 w-5" />
            <span className="text-xs">重新生成</span>
          </button>
        )}
        <button
          className="flex flex-col items-center rounded-lg text-red-500 transition-colors hover:text-red-700"
          onClick={handleDelete}
          type="button"
        >
          <Trash2 className="mb-1 h-5 w-5" />
          <span className="text-xs">删除</span>
        </button>
      </div>

      {/* RegenerateModal */}
      <RegenerateModal
        currentContent={currentContent}
        nextStepPlan={nextStepPlan}
        observationId={observationId}
        onClose={() => setShowRegenerateModal(false)}
        onSuccess={onContentUpdate}
        visible={showRegenerateModal}
      />
    </div>
  );
}

export default FooterActions;
