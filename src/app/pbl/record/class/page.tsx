'use client';

import { useInfiniteQuery, useQuery } from '@tanstack/react-query';
import {
  ErrorBlock,
  InfiniteScroll,
  PullToRefresh,
  Segmented,
} from 'antd-mobile';
import { useAtom } from 'jotai';
import { ChevronRight, Search, Users } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useQueryState } from 'nuqs';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { FaPlus } from 'react-icons/fa';
import { getObservationList, getObservationStudentList } from '@/api/pbl';
import {
  currentSelectedRegionsAtom,
  currentTimeRangeAtom,
} from '@/store/timeZoneSelectorAtoms';
import { getMessage } from '@/utils';
import RegionSelector from '../components/RegionSelector';
import TimeSelector, { type TimeRange } from '../components/TimeSelector';
import AddRecordButton from '../list/components/AddRecordButton';
import RecordItem from '../list/components/RecordItem';

interface Child {
  studentId: number;
  name: string;
  avatar: string;
  cnt: number;
}

interface ChildItemProps {
  child: Child;
}

// --- 儿童项组件 ---
function ChildItem({ child }: ChildItemProps) {
  const router = useRouter();
  const { studentId, name, avatar } = child;

  return (
    <div className="border-stone-100 border-b p-4 transition-all duration-200 ease-in-out">
      <div
        className="flex items-center justify-between"
        onClick={() => {
          router.push(
            `/pbl/record/list?studentId=${studentId}&studentName=${name}`
          );
        }}
        onKeyDown={() => null}
        role="button"
        tabIndex={-1}
      >
        {/* 头像和姓名 */}
        <div className="flex items-center">
          <picture>
            <img
              alt={`${name} 的头像`}
              className="mr-3 size-12 cursor-pointer rounded-full border-2 border-gray-100 object-cover"
              src={avatar}
            />
          </picture>
          <span className="font-medium text-base text-text-primary">
            {name}
          </span>
        </div>
        <div>
          <span className="text-sm text-stone-400">{child.cnt} 条记录</span>
          <ChevronRight className="text-gray-400" size={20} />
        </div>
      </div>
    </div>
  );
}

const pageSize = 10;

export default function App() {
  const searchParams = useSearchParams();
  const classId = searchParams?.get('id') || '';
  const className = searchParams?.get('name') || '';
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useQueryState('activeTab', {
    defaultValue: 'student',
  });

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '班级观察记录';
    }
  }, []);

  // 防抖处理搜索
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => {
      clearTimeout(timer);
    };
  }, [searchTerm]);

  // 使用全局时间状态
  const [currentTimeRange] = useAtom(currentTimeRangeAtom);

  // 使用全局区域状态
  const [selectedRegions] = useAtom(currentSelectedRegionsAtom);

  // 处理时间选择确认
  const handleTimeConfirm = (timeRange?: TimeRange) => {
    // 立即触发数据重新获取
    if (activeTab === 'all') {
      refetchObservations();
    } else if (activeTab === 'student') {
      refetchStudents();
    }
    console.log('选择的时间段:', timeRange);
  };

  // 处理区域选择确认
  const handleRegionConfirm = () => {
    // 立即触发数据重新获取
    if (activeTab === 'all') {
      refetchObservations();
    } else if (activeTab === 'student') {
      refetchStudents();
    }
    console.log('选择的区域:', selectedRegions);
  };

  // 构建 API 请求参数
  const buildApiParams = useMemo(() => {
    const params: { observeDate?: string[]; zoneId?: string[] } = {};

    if (currentTimeRange) {
      // 检查时间格式，如果已经包含时间部分则直接使用，否则添加时间部分
      const formatTimeString = (timeStr: string, isEndTime = false) => {
        if (timeStr.includes(' ')) {
          // 已经包含时间部分，直接返回
          return timeStr;
        }
        // 只有日期部分，添加时间
        return isEndTime ? `${timeStr} 23:59:59` : `${timeStr} 00:00:00`;
      };

      params.observeDate = [
        formatTimeString(currentTimeRange.startTime),
        formatTimeString(currentTimeRange.endTime, true),
      ];
    }

    if (selectedRegions && selectedRegions.length > 0) {
      params.zoneId = selectedRegions.map((item) => item.zoneId);
    } else {
      params.zoneId = undefined;
    }

    return params;
  }, [currentTimeRange, selectedRegions]);

  // 获取班级观察记录列表（分页）
  const {
    data: infiniteObservationData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: isLoadingObservations,
    refetch: refetchObservations,
  } = useInfiniteQuery({
    queryKey: ['observationList', classId, buildApiParams],
    queryFn: async ({ pageParam }) => {
      const response = await getObservationList({
        deptId: classId,
        page: pageParam,
        perPage: pageSize,
        ...buildApiParams,
      });

      // 确保返回完整的分页信息，包括数据和分页状态
      // 以安全的方式访问返回数据
      const responseData = response as unknown;
      let records = [] as any[];
      if (Array.isArray(responseData)) {
        records = responseData;
      } else if (Array.isArray((responseData as any).list)) {
        records = (responseData as any).list;
      }

      return {
        data: records,
        page: pageParam,
        hasMore: records.length >= pageSize,
      };
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      // 如果没有更多数据，返回 undefined 表示不需要加载下一页
      if (
        lastPage.hasMore === false ||
        !lastPage.data ||
        lastPage.data.length < pageSize
      ) {
        return;
      }
      return lastPage.page + 1;
    },
    enabled: activeTab === 'all' && !!classId,

    staleTime: 60 * 1000, // 1分钟内不会自动重新获取数据
    refetchOnWindowFocus: false, // 窗口聚焦时不重新获取数据
  });

  useEffect(() => {
    getMessage(onMessage);
  }, []);

  // 获取到原生通知
  const onMessage = useCallback(
    (event: { data: string }) => {
      console.log('获取到原生通知 data: ', event);
      try {
        const data = JSON.parse(event.data);
        console.log('🚀 ~ data:', data);
        if (data.activity_on_resume) {
          refetchObservations();
        }
      } catch (error) {
        console.log('onMessage', error);
      }
    },
    [refetchObservations]
  );

  // 获取班级学生列表（不分页）
  const {
    data: studentListData = [],
    isLoading: isLoadingStudents,
    refetch: refetchStudents,
  } = useQuery({
    queryKey: ['observationStudentList', classId, buildApiParams],
    queryFn: async () => {
      const response = await getObservationStudentList({
        deptId: classId,
        ...buildApiParams,
      });
      return ((response.data || response).list || []) as Child[];
    },
    enabled: activeTab === 'student' && !!classId,
    staleTime: 60 * 1000, // 1 分钟内不会自动重新获取数据
    refetchOnWindowFocus: false, // 窗口聚焦时不重新获取数据
  });

  // 加载更多数据的处理函数
  const loadMore = async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  };

  // 处理标签页切换
  const handleTabChange = (tab: string | number) => {
    setActiveTab(tab.toString());
  };

  // 处理全部记录标签页的下拉刷新
  const handleRefreshAll = async () => {
    // 直接使用 refetch 方法刷新数据
    await refetchObservations();
  };

  // 处理按学生标签页的下拉刷新
  const handleRefreshStudent = async () => {
    // 直接使用 refetch 方法刷新数据
    await refetchStudents();
  };

  // 从 infiniteObservationData 中提取记录数据
  const recordsList = infiniteObservationData?.pages
    ? infiniteObservationData.pages.flatMap((page) => {
        if (page) {
          return page.data || [];
        }
        return [];
      })
    : [];

  // 过滤学生列表
  const filteredStudentList = studentListData
    ? studentListData
        .filter((child: Child) =>
          child.name.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
        )
        .sort((a: Child, b: Child) => {
          let aCnt: number;
          if (typeof a.cnt === 'number') {
            aCnt = a.cnt;
          } else {
            aCnt = 0;
          }

          let bCnt: number;
          if (typeof b.cnt === 'number') {
            bCnt = b.cnt;
          } else {
            bCnt = 0;
          }

          return aCnt - bCnt;
        })
    : [];

  return (
    <main className="min-h-screen bg-slate-50">
      {/* 页面头部 */}
      <div className="sticky top-0 z-10 bg-white p-4">
        <div className="mb-4 flex items-center justify-between">
          <TimeSelector
            className="cursor-pointer"
            onConfirm={handleTimeConfirm}
          />

          <RegionSelector
            className="cursor-pointer"
            onConfirm={handleRegionConfirm}
            placeholder="全部区域"
          />
        </div>

        {/* 班级信息 */}
        <div className=" flex items-center justify-between">
          <div className="flex items-center">
            <Users className="mr-2 text-brand-primary" size={18} />
            <span className="text-base">{className}</span>
          </div>
          <Segmented
            onChange={handleTabChange}
            options={[
              { label: '按学生', value: 'student' },
              { label: '全部记录', value: 'all' },
            ]}
            value={activeTab}
          />
        </div>
      </div>

      {/* 全部记录标签页 */}
      {activeTab === 'all' && (
        <div className="overflow-y-auto px-4 py-2">
          <PullToRefresh
            canReleaseText="释放立即刷新"
            completeDelay={500}
            completeText="刷新成功"
            onRefresh={handleRefreshAll}
          >
            {(() => {
              if (isLoadingObservations) {
                return <div className="py-10 text-center">加载中...</div>;
              }
              if (
                infiniteObservationData?.pages.length === 0 ||
                recordsList.length === 0
              ) {
                return (
                  <div className="py-10 text-center">
                    <ErrorBlock status="empty" title="暂无观察记录" />
                  </div>
                );
              }
              return (
                <div className="mb-12">
                  {/* 这里传递实际的记录数据给 Records 组件 */}
                  {recordsList.map((record) => (
                    <div key={record.id}>
                      <RecordItem record={record} />
                    </div>
                  ))}
                  {/* 使用 InfiniteScroll 组件 */}
                  <InfiniteScroll
                    className="!py-2"
                    hasMore={!!hasNextPage}
                    loadMore={loadMore}
                    threshold={250}
                  >
                    {(() => {
                      if (isFetchingNextPage) {
                        return (
                          <div className="py-3 text-center">
                            <span className="text-gray-500 text-sm">
                              加载更多数据...
                            </span>
                          </div>
                        );
                      }
                      if (hasNextPage) {
                        return (
                          <div className="py-3 text-center">
                            <span className="text-gray-500 text-sm">
                              上拉加载更多
                            </span>
                          </div>
                        );
                      }
                      return (
                        <div className="py-3 text-center">
                          <span className="text-gray-500 text-sm">
                            没有更多数据了
                          </span>
                        </div>
                      );
                    })()}
                  </InfiniteScroll>
                </div>
              );
            })()}
          </PullToRefresh>
        </div>
      )}

      {/* 按学生标签页 */}
      {activeTab === 'student' && (
        <div className="overflow-y-auto p-4">
          <PullToRefresh
            canReleaseText="释放立即刷新"
            completeDelay={500}
            completeText="刷新成功"
            onRefresh={handleRefreshStudent}
          >
            <div className="mb-4 flex items-center space-x-2">
              <div className="relative flex-1">
                <input
                  className="w-full rounded-lg border border-gray-200 py-2 pr-3 pl-9 text-sm focus:outline-none focus:ring-1 focus:ring-brand-primary"
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="搜索幼儿姓名..."
                  type="text"
                  value={searchTerm}
                />
                <Search
                  className="-translate-y-1/2 absolute top-1/2 left-3 text-gray-400"
                  size={16}
                />
                {searchTerm && (
                  <button
                    className="-translate-y-1/2 absolute top-1/2 right-3 text-gray-400 hover:text-gray-600"
                    onClick={() => setSearchTerm('')}
                    type="button"
                  >
                    ×
                  </button>
                )}
              </div>
            </div>

            {(() => {
              if (isLoadingStudents) {
                return <div className="py-10 text-center">加载中...</div>;
              }
              if (filteredStudentList.length > 0) {
                return (
                  <>
                    <div className="mb-2 text-gray-500 text-sm">
                      共 {studentListData?.length || 0} 名幼儿
                      {debouncedSearchTerm &&
                        `，搜索"${debouncedSearchTerm}"的结果 (${filteredStudentList.length})`}
                    </div>
                    <div className="mb-12 space-y-2 rounded-lg bg-white">
                      {filteredStudentList.map((child: Child) => (
                        <ChildItem child={child} key={child.studentId} />
                      ))}
                    </div>
                  </>
                );
              }
              return (
                <div className="py-10 text-center">
                  <ErrorBlock status="empty" title="暂无符合条件的幼儿" />
                </div>
              );
            })()}
          </PullToRefresh>
        </div>
      )}
      <AddRecordButton classId={classId}>
        {(onClick) => (
          <div className="fixed right-0 bottom-0 left-0 px-6 pb-2">
            <button
              className={`flex w-full items-center justify-center space-x-2 rounded-full bg-gradient-to-r from-violet-400 to-violet-500 px-6 py-3 text-white shadow-md transition-all hover:shadow-lg ${className}`}
              onClick={onClick}
              type="button"
            >
              <FaPlus />
              <span>新增观察记录</span>
            </button>
          </div>
        )}
      </AddRecordButton>
    </main>
  );
}
