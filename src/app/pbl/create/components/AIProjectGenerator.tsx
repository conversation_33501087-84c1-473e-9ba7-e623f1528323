'use client';

import { Input, Card as MobileCard, Space, TextArea, Toast } from 'antd-mobile';
import clsx from 'clsx';
import { Loader2, <PERSON><PERSON><PERSON>, Target } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import { aiGenerateContent } from '@/api/pbl';

interface ProjectForm {
  topic: string;
  briefIntro?: string;
  title: string;
  description: string;
  objectivesText: string;
  drivingQuestions: string[];
  tags: string[];
}

interface AIProjectGeneratorProps {
  onGenerated: (data: Partial<ProjectForm>) => void;
  onManualCreate: () => void;
}

function SectionTitle({
  icon,
  title,
  hint,
  accent = 'from-indigo-500/80 via-fuchsia-500/70 to-pink-500/70',
}: {
  icon: React.ReactNode;
  title: string;
  hint?: string;
  accent?: string;
}) {
  return (
    <div className="mb-4 flex items-center">
      <div
        className={clsx(
          'mr-3 flex aspect-square h-9 w-9 shrink-0 items-center justify-center overflow-hidden rounded-lg',
          'bg-gradient-to-br text-white shadow-[0_8px_24px_-8px_rgba(99,102,241,0.45)]',
          accent
        )}
      >
        {icon}
      </div>
      <div className="flex flex-col">
        <div className="font-semibold text-base text-gray-900 leading-none">
          {title}
        </div>
        {hint ? (
          <div className="mt-2 text-gray-500 text-xs leading-3">{hint}</div>
        ) : null}
      </div>
    </div>
  );
}

type NeoButtonProps = {
  children?: React.ReactNode;
  className?: string;
  color?: 'default' | 'primary' | 'danger';
  variant?: 'solid' | 'outline' | 'ghost';
  size?: 'mini' | 'default' | 'large';
  block?: boolean;
  loading?: boolean;
  disabled?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit';
};

function NeoButton({
  children,
  className,
  color = 'default',
  variant = 'solid',
  size = 'default',
  block = false,
  loading = false,
  disabled = false,
  onClick,
  type = 'button',
}: NeoButtonProps) {
  const base =
    'relative inline-flex items-center justify-center select-none rounded-xl font-medium transition-all duration-200 active:scale-[0.98] focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500/40 disabled:opacity-60 disabled:cursor-not-allowed';
  const sizeCls = clsx(
    size === 'mini' && 'h-7 px-2.5 text-xs',
    size === 'default' && 'h-10 px-3.5 text-sm',
    size === 'large' && 'h-12 px-4 text-base'
  );
  const blockCls = block ? 'w-full' : '';

  const palette = {
    default: {
      solid:
        'bg-white text-gray-700 border border-gray-200 shadow-sm hover:border-gray-300 hover:text-gray-900',
      outline:
        'bg-transparent text-gray-700 border border-gray-200 hover:border-gray-300 hover:bg-white/60',
      ghost:
        'bg-transparent text-gray-700 hover:bg-gray-50 border border-transparent',
    },
    primary: {
      solid:
        'text-white bg-gradient-to-br from-purple-400 to-blue-600 shadow-[0_10px_30px_-12px_rgba(99,102,241,0.35)] hover:brightness-[1.05]',
      outline:
        'text-indigo-600 border border-indigo-200/80 bg-white/80 hover:bg-white hover:border-indigo-300',
      ghost: 'text-indigo-600 hover:bg-indigo-50/70 border border-transparent',
    },
    danger: {
      solid:
        'text-white bg-gradient-to-br from-rose-500 via-orange-500 to-amber-500 shadow-[0_10px_30px_-12px_rgba(244,63,94,0.35)] hover:brightness-[1.05]',
      outline:
        'text-rose-600 border border-rose-200/80 bg-white/80 hover:bg-white hover:border-rose-300',
      ghost: 'text-rose-600 hover:bg-rose-50/70 border border-transparent',
    },
  } as const;

  const cls = clsx(
    base,
    sizeCls,
    blockCls,
    palette[color][variant],
    loading && 'pointer-events-none'
  );

  return (
    <button
      className={clsx(cls, className)}
      disabled={disabled || loading}
      onClick={onClick}
      type={type}
    >
      <span
        className={clsx(
          'inline-flex items-center gap-1.5',
          loading && 'opacity-90'
        )}
      >
        {loading && <Loader2 className="h-4 w-4 animate-spin" />}
        {children}
      </span>
    </button>
  );
}

export default function AIProjectGenerator({
  onGenerated,
  onManualCreate,
}: AIProjectGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedByAI, setGeneratedByAI] = useState(false);
  const [form, setForm] = useState({
    topic: '',
    briefIntro: '',
  });

  const topicSamples = useMemo(
    () => ['春天', '动物', '颜色', '水', '低碳城市'],
    []
  );

  const handleGenerateProject = useCallback(async () => {
    if (!form.topic.trim()) {
      Toast.show('请输入项目主题');
      return;
    }

    setIsGenerating(true);
    try {
      // 构建查询内容，包含主题和简单说明
      let query = `请为幼儿园PBL项目生成内容，主题是：${form.topic}`;
      if (form.briefIntro?.trim()) {
        query += `\n补充说明：${form.briefIntro}`;
      }
      query += `\n请生成项目标题、项目描述、学习目标（多个）、驱动性问题（多个）等内容。
        请按照如下 json 格式返回：\n
        {
          title: '春天的奇妙发现',
          description:
            '通过观察春天的变化，让孩子们探索自然界的生命力，培养观察能力和对自然的热爱。',
          objectives: 
            '培养孩子们的观察能力和记录习惯;了解春天植物和动物的变化规律;发展语言表达和艺术创作能力;增强对自然环境的保护意识',
          drivingQuestions: [
            '春天来了，我们周围发生了哪些变化？',
            '为什么有些植物会在春天开花？',
            '小动物们在春天都在做什么？',
            '我们可以用什么方式记录春天的美好？',
          ],
          tags: ['自然观察', '季节变化', '生命教育', '艺术表达'],
        }
      `;

      const response = await aiGenerateContent({
        conversation_id: '',
        files: [],
        inputs: [],
        query,
        response_mode: 'blocking',
      });

      // 解析AI返回的内容
      if (response?.answer) {
        try {
          const aiContent = JSON.parse(response.answer);

          // 验证返回的JSON格式是否符合预期
          if (aiContent && typeof aiContent === 'object') {
            // 解析学习目标，将分号分隔的字符串转换为多行文本
            let objectivesText = '';
            if (aiContent.objectives) {
              if (Array.isArray(aiContent.objectives)) {
                // 如果objectives已经是数组，则直接使用
                objectivesText = aiContent.objectives.join('\n');
              } else if (typeof aiContent.objectives === 'string') {
                // 如果objectives是分号分隔的字符串，则按分号分割
                objectivesText = aiContent.objectives.split(';').join('\n');
              }
            }

            // 解析驱动性问题，确保是字符串数组
            let drivingQuestions: string[] = [''];
            if (Array.isArray(aiContent.drivingQuestions)) {
              drivingQuestions = aiContent.drivingQuestions.slice(0, 5); // 最多5个问题
              if (drivingQuestions.length === 0) {
                drivingQuestions = [''];
              }
            }

            // 解析标签，确保是字符串数组
            let tags: string[] = [];
            if (Array.isArray(aiContent.tags)) {
              tags = aiContent.tags.slice(0, 5); // 最多5个标签
            }

            const generatedData = {
              title: aiContent.title
                ? aiContent.title.slice(0, 60)
                : `探索"${form.topic}"的奥秘`, // 限制标题长度
              description: aiContent.description
                ? aiContent.description.slice(0, 300)
                : `${response.answer.substring(0, 200)}...`, // 限制描述长度
              objectivesText,
              drivingQuestions,
              tags,
            };

            onGenerated(generatedData);
            setGeneratedByAI(true);
            Toast.show('项目内容已生成，您可以进行修改');
          } else {
            throw new Error('AI返回内容格式错误');
          }
        } catch (error) {
          console.error('解析AI返回内容失败:', error);
          Toast.show('生成失败，请重试');
        }
      } else {
        throw new Error('AI返回内容为空');
      }
    } catch (error) {
      console.error('AI生成项目失败:', error);
      Toast.show('生成失败，请重试');
    } finally {
      setIsGenerating(false);
    }
  }, [form.topic, form.briefIntro, onGenerated]);

  const handleManualCreateClick = useCallback(() => {
    setGeneratedByAI(false);
    onManualCreate();
    Toast.show('已切换到手动创建模式');
  }, [onManualCreate]);

  return (
    <MobileCard className="overflow-hidden rounded-2xl border border-white/60 bg-white/80 shadow-[0_10px_30px_-12px_rgba(99,102,241,0.20)] backdrop-blur supports-[backdrop-filter]:bg-white/70">
      <div className="p-2">
        <SectionTitle
          accent="from-violet-500/80 via-sky-500/70 to-emerald-500/70"
          hint="输入项目主题，AI 帮您生成项目基本信息"
          icon={<Sparkles className="h-5 w-5 shrink-0" />}
          title="AI 项目助手"
        />
        <Space block className="gap-3" direction="vertical">
          <div>
            <div className="mb-2 flex items-center justify-between">
              <div className="font-medium text-gray-700 text-sm">项目主题</div>
            </div>
            <div className="flex flex-col items-center gap-2">
              <Input
                clearable
                onChange={(value) =>
                  setForm((prev) => ({ ...prev, topic: value }))
                }
                placeholder="例如：春天、动物、颜色、水…"
                value={form.topic}
              />
            </div>

            <div className="mt-2 flex flex-wrap gap-2">
              {topicSamples.map((k) => (
                <button
                  className={clsx(
                    'rounded-full border border-gray-200 bg-white px-2.5 py-1 text-gray-600 text-xs',
                    'hover:border-gray-300 hover:text-gray-800',
                    'transition active:scale-[0.98]'
                  )}
                  key={k}
                  onClick={() => setForm((prev) => ({ ...prev, topic: k }))}
                  type="button"
                >
                  {k}
                </button>
              ))}
            </div>

            <div className="mt-3">
              <div className="mb-2 font-medium text-gray-700 text-sm">
                简单说明（可选）
              </div>
              <TextArea
                maxLength={300}
                onChange={(value) =>
                  setForm((prev) => ({ ...prev, briefIntro: value }))
                }
                placeholder="可补充项目背景、目标、受众等（可选）"
                rows={3}
                showCount
                value={form.briefIntro}
              />
            </div>
            <div className="mt-4 flex justify-center gap-3">
              <NeoButton
                className={clsx('min-w-[112px]')}
                color="primary"
                disabled={!form.topic.trim() || isGenerating}
                loading={isGenerating}
                onClick={handleGenerateProject}
                variant="solid"
              >
                {isGenerating ? (
                  <span className="inline-flex items-center">生成中…</span>
                ) : (
                  <span className="inline-flex items-center">
                    <Sparkles className="mr-1 h-4 w-4" />
                    生成项目
                  </span>
                )}
              </NeoButton>
              {!generatedByAI && (
                <NeoButton
                  className={clsx('min-w-[112px]')}
                  color="default"
                  disabled={isGenerating}
                  onClick={handleManualCreateClick}
                  variant="outline"
                >
                  <span className="inline-flex items-center">
                    <Target className="mr-1 h-4 w-4" />
                    手动创建
                  </span>
                </NeoButton>
              )}
            </div>
          </div>

          {generatedByAI && (
            <div className="rounded-xl border border-emerald-200 bg-emerald-50/80 px-3 py-2 text-emerald-700 text-sm">
              ✨ 已生成初稿，可在下方进行编辑与细化
            </div>
          )}
        </Space>
      </div>
    </MobileCard>
  );
}
