import { Toast } from 'antd-mobile';
import clsx from 'clsx';
import { ImagePlus, X } from 'lucide-react';
import type React from 'react';
import { useRef, useState } from 'react';
import { compress, generateKey, uploadObs } from '@/utils/obs';

// 顶层正则常量，避免在函数内重复创建
const IMAGE_TYPE_REGEX = /^image\//;

interface CoverImagePickerProps {
  coverPreview: string | null;
  onPickCover: (url: string) => void;
  onRemoveCover: () => void;
}

function CoverImagePicker({
  coverPreview,
  onPickCover,
  onRemoveCover,
}: CoverImagePickerProps) {
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const triggerPickCover = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange: React.ChangeEventHandler<HTMLInputElement> = async (
    e
  ) => {
    const file = e.target.files?.[0];
    if (!file) {
      return;
    }

    // 简单校验类型与大小（UI 级反馈）
    const isImg = IMAGE_TYPE_REGEX.test(file.type);
    if (!isImg) {
      Toast.show('请选择图片文件');
      e.target.value = '';
      return;
    }
    if (file.size > 5 * 1024 * 1024) {
      Toast.show('图片大小不超过 5MB');
      e.target.value = '';
      return;
    }

    // 开始上传流程
    setIsUploading(true);
    try {
      // 生成上传 key
      const key = generateKey(file.name, 'pbl');

      // 图片压缩
      let uploadFile = file;
      if (file.type.indexOf('image') > -1) {
        uploadFile = await compress(file);
      }

      // 上传到 OBS
      const url = await uploadObs(uploadFile, key);
      if (!url) {
        throw new Error('上传失败');
      }

      // 通知父组件上传成功
      onPickCover(url);
      Toast.show('上传成功');
    } catch (err) {
      console.error('上传失败:', err);
      Toast.show((err as Error).message || '上传失败，请重试');
    } finally {
      setIsUploading(false);
      // 清空 input 值，确保下次选择相同文件也能触发 onChange
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  return (
    <div className="space-y-3">
      {/* 预览区/占位区 */}
      <div
        className={clsx(
          'relative overflow-hidden rounded-xl border',
          coverPreview
            ? 'border-gray-200 bg-white'
            : 'border-gray-300 border-dashed bg-gradient-to-br from-gray-50 to-white'
        )}
      >
        {coverPreview ? (
          <div className="group relative">
            {/* 封面图 */}
            <img
              alt="项目封面预览"
              className="aspect-video w-full object-cover transition-transform duration-300 group-hover:scale-[1.01]"
              src={coverPreview}
            />
            {/* 遮罩动作条 */}
            <div className="pointer-events-none absolute inset-0 flex items-end justify-end bg-gradient-to-t from-black/30 via-black/0 to-transparent p-2 transition-opacity duration-200 group-hover:opacity-100">
              <div className="pointer-events-auto flex gap-2">
                <button
                  className={clsx(
                    'relative inline-flex select-none items-center justify-center rounded-xl font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500/40 active:scale-[0.98] disabled:cursor-not-allowed disabled:opacity-60',
                    'border border-gray-200 bg-white/80 text-gray-700 hover:border-gray-300 hover:bg-white',
                    'h-7 px-2.5 text-xs'
                  )}
                  disabled={isUploading}
                  onClick={triggerPickCover}
                  type="button"
                >
                  <span className="inline-flex items-center gap-1.5">
                    {isUploading ? (
                      <span className="h-3.5 w-3.5 animate-spin rounded-full border-2 border-indigo-600 border-t-transparent" />
                    ) : (
                      <ImagePlus className="mr-1 h-3.5 w-3.5" />
                    )}
                    {isUploading ? '上传中...' : '更换'}
                  </span>
                </button>
                <button
                  className={clsx(
                    'relative inline-flex select-none items-center justify-center rounded-xl font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500/40 active:scale-[0.98] disabled:cursor-not-allowed disabled:opacity-60',
                    'bg-gradient-to-br from-rose-500 via-orange-500 to-amber-500 text-white shadow-[0_10px_30px_-12px_rgba(244,63,94,0.35)] hover:brightness-[1.05]',
                    'h-7 px-2.5 text-xs'
                  )}
                  onClick={(e) => {
                    e.stopPropagation(); // 防止事件冒泡触发父级的 triggerPickCover
                    onRemoveCover();
                  }}
                  type="button"
                >
                  <span className="inline-flex items-center gap-1.5">
                    <X className="mr-1 h-3.5 w-3.5" />
                    移除
                  </span>
                </button>
              </div>
            </div>
          </div>
        ) : (
          <button
            className={clsx(
              'group flex w-full flex-col items-center justify-center gap-2 py-10 outline-none transition',
              'hover:bg-gray-50 active:scale-[0.99]',
              isUploading && 'cursor-not-allowed opacity-70'
            )}
            disabled={isUploading}
            onClick={triggerPickCover}
            type="button"
          >
            <div className="rounded-lg border border-gray-400 p-2 text-gray-500">
              {isUploading ? (
                <div className="h-6 w-6 animate-spin rounded-full border-2 border-indigo-600 border-t-transparent" />
              ) : (
                <ImagePlus className="h-6 w-6" />
              )}
            </div>
            <div className="font-medium text-gray-600 text-sm">
              {isUploading ? '上传中...' : '点击上传封面图'}
            </div>
            <div className="text-gray-500 text-xs">
              支持 jpg/png/webp，推荐 16:9
            </div>
          </button>
        )}

        {/* 隐藏的文件选择 */}
        <input
          accept="image/*"
          className="sr-only"
          disabled={isUploading}
          onChange={handleFileChange}
          ref={fileInputRef}
          type="file"
        />
      </div>

      {/* 次要操作行（当无图时展示一个按钮） */}
      {!coverPreview && (
        <div className="flex justify-end">
          <button
            className={clsx(
              'relative inline-flex select-none items-center justify-center rounded-xl font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500/40 active:scale-[0.98] disabled:cursor-not-allowed disabled:opacity-60',
              'border border-indigo-200/80 bg-white/80 text-indigo-600 hover:border-indigo-300 hover:bg-white',
              'h-7 px-2.5 text-xs',
              isUploading && 'cursor-not-allowed opacity-70'
            )}
            disabled={isUploading}
            onClick={triggerPickCover}
            type="button"
          >
            {isUploading ? '上传中...' : '选择图片'}
          </button>
        </div>
      )}
    </div>
  );
}

export default CoverImagePicker;
