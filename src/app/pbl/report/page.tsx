'use client';

import type { JSONContent } from '@tiptap/core';
import type { DebouncedFunc } from 'lodash-es';
import { debounce } from 'lodash-es';
import { useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { getPblContent, updatePblContent } from '@/api/pbl';
import { SimpleEditor } from '@/components/tiptap-templates/simple/simple-editor';
import { initData } from './data';

export default function Page() {
  const searchParams = useSearchParams();
  const projectId = searchParams?.get('projectId') || '';
  const [content, setContent] = useState<JSONContent | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      init();
    }
  }, [projectId]);

  // 类型守卫函数，检查对象是否为有效的 JSONContent
  const isValidJSONContent = (obj: unknown): obj is JSONContent => {
    if (!obj || typeof obj !== 'object') {
      return false;
    }

    const candidate = obj as Record<string, unknown>;

    // JSONContent 必须有 type 属性
    if (typeof candidate.type !== 'string') {
      return false;
    }

    // 如果有 content 属性，必须是数组
    if (candidate.content !== undefined && !Array.isArray(candidate.content)) {
      return false;
    }

    // 如果有 attrs 属性，必须是对象
    if (
      candidate.attrs !== undefined &&
      (typeof candidate.attrs !== 'object' || candidate.attrs === null)
    ) {
      return false;
    }

    // 如果有 marks 属性，必须是数组
    if (candidate.marks !== undefined && !Array.isArray(candidate.marks)) {
      return false;
    }

    return true;
  };

  const init = () => {
    setIsLoading(true);
    getPblContent(projectId)
      .then((res: any) => {
        try {
          const parsed = res?.content ? JSON.parse(res.content) : initData;

          // 只有当 parsed 是有效的 JSONContent 类型时才设置内容
          if (isValidJSONContent(parsed)) {
            setContent(parsed);
          } else {
            console.warn('解析的内容不是有效的 JSONContent 格式，使用默认数据');
            setContent(initData);
          }
        } catch (error) {
          console.error('🚀 ~ error:', error);
          setContent(initData);
        }
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const debouncedOnChange: DebouncedFunc<(nextContent: JSONContent) => void> =
    useMemo(
      () =>
        debounce((nextContent: JSONContent) => {
          console.log('🚀 ~ content:', nextContent);
          updatePblContent(projectId, {
            content: JSON.stringify(nextContent),
          }).then(() => {
            console.log('🚀 ~ 更新完成');
          });
        }, 800),
      [projectId]
    );

  useEffect(() => {
    // 组件卸载或项目切换时，取消未决的防抖调用，避免内存泄漏或误写
    return () => {
      debouncedOnChange.cancel();
    };
  }, [debouncedOnChange]);

  if (isLoading) {
    return (
      <div className="flex h-screen flex-col items-center justify-center">
        <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent" />
        <p className="mt-4">加载中...</p>
      </div>
    );
  }

  return (
    <>
      {content ? (
        <SimpleEditor
          content={content as JSONContent}
          onChange={debouncedOnChange}
          projectId={projectId}
        />
      ) : null}
    </>
  );
}
