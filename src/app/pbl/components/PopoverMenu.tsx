'use client';

import { Popover } from 'antd-mobile';
import type { ReactNode } from 'react';

export interface PopoverAction {
  text: string;
  key: string;
  icon?: ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  danger?: boolean;
}

interface PopoverMenuProps {
  actions: PopoverAction[];
  placement?:
    | 'top'
    | 'top-start'
    | 'top-end'
    | 'right'
    | 'right-start'
    | 'right-end'
    | 'bottom'
    | 'bottom-start'
    | 'bottom-end'
    | 'left'
    | 'left-start'
    | 'left-end';
  trigger?: 'click';
  children: ReactNode;
  onAction?: (action: PopoverAction) => void;
  className?: string;
}

const PopoverMenu: React.FC<PopoverMenuProps> = ({
  actions,
  placement = 'bottom-start',
  trigger = 'click',
  children,
  onAction,
  className,
}) => {
  const handleAction = (action: PopoverAction) => {
    if (action.onClick) {
      action.onClick();
    }
    if (onAction) {
      onAction(action);
    }
  };

  return (
    <Popover.Menu
      actions={actions.map((action) => ({
        ...action,
        onClick: () => handleAction(action),
      }))}
      className={className}
      placement={placement}
      trigger={trigger}
    >
      <button
        onClick={(e) => e.stopPropagation()}
        style={{
          background: 'none',
          border: 'none',
          padding: 0,
          cursor: 'pointer',
        }}
        type="button"
      >
        {children}
      </button>
    </Popover.Menu>
  );
};

export default PopoverMenu;
