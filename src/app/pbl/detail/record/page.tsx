'use client';

import { useInfiniteQuery } from '@tanstack/react-query';
import { ErrorBlock, InfiniteScroll, PullToRefresh } from 'antd-mobile';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { getObservationList } from '@/api/pbl';
import RecordItem from '../../record/list/components/RecordItem';
import type { RecordData } from '../../record/list/mock/recordData';

const pageSize = 10;

export default function ProjectRecordPage() {
  const searchParams = useSearchParams();
  const projectId = searchParams?.get('projectId') || '';
  const projectName = searchParams?.get('projectName') || '';

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '项目观察记录';
    }
  }, []);

  // 获取项目观察记录列表（分页）
  const {
    data: infiniteObservationData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: isLoadingObservations,
    refetch: refetchObservations,
  } = useInfiniteQuery({
    queryKey: ['observationList', projectId],
    queryFn: async ({ pageParam }) => {
      const response = await getObservationList({
        projectId, // 使用 projectId 而不是 deptId
        page: pageParam,
        perPage: pageSize,
      });

      // 确保返回完整的分页信息，包括数据和分页状态
      // 以安全的方式访问返回数据
      const responseData = response as unknown;
      let records: RecordData[] = [];
      if (Array.isArray(responseData)) {
        records = responseData as RecordData[];
      } else if (
        responseData &&
        typeof responseData === 'object' &&
        'list' in responseData &&
        Array.isArray((responseData as { list: RecordData[] }).list)
      ) {
        records = (responseData as { list: RecordData[] }).list;
      }

      return {
        data: records,
        page: pageParam,
        hasMore: records.length >= pageSize,
      };
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      // 如果没有更多数据，返回 undefined 表示不需要加载下一页
      if (
        lastPage.hasMore === false ||
        !lastPage.data ||
        lastPage.data.length < pageSize
      ) {
        return;
      }
      return lastPage.page + 1;
    },
    enabled: !!projectId,

    staleTime: 60 * 1000, // 1分钟内不会自动重新获取数据
    refetchOnWindowFocus: false, // 窗口聚焦时不重新获取数据
  });

  // 加载更多数据的处理函数
  const loadMore = async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  };

  // 处理下拉刷新
  const handleRefresh = async () => {
    // 直接使用 refetch 方法刷新数据
    await refetchObservations();
  };

  // 从 infiniteObservationData 中提取记录数据
  const recordsList = infiniteObservationData?.pages
    ? infiniteObservationData.pages.flatMap((page) => {
        if (page) {
          return page.data || [];
        }
        return [];
      })
    : [];

  return (
    <main className="min-h-screen bg-slate-50">
      {/* 页面头部 */}
      <div className="sticky top-0 z-10 bg-white p-4">
        {/* 页面标题 */}
        <div className="flex items-center">
          <h1 className="font-medium text-lg text-text-primary">
            {projectName}
          </h1>
        </div>
      </div>

      {/* 观察记录列表 */}
      <div className="overflow-y-auto px-4 py-2">
        <PullToRefresh
          canReleaseText="释放立即刷新"
          completeDelay={500}
          completeText="刷新成功"
          onRefresh={handleRefresh}
        >
          {(() => {
            if (isLoadingObservations) {
              return <div className="py-10 text-center">加载中...</div>;
            }
            if (
              infiniteObservationData?.pages.length === 0 ||
              recordsList.length === 0
            ) {
              return (
                <div className="py-10 text-center">
                  <ErrorBlock status="empty" title="暂无观察记录" />
                </div>
              );
            }
            return (
              <div className="mb-12">
                {/* 这里传递实际的记录数据给 RecordItem 组件 */}
                {recordsList.map((record) => (
                  <div key={record.id}>
                    <RecordItem record={record} />
                  </div>
                ))}
                {/* 使用 InfiniteScroll 组件 */}
                <InfiniteScroll
                  className="!py-2"
                  hasMore={!!hasNextPage}
                  loadMore={loadMore}
                  threshold={250}
                >
                  {(() => {
                    if (isFetchingNextPage) {
                      return (
                        <div className="py-3 text-center">
                          <span className="text-gray-500 text-sm">
                            加载更多数据...
                          </span>
                        </div>
                      );
                    }
                    if (hasNextPage) {
                      return (
                        <div className="py-3 text-center">
                          <span className="text-gray-500 text-sm">
                            上拉加载更多
                          </span>
                        </div>
                      );
                    }
                    return (
                      <div className="py-3 text-center">
                        <span className="text-gray-500 text-sm">
                          没有更多数据了
                        </span>
                      </div>
                    );
                  })()}
                </InfiniteScroll>
              </div>
            );
          })()}
        </PullToRefresh>
      </div>
    </main>
  );
}
