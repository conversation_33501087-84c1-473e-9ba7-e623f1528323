'use client';

import {
  ChevronRight,
  Clock,
  Mic,
  PlayCircle,
  Share,
  SquareChartGantt,
  Users,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import Media from '@/app/pbl/record/detail/components/Media';

interface MediaItem {
  type: 'image' | 'video' | 'audio';
  url: string;
  caption: string;
  thumbnail?: string;
  duration?: string;
}

interface RecordItemProps {
  record: {
    summaryId: string;
    summaryDate: string;
    observationIds: number[];
    suggest: string;
    summary: string;
    createTime: string;
    updateTime: string;
    projectId: string;
    observationCnt: number;
    mediaList: MediaItem[];
    students: {
      id: string;
      name: string;
      avatar: string;
    }[];
  };
  index: number;
}

// 格式化日期显示
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const weekdays = [
    '星期日',
    '星期一',
    '星期二',
    '星期三',
    '星期四',
    '星期五',
    '星期六',
  ];
  const weekday = weekdays[date.getDay()];
  return { month, day, weekday };
};

export default function RecordByDayItem({ record, index }: RecordItemProps) {
  console.log('🚀 ~ record:', record);
  const router = useRouter();
  const { month, day, weekday } = formatDate(record.summaryDate);
  const delay = index * 0.1;
  // 确保日期非空
  const date = record?.summaryDate?.split(' ')[0] || '';
  // 时间默认为当天的创建时间
  const time = record?.createTime?.split(' ')?.[1]?.substring(0, 5) || '';
  return (
    <div
      className="card-animate overflow-hidden rounded-xl bg-white shadow-sm"
      onClick={() => {
        router.push(`/pbl/record/detail/day?id=${record.summaryId}`);
      }}
      style={{ animationDelay: `${delay}s` }}
    >
      {/* 日期头部 */}
      <div className="flex items-center border-gray-200 border-b p-4">
        <div className="flex h-14 w-14 flex-col items-center justify-center rounded-lg bg-slate-400 bg-opacity-10 p-2">
          <span className="font-bold text-indigo-500 text-xl">{day}</span>
          <span className="text-indigo-500 text-xs">{month}月</span>
        </div>
        <div className="ml-3">
          <h3 className="font-medium ">{weekday}</h3>
          <p className="text-sm">{date}</p>
        </div>
        <div className="ml-auto flex items-center text-gray-600">
          <Clock className="h-3 w-3 " />
          <span className="ml-1 text-sm">{time}</span>
        </div>
      </div>

      {/* 卡片内容 */}
      <div className="p-4">
        <p className="mb-4 line-clamp-5 text-text-primary leading-relaxed">
          {record.summary}
        </p>

        <Media media={record.mediaList} />

        {/* 参与儿童 */}
        <div className="mt-3 flex items-start">
          <span className="mt-1 mr-2 flex items-center text-xs">
            <Users className="mr-1 h-3 w-3" />
            参与儿童：
          </span>
          <div className="flex flex-1 flex-wrap">
            {record.students.map((child, i) => (
              <span
                className="mr-1 mb-1 rounded-full bg-slate-100 px-2 py-1 text-xs"
                key={`${child.id}`}
              >
                {child.name || ''}
              </span>
            ))}
          </div>
        </div>
      </div>
      {/* 卡片底部操作区 */}
      <div className="flex items-center justify-between bg-neutral px-4 py-3">
        <div className="flex items-center text-sm">
          <button
            className="flex items-center"
            onClick={(e) => {
              e.stopPropagation();
              const date = record.summaryDate.split(' ')[0];
              router.push(
                `/pbl/record/list?date=${date}&weekday=${weekday}&observationIds=${record.observationIds}`
              );
            }}
            type="button"
          >
            <SquareChartGantt className="mr-1 h-4 w-4" />
            <span>关联 {record.observationCnt} 条记录</span>
            <ChevronRight className="ml-1 h-4 w-4 text-gray-500" />
          </button>
        </div>
        <div>
          <button
            className="flex items-center text-primary text-sm"
            type="button"
          >
            <Share className="mr-1 h-4 w-4" />
            <span>分享</span>
          </button>
        </div>
      </div>
    </div>
  );
}
