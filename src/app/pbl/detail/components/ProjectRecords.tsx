'use client';

import { useQuery } from '@tanstack/react-query';
import { Activity as ActivityIcon, Camera, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import { getObservationList } from '@/api/pbl';
import ActionSheetButton from '../../record/list/components/AddRecordButton';
import RecordItem from '../../record/list/components/RecordItem';
import type { RecordData } from '../../record/list/mock/recordData';

export default function ProjectRecords({
  classId,
  projectId,
  projectName,
}: {
  classId: string;
  projectId: string;
  projectName: string;
}) {
  // 获取项目观察记录列表（只获取前5条）
  const { data: recordsData, isLoading } = useQuery({
    queryKey: ['projectRecords', projectId],
    queryFn: async () => {
      const response = await getObservationList({
        projectId,
        page: 1,
        perPage: 5, // 只获取前5条
      });

      // 以安全的方式访问返回数据
      const responseData = response as unknown;
      let records: RecordData[] = [];
      if (Array.isArray(responseData)) {
        records = responseData as RecordData[];
      } else if (
        responseData &&
        typeof responseData === 'object' &&
        'list' in responseData &&
        Array.isArray((responseData as { list: RecordData[] }).list)
      ) {
        records = (responseData as { list: RecordData[] }).list;
      }

      return records;
    },
    enabled: !!projectId,
    staleTime: 60 * 1000, // 1分钟内不会自动重新获取数据
    refetchOnWindowFocus: false, // 窗口聚焦时不重新获取数据
  });

  const records = recordsData || [];

  return (
    <div className="mb-4 px-4">
      <div className="rounded-2xl bg-white shadow-sm ring-1 ring-gray-100">
        <div className="border-gray-200 border-b p-4">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-base text-gray-900">项目记录</h3>
            {records.length > 0 && (
              <Link
                className="flex items-center pl-2 font-normal text-gray-600 text-sm transition-colors hover:text-gray-900"
                href={`/pbl/detail/record?projectId=${projectId}${projectName ? `&projectName=${encodeURIComponent(projectName)}` : ''}`}
              >
                查看全部
                <ChevronRight className="ml-1 h-4 w-4" />
              </Link>
            )}
          </div>
        </div>

        {(() => {
          if (isLoading) {
            return (
              <div className="p-8 text-center">
                <div className="text-gray-500 text-sm">加载中...</div>
              </div>
            );
          }

          if (records.length > 0) {
            return (
              <div className="">
                {records.map((record) => (
                  <div className="" key={record.id}>
                    <RecordItem record={record} />
                  </div>
                ))}
              </div>
            );
          }

          return (
            <div className="p-8 text-center">
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-50 text-gray-400 ring-1 ring-gray-100">
                <ActivityIcon className="h-8 w-8" />
              </div>
              <h4 className="mb-3 font-medium text-gray-600 text-sm">
                暂无活动记录
              </h4>
              <p className="mb-2 text-gray-900 text-sm">开始记录第一个活动吧</p>
              <ActionSheetButton classId={classId} projectId={projectId}>
                {(onClick) => (
                  <button
                    className="hover:-translate-y-0.5 inline-flex items-center rounded-xl border border-gray-300 px-4 py-2 font-medium text-sm shadow-sm transition-all hover:shadow-md active:translate-y-0"
                    onClick={onClick}
                    type="button"
                  >
                    <Camera className="mr-2 h-4 w-4" />
                    记录活动
                  </button>
                )}
              </ActionSheetButton>
            </div>
          );
        })()}
      </div>
    </div>
  );
}
