'use client';

import { CalendarPlus, Camera, FileText, FolderOpen } from 'lucide-react';
import { useRouter } from 'next/navigation';
import ActionSheetButton from '../../record/list/components/AddRecordButton';

export default function QuickActions({
  classId,
  projectId,
}: {
  classId: string;
  projectId: string;
}) {
  const router = useRouter();

  return (
    <div className="mb-4 px-4">
      <div className="rounded-2xl bg-white p-4 shadow-sm ring-1 ring-gray-100">
        <h3 className="mb-4 font-semibold text-base text-gray-900">快捷操作</h3>
        <div className="flex justify-around gap-4">
          {/* 添加记录按钮 - 使用 ActionSheet */}
          <ActionSheetButton classId={classId} projectId={projectId}>
            {(onClick) => (
              <button
                className="group hover:-translate-y-0.5 flex flex-col items-center gap-2 transition-all active:translate-y-0 active:opacity-90"
                onClick={onClick}
                type="button"
              >
                <div className="flex h-12 w-12 items-center justify-center rounded-full border border-blue-300 bg-blue-50 shadow-[0_1px_0_rgba(0,0,0,0.02)] transition-all group-hover:border-blue-400 group-hover:bg-blue-100 group-hover:shadow-md">
                  <Camera className="h-5 w-5 text-blue-600 transition-transform group-active:scale-95" />
                </div>
                <span className="font-medium text-gray-700 text-xs">
                  添加记录
                </span>
              </button>
            )}
          </ActionSheetButton>
          {/* 项目报告按钮 - 直接跳转 */}
          <button
            className="group hover:-translate-y-0.5 flex flex-col items-center gap-2 transition-all active:translate-y-0 active:opacity-90"
            onClick={() => {
              router.push(`/pbl/report?projectId=${projectId}`);
            }}
            type="button"
          >
            <div className="flex h-12 w-12 items-center justify-center rounded-full border border-purple-300 bg-purple-50 shadow-[0_1px_0_rgba(0,0,0,0.02)] transition-all group-hover:border-purple-400 group-hover:bg-purple-100 group-hover:shadow-md">
              <FileText className="h-5 w-5 text-purple-600 transition-transform group-active:scale-95" />
            </div>
            <span className="font-medium text-gray-700 text-xs">项目报告</span>
          </button>

          {/* 项目活动按钮 - 直接跳转 */}
          {/* <button
            className="group hover:-translate-y-0.5 flex flex-col items-center gap-2 transition-all active:translate-y-0 active:opacity-90"
            onClick={() => {
              router.push('/pbl-v2/task');
            }}
            type="button"
          >
            <div className="flex h-12 w-12 items-center justify-center rounded-full border border-emerald-300 bg-emerald-50 shadow-[0_1px_0_rgba(0,0,0,0.02)] transition-all group-hover:border-emerald-400 group-hover:bg-emerald-100 group-hover:shadow-md">
              <CalendarPlus className="h-5 w-5 text-emerald-600 transition-transform group-active:scale-95" />
            </div>
            <span className="font-medium text-gray-700 text-xs">项目活动</span>
          </button> */}

          {/* 相关素材按钮 - 直接跳转 */}
          <button
            className="group hover:-translate-y-0.5 flex flex-col items-center gap-2 transition-all active:translate-y-0 active:opacity-90"
            onClick={() => {
              router.push(
                `/pbl/material?projectId=${projectId}&classId=${classId}`
              );
            }}
            type="button"
          >
            <div className="flex h-12 w-12 items-center justify-center rounded-full border border-orange-300 bg-orange-50 shadow-[0_1px_0_rgba(0,0,0,0.02)] transition-all group-hover:border-orange-400 group-hover:bg-orange-100 group-hover:shadow-md">
              <FolderOpen className="h-5 w-5 text-orange-600 transition-transform group-active:scale-95" />
            </div>
            <span className="font-medium text-gray-700 text-xs">相关素材</span>
          </button>
        </div>
      </div>
    </div>
  );
}
