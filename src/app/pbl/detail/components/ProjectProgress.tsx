'use client';

import { format } from 'date-fns';
import { Calendar } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { getDailyObservationList } from '@/api/pbl';

export default function ProjectProgress({ projectId }: { projectId: string }) {
  const router = useRouter();
  const [timeline, setTimeline] = useState<
    {
      summaryId: string;
      summaryDate: string;
      observationCnt: number;
      summary: string;
    }[]
  >([]);
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>(
    {}
  );

  useEffect(() => {
    if (projectId) {
      getDailyObservationList(projectId).then((res) => {
        setTimeline((res?.list || []).slice(0, 5));
      });
    }
  }, [projectId]);

  return (
    <div className="mb-4 px-4">
      <div className="rounded-2xl bg-white shadow-sm ring-1 ring-gray-100">
        <div className="border-gray-100 border-b p-4">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-base text-gray-900">每日汇总</h3>
            {timeline.length > 0 && (
              <button
                className="pl-2 font-normal text-gray-600 text-sm"
                onClick={() =>
                  router.push(`/pbl/detail/dayRecord?projectId=${projectId}`)
                }
                type="button"
              >
                查看全部
              </button>
            )}
          </div>
        </div>

        {timeline.length > 0 ? (
          <div className="flex w-full flex-col items-start p-4 ">
            {timeline.map((item, index: number) => {
              const isLast = index >= timeline.length - 1;
              return (
                <div className="group flex gap-x-3" key={item.summaryId}>
                  <div className="relative">
                    {!isLast && (
                      <div className="-translate-x-1/2 absolute top-0 left-1/2 h-full w-0.5 bg-slate-200" />
                    )}
                    <span className="relative z-10 grid h-3 w-3 place-items-center rounded-full bg-green-500 text-slate-800" />
                  </div>
                  <div className="-translate-y-1.5 pb-6 text-slate-600 data-[orientation=horizontal]:py-4">
                    <div className="flex justify-between font-bold text-base text-slate-600 antialiased dark:text-white">
                      <span>
                        {format(new Date(item.summaryDate), 'yyyy-MM-dd')}
                      </span>
                      <span className="text-gray-500 text-xs">
                        {item.observationCnt} 条记录
                      </span>
                    </div>
                    <div className="mt-2">
                      <div
                        className={`font-sans text-slate-600 text-sm antialiased ${
                          expandedItems[item.summaryId] ? '' : 'line-clamp-3'
                        }`}
                      >
                        {item.summary}
                      </div>
                      {item.summary && item.summary.length > 100 && (
                        <button
                          className="mt-1 font-sans text-indigo-500 text-xs antialiased hover:text-blue-800 focus:outline-none"
                          onClick={() => {
                            setExpandedItems((prev) => ({
                              ...prev,
                              [item.summaryId]: !prev[item.summaryId],
                            }));
                          }}
                          type="button"
                        >
                          {expandedItems[item.summaryId] ? '收起' : '更多'}
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="p-8 text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-50 text-gray-400 ring-1 ring-gray-100">
              <Calendar className="h-8 w-8" />
            </div>
            <h4 className="mb-2 font-medium text-gray-600 text-sm">
              暂无进度记录
            </h4>
          </div>
        )}
      </div>
    </div>
  );
}
