'use client';

import clsx from 'clsx';
import {
  Calendar,
  ChevronDown,
  MessageSquare,
  Target,
  Users,
} from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { getProjectDetail } from '@/api/pbl';
import { calculateProjectProgress } from '@/utils/projectProgress';
import type { Project } from '../../../data/mockData';
import DataOverview from './components/DataOverview';
import ProjectProgress from './components/ProjectProgress';
import ProjectRecords from './components/ProjectRecords';
import QuickActions from './components/QuickActions';

// 接口返回的项目数据类型
interface ApiProjectData {
  id: number;
  instId: string;
  deptId: string;
  projectId: string;
  projectName: string;
  description: string;
  theme?: string;
  drivingQuestion?: string;
  learningGoals?: string;
  keyword?: string;
  startDate: string;
  endDate: string;
  status: number;
  delFlag: number;
  createUserId: string;
  createTime: string;
  updateTime: string;
  studentCnt: number;
  observationStudentCnt: number;
  observationCnt: number;
  dept: {
    id: string;
    name: string;
  };
}

// 数据映射函数：将接口数据转换为页面所需格式
function mapApiDataToProject(apiData: ApiProjectData) {
  const { progress, statusText, isActive } = calculateProjectProgress(
    apiData.startDate,
    apiData.endDate
  );

  // 解析关键词为标签数组
  const tags = apiData.keyword
    ? apiData.keyword
        .split(',')
        .map((tag) => tag.trim())
        .filter(Boolean)
    : [];

  // 根据 ProjectCard 的处理方式映射状态
  let status: Project['status'] = 'draft';
  if (isActive) {
    status = 'active';
  } else if (statusText === '已完成') {
    status = 'completed';
  } else if (statusText === '未开始') {
    status = 'draft';
  }

  return {
    id: apiData.projectId,
    title: apiData.projectName,
    description: apiData.description || '',
    learningGoals: apiData.learningGoals || '',
    drivingQuestion: apiData.drivingQuestion || '',
    status,
    progress,
    startDate: apiData.startDate === '0000-00-00' ? '待定' : apiData.startDate,
    endDate: apiData.endDate === '0000-00-00' ? '待定' : apiData.endDate,
    deptId: apiData.deptId,
    className: apiData.dept?.name || '',
    studentCnt: apiData.studentCnt,
    observationStudentCnt: apiData.observationStudentCnt,
    tags,
    observationCnt: apiData.observationCnt,
    parentParticipationRate: 0,
  };
}

function ProjectHomePage() {
  const searchParams = useSearchParams();
  const projectId = searchParams?.get('projectId') || '';

  // 状态管理
  const [isDetailsExpanded, setIsDetailsExpanded] = useState(false);
  const [project, setProject] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = project?.title || '项目详情';
    }
  }, [project]);

  // 获取项目数据
  useEffect(() => {
    if (!projectId) {
      setError('项目ID不能为空');
      setIsLoading(false);
      return;
    }

    const fetchProjectDetail = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const response = await getProjectDetail(projectId);
        const mappedProject = mapApiDataToProject(response.data || response);
        setProject(mappedProject);
      } catch (err) {
        console.error('获取项目详情失败:', err);
        setError('获取项目详情失败，请稍后重试');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProjectDetail();
  }, [projectId]);

  // 加载状态
  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-slate-50 via-white to-slate-50">
        <div className="text-center">
          <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-4 border-indigo-200 border-t-indigo-600" />
          <p className="text-gray-600">正在加载项目详情...</p>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error || !project) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-slate-50 via-white to-slate-50">
        <div className="text-center">
          <h2 className="mb-2 font-semibold text-gray-900 text-xl">
            {error || '项目未找到'}
          </h2>
          <p className="mb-4 text-gray-600">
            {error || '请检查项目ID是否正确'}
          </p>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: Project['status']) => {
    switch (status) {
      case 'active':
        return 'bg-emerald-100 text-emerald-800 ring-1 ring-inset ring-emerald-200';
      case 'completed':
        return 'bg-blue-100 text-blue-800 ring-1 ring-inset ring-blue-200';
      case 'paused':
        return 'bg-amber-100 text-amber-800 ring-1 ring-inset ring-amber-200';
      case 'draft':
        return 'bg-gray-100 text-gray-800 ring-1 ring-inset ring-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 ring-1 ring-inset ring-gray-200';
    }
  };

  // 直接使用 calculateProjectProgress 返回的 statusText
  const getStatusText = () => {
    const { statusText } = calculateProjectProgress(
      project.startDate,
      project.endDate
    );
    return statusText;
  };

  const formatDate = (dateString: string) => {
    if (dateString === '待定') {
      return '待定';
    }
    return new Date(dateString).toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
    });
  };

  const progressWidth = `${project.progress}%`;

  return (
    <div className="min-h-screen bg-slate-50">
      {/* 顶部英雄区 */}
      <div className="relative mb-4 overflow-hidden bg-white/70 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        {/* 背景渐变与装饰 */}
        <div className="pointer-events-none absolute inset-0">
          <div className="-left-24 -top-24 absolute h-72 w-72 rounded-full bg-gradient-to-br from-blue-400/20 via-purple-400/10 to-pink-400/10 blur-3xl" />
          {/* <div className="-bottom-24 -right-24 absolute h-64 w-64 rounded-full bg-gradient-to-tr from-cyan-400/20 via-indigo-400/10 to-fuchsia-400/20 blur-3xl" /> */}
        </div>

        <div className="relative px-6 pt-4">
          <div
            className={clsx(
              'absolute top-4 right-4 inline-flex items-center rounded-full px-3 py-1 font-medium text-xs shadow-sm ring-1',
              getStatusColor(project.status)
            )}
          >
            <span>{getStatusText()}</span>
          </div>

          {/* 标题与元信息 */}
          <div className="pb-4">
            <h1 className="mb-4 font-bold text-gray-900 text-xl tracking-tight">
              {project.title}
            </h1>

            <div className="mb-2">
              <div className="space-y-3">
                <div>
                  <p className="text-slate-700 text-sm leading-relaxed">
                    {project.description}
                    {/* 展开按钮 */}
                    <button
                      className="ml-2 inline-flex items-center transition-all"
                      onClick={() => setIsDetailsExpanded(!isDetailsExpanded)}
                      type="button"
                    >
                      <span className="text-slate-500 text-xs hover:text-slate-800">
                        更多
                      </span>
                      <ChevronDown
                        className={clsx(
                          'h-4 w-4 text-slate-500 transition-transform duration-200',
                          isDetailsExpanded && 'rotate-180'
                        )}
                      />
                    </button>
                  </p>
                </div>

                {/* 展开的详细信息 */}
                {isDetailsExpanded && (
                  <div className="slide-in-from-top-2 animate-in duration-200">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <h3 className="mb-2 flex items-center gap-2 font-medium text-slate-900 text-sm">
                          <MessageSquare className="h-4 w-4 text-purple-500" />
                          驱动性问题
                        </h3>
                        <p className="text-slate-700 text-sm leading-relaxed">
                          {project.drivingQuestion}
                        </p>
                      </div>

                      <div>
                        <h3 className="mb-2 flex items-center gap-2 font-medium text-slate-900 text-sm">
                          <Target className="h-4 w-4 text-emerald-500" />
                          学习目标
                        </h3>
                        <div className="space-y-1">
                          {project.learningGoals.split('\n').map((goal) => (
                            <p
                              className="text-slate-700 text-sm leading-relaxed"
                              key={goal}
                            >
                              {goal}
                            </p>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 标签 */}
            <div className="mb-4 flex flex-wrap gap-2">
              {project.tags.map((tag: string) => (
                <span
                  className="inline-flex items-center rounded-full bg-white px-2 py-1 text-stone-700 text-xs ring-1 ring-sky-100"
                  key={tag}
                >
                  {tag}
                </span>
              ))}
            </div>

            {/* 基本信息 */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center text-gray-600">
                <Calendar className="mr-2 h-4 w-4" />
                <span>
                  {formatDate(project.startDate)} -{' '}
                  {formatDate(project.endDate)}
                </span>
              </div>
              <div className="flex items-center text-gray-600">
                <Users className="mr-2 h-4 w-4" />
                <span>
                  {project.className} · {project.studentCnt}人
                </span>
              </div>
            </div>
          </div>

          {/* 进度 */}
          <div className="pb-4">
            <div className="mb-2 flex items-center justify-between">
              <span className="font-medium text-gray-700 text-sm">
                项目进度
              </span>
              <span className="text-gray-500 text-sm">{project.progress}%</span>
            </div>
            <div className="relative h-2 w-full rounded-full bg-slate-200">
              <div
                className="h-full rounded-full bg-gradient-to-r from-indigo-400 to-indigo-500 transition-all duration-500"
                style={{ width: progressWidth }}
              />
              <div className="pointer-events-none absolute inset-0 animate-pulse rounded-full bg-gradient-to-r from-white/0 via-white/20 to-white/0" />
            </div>
          </div>
        </div>
      </div>

      <QuickActions classId={project.deptId} projectId={projectId} />

      <ProjectProgress projectId={projectId} />

      <ProjectRecords
        classId={project.deptId}
        projectId={projectId}
        projectName={project.title}
      />

      <DataOverview project={project} />
    </div>
  );
}

export default ProjectHomePage;
