'use client';

import { Dialog, DotLoading } from 'antd-mobile';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { getFreeBenefits, getMonitorReportList } from '@/api/member';
import { hinaTrack, navigationToNativePage, onBackPressed } from '@/utils';

const WeeklyReport = () => {
  const router = useRouter();
  const [isVip, setIsVip] = useState(true);
  const [freeLimit, setFreeLimit] = useState(4);
  const [timelineData, setTimelineData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setIsLoading(true);
    getFreeBenefits().then((res: any) => {
      console.log(`free: ${JSON.stringify(res)}`);
      setIsVip(res.isVip);
      setFreeLimit(res.monitorReportFreeQuota - res.monitorReportCnt);
    });
    getMonitorReportList()
      .then((res: any) => {
        console.log(`list: ${JSON.stringify(res)}`);
        setTimelineData(res.list);
      })
      .finally(() => {
        setIsLoading(false);
      });
    hinaTrack('videoreport_list_pageview');
  }, []); // 空数组表示只在组件挂载和卸载时运行一次

  const handleBackClick = () => {
    onBackPressed();
  };

  const handleDetail = (item: any) => {
    hinaTrack('videoreport_listcard_click');
    if (isVip || item.canSee)
      router.push(`/live/dailyReport?summaryId=${item.summaryId}`);
    else if (freeLimit >= 0) handleUnlockPress(item);
    else {
      Dialog.confirm({
        content: '您当前没有限免机会，开通会员可无限次查看~',
        confirmText: '立即开通',
        cancelText: <div className="text-gray-600">我再想想</div>,
        onConfirm: () => handleOpenVip(),
      });
    }
  };

  const handleUnlockPress = (item: any) => {
    Dialog.confirm({
      content: '是否确认使用1次限免机会解锁当前视频日报？',
      confirmText: '确认解锁',
      cancelText: <div className="text-gray-600">我再想想</div>,
      onConfirm: () => {
        hinaTrack('videoreport_unlock_confirm_click');
        router.push(`/live/dailyReport?summaryId=${item.summaryId}`);
      },
    });
  };

  const handleOpenVip = () => {
    hinaTrack('videoreport_openVIP_click');
    navigationToNativePage(
      'rn://MemberStack?initialRoute=ParentsIndexScreen?utm_source=1'
    );
  };

  return (
    <div
      className="relative min-h-screen w-full font-sans"
      style={{
        backgroundImage: "url('/images/live/bg.png')",
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        backgroundPosition: 'top',
      }}
    >
      <header
        className="fixed inset-x-0 top-0 z-20 w-full"
        style={{
          backgroundImage: "url('/images/live/bg.png')",
          backgroundRepeat: 'no-repeat',
          backgroundSize: '100% auto',
          backgroundPosition: 'top center',
        }}
      >
        <div className="relative flex w-full items-center justify-center">
          <div className="inset-0 mt-14 mb-2 flex items-center justify-center">
            <button
              className="absolute left-4 flex items-center justify-center text-gray-800"
              onClick={handleBackClick}
            >
              <img
                alt="返回"
                className="size-6 object-contain"
                src="/images/live/arrow_left.png"
              />
            </button>
            <h1 className="text-center font-bold text-black text-xl">
              宝贝在校视频日报
            </h1>
          </div>
        </div>

        {!isVip && (
          <div className="w-full bg-gradient-to-r from-[#FCE9AE] via-[#EAE67F] to-[#FCE48E] p-2">
            <div className="flex items-center justify-between">
              <div className="mr-2 flex flex-auto items-center">
                <svg
                  className="mr-1 size-6 shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M5 16L3 7L7 9L12 4L17 9L21 7L19 16H5Z"
                    fill="#FCD34D"
                  />
                  <rect
                    fill="#FCD34D"
                    height="3"
                    rx="1"
                    width="16"
                    x="4"
                    y="16"
                  />
                  <path d="M12 4L10 8H14L12 4Z" fill="#F59E0B" />
                  <path d="M7 9L5.5 11H8.5L7 9Z" fill="#F59E0B" />
                  <path d="M17 9L15.5 11H18.5L17 9Z" fill="#F59E0B" />
                </svg>
                <p
                  className="grow text-blue-400 text-sm"
                  style={{ minWidth: '0px' }}
                >
                  限免剩余
                  <span className="text-red-500 text-sm">{freeLimit}</span>
                  次，成为会员可无限次查看~
                </p>
              </div>
              <button
                className="shrink-0 rounded-full bg-gradient-to-r from-yellow-400 to-amber-400 px-2 py-1 text-white text-xs shadow-sm transition-transform hover:scale-105"
                onClick={() => handleOpenVip()}
              >
                <span className="whitespace-nowrap">立即开通</span>
              </button>
            </div>
          </div>
        )}
      </header>

      {/* 主内容容器，避免被 header 遮挡 */}
      <div className="relative overflow-hidden">
        {' '}
        {/* 注意：添加了 pt-120 来避开 fixed header */}
        <div className="relative z-10 flex min-h-80 flex-col">
          <div
            className={`absolute right-[55px] size-48 bg-[url('/images/live/bg1.png')] bg-contain bg-no-repeat opacity-30 ${isVip ? 'top-[120px]' : 'top-[200px]'}`}
          />
          {isLoading ? (
            <div className="flex h-screen flex-col items-center justify-center">
              <span style={{ fontSize: 16 }}>
                <span className="text-gray-500">加载中</span>
                <DotLoading />
              </span>
            </div>
          ) : timelineData.length === 0 ? (
            <div className="flex h-screen flex-col items-center justify-center">
              <p className="px-8 text-gray-500">
                日报将在每晚6点自动生成，您可以通过报告了解宝贝当天的在学校情况，请耐心等待~
              </p>
            </div>
          ) : (
            <main
              className={`px-4 pb-2 ${isVip ? 'pt-[260px]' : 'pt-[320px]'}`}
            >
              <div className="relative mt-6">
                <div
                  className="-top-4 absolute bottom-0 left-[12px] w-0.5 border-amber-300" // 移除 border-dashed，因为我们要用背景图模拟
                  style={{
                    // 使用线性渐变模拟虚线
                    // background: 'repeating-linear-gradient(to bottom, #FCD34D 0px, #FCD34D 10px, transparent 10px, transparent 20px)',
                    // border-l-2 的颜色通常是 border-amber-300
                    // 假设 #FCD34D 是 amber-300 的一个近似色
                    background:
                      'repeating-linear-gradient(to bottom, #FCD34D 0, #FCD34D 5px, transparent 5px, transparent 10px)',
                    backgroundRepeat: 'repeat-y', // 垂直重复
                    backgroundSize: '100% 10px', // 每隔 10px 重复一次模式，其中 5px 是线，5px 是间距
                  }}
                />
                {timelineData.map((monthData: any) => (
                  <div className="mb-4" key={monthData.summaryDate}>
                    <div className="space-y-6">
                      {monthData.items && monthData.items.length > 0 && (
                        <div
                          className="relative flex flex-col"
                          key={monthData.items[0].summaryId}
                        >
                          <div className="flex items-center">
                            <div className="-ml-4 relative flex size-8 shrink-0 items-center justify-center">
                              <span className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-6 size-3 rounded-full border-2 border-white bg-yellow-500" />
                            </div>
                            <div className="ml-2 flex">
                              <p className="font-semibold text-[rgb(253,145,70,1)] text-base leading-tight">
                                {monthData.summaryDate.split(' ')[0]}
                              </p>
                              <p className="ml-2 text-gray-700 text-xs leading-tight">
                                {monthData.items[0].dayOfWeek}
                              </p>
                            </div>
                          </div>
                          <div className="mt-2 ml-5">
                            <div className="overflow-hidden rounded-xl bg-white shadow-md transition-transform duration-300">
                              <img
                                alt="Kindergarten activity"
                                className="h-40 w-full object-cover"
                                src={monthData.items[0].images[0]}
                              />
                              <div className="p-4 pt-2">
                                <p className="{/*leading-relaxed mb-2 line-clamp-2*/} text-base text-gray-950">
                                  {monthData.daySummary || '暂无内容'}
                                </p>

                                <div className="flex justify-end">
                                  <button
                                    className="flex items-center space-x-1 rounded-md bg-gradient-to-r from-yellow-400 to-amber-400 px-2 py-1 text-base text-white shadow transition-all hover:scale-105 hover:shadow-md"
                                    onClick={() => handleDetail(monthData)}
                                  >
                                    <span>查看详情</span>
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </main>
          )}
        </div>
      </div>
    </div>
  );
};

export default WeeklyReport;
