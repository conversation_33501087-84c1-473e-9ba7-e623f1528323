/** biome-ignore-all lint/suspicious/noExplicitAny: <explanation> */
'use client';

import { Dialog } from 'antd-mobile';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  getAttendanceReportList,
  getAttendanceReportOverviewData,
  getVipFreeBenefits,
} from '@/api/attendance';
import { useCommonStore } from '@/store/useCommonStore';
import { handelGoBack, hinaTrack, navigationToNativePage } from '@/utils';

// 获取当前时间的问候语
const getGreeting = () => {
  const hour = new Date().getHours();
  if (hour < 12) {
    return '上午好';
  }
  if (hour < 14) {
    return '中午好';
  }
  return '下午好';
};
export default function ReportPage() {
  // 返回按钮点击事件处理
  const router = useRouter();
  const studentName = useCommonStore((state) => state.studentName);
  const [isVip, setIsVip] = useState(false);
  const [freeLimit, setFreeLimit] = useState(0);
  const time = getGreeting();
  // 上拉加载相关状态
  const [timelineReports, setTimelineReports] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [pageNum, setPageNum] = useState(1);
  // 分别控制不同数据的加载状态
  const [vipDataLoading, setVipDataLoading] = useState(true);
  const [overviewDataLoading, setOverviewDataLoading] = useState(true);
  const [reportsLoading, setReportsLoading] = useState(true);
  const [overviewData, setOverviewData] = useState<{
    familyDays: number;
    growthDays: number;
    attendedDays: number;
    restDays: number;
  }>({
    familyDays: 0,
    growthDays: 0,
    attendedDays: 0,
    restDays: 0,
  });

  const headerRef = useRef<HTMLDivElement>(null);
  const [headerHeight, setHeaderHeight] = useState(100);
  const vipRef = useRef<HTMLDivElement>(null);
  const [vipHeight, setVipHeight] = useState(40);
  useEffect(() => {
    if (headerRef.current) {
      console.log(
        'headerRef.current.offsetHeight',
        headerRef.current.offsetHeight
      );
      if (headerRef.current.offsetHeight > 36) {
        setHeaderHeight(headerRef.current.offsetHeight);
      }
    }
    if (vipRef.current) {
      console.log('vipRef.current.offsetHeight', vipRef.current.offsetHeight);
      setVipHeight(vipRef.current.offsetHeight);
    }
  }, [vipDataLoading]);

  // 背景图片的函数
  const getCardBackground = (keyWord: string) => {
    if (keyWord.includes('守时小标兵')) {
      return '/images/attendance/bg_card_1.png';
    }
    if (keyWord.includes('天气探险家')) {
      return '/images/attendance/bg_card_2.png';
    }
    if (keyWord.includes('时间小尾巴')) {
      return '/images/attendance/bg_card_3.png';
    }
    if (keyWord.includes('全勤小超人预备员')) {
      return '/images/attendance/bg_card_4.png';
    }
    if (keyWord.includes('晨光收藏家')) {
      return '/images/attendance/bg_card_5.png';
    }
    if (keyWord.includes('守时家庭')) {
      return '/images/attendance/bg_card_6.png';
    }
    if (keyWord.includes('快乐早退星人')) {
      return '/images/attendance/bg_card_7.png';
    }
    return '/images/attendance/bg_card_1.png';
  };

  const handleGoBack = () => {
    handelGoBack(router);
  };

  // 获取报告状态文本
  const getStatusText = (status: number) => {
    switch (status) {
      case 1:
        return '查看完整周报';
      case 2:
        return '查看完整周报...';
      case 3:
        return '查看完整周报';
      case 4:
        return '查看完整周报';
      default:
        return '查看完整周报';
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: number) => {
    switch (status) {
      case 1:
        return '#101010'; // 灰色
      case 2:
        return '#101010'; // 蓝色
      case 3:
        return '#101010'; // 黑色
      case 4:
        return '#101010'; // 红色
      default:
        return '#101010';
    }
  };

  // 添加跳转函数
  const handleViewWeeklyReport = (report: any) => {
    const { id, status, title } = report;
    hinaTrack('attendreport_listcard_click');
    if (
      report.extra.totalAttendDays === 0 ||
      title === '当前数据无法输出有效报告'
    ) {
      //宝贝本周没有出勤数据，暂时无法生成报告哦~
      Dialog.alert({
        content: '宝贝本周没有出勤数据，暂时无法生成报告哦~',
      });
      return;
    }
    // 已生成，正常跳转逻辑
    if (isVip || status === 3) {
      router.push(`/attendance/weeklyReport/detail?id=${id}`);
    } else if (freeLimit > 0) {
      Dialog.confirm({
        content: '是否确认使用1次限免机会解锁当前考勤周报？',
        onConfirm: () => {
          hinaTrack('attendreport_unlock_confirm_click');
          router.push(`/attendance/weeklyReport/detail?id=${id}`);
        },
        confirmText: '确认解锁',
        cancelText: '我再想想',
      });
    } else {
      // 跳转会员中心
      Dialog.confirm({
        content: '您当前没有限免机会，开通会员可无限次查看~',
        onConfirm: () => {
          hinaTrack('attendreport_openVIP_click');
          navigationToNativePage(
            'rn://MemberStack?initialRoute=ParentsIndexScreen?utm_source=2'
          );
        },
        confirmText: '去开通',
        cancelText: '我再想想',
      });
    }
  };

  // 模拟获取报告数据的函数
  const fetchReports = useCallback(async (page: number) => {
    setLoading(true);
    try {
      const res: any = await getAttendanceReportList({
        page,
        perPage: 4,
      });
      const list = res.list;
      setHasMore(list.length >= 4);
      const data = list.map((item: any) => ({
        id: item.reportId,
        title: item?.title || '--',
        keyWord: item?.keyWord || '--',
        status: item.handleStatus,
        days: getReportDays(item.startDate, item.endDate),
        ...item,
      }));
      return data;
    } catch (error) {
      console.error('获取报告数据失败:', error);
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  // 格式化时间标签函数
  const getReportDays = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);

    // 获取月份
    const month = start.getMonth() + 1;

    // 格式化开始和结束日期
    const startFormatted = `${month}.${start.getDate().toString().padStart(2, '0')}`;
    const endFormatted = `${end.getMonth() + 1}.${end.getDate().toString().padStart(2, '0')}`;

    return `/${startFormatted}-${endFormatted}`;
  };

  // 加载更多数据
  const loadMore = useCallback(async () => {
    if (loading || !hasMore) {
      return;
    }

    const newReports = await fetchReports(pageNum + 1);
    if (newReports.length > 0) {
      setTimelineReports((prev: any) => [...prev, ...newReports]);
      setPageNum((prev) => prev + 1);
    }
  }, [loading, hasMore, pageNum, fetchReports]);

  // 滚动监听
  const handleScroll = useCallback(() => {
    if (loading || !hasMore) {
      return;
    }

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const windowHeight = window.innerHeight;
    const documentHeight = document.documentElement.scrollHeight;

    // 当滚动到距离底部100px时触发加载
    if (scrollTop + windowHeight >= documentHeight - 100) {
      loadMore();
    }
  }, [loading, hasMore, loadMore]);

  useEffect(() => {
    hinaTrack('attendreport_list_pageview');
    // 分别处理各个API请求，不等待全部完成
    getVipFreeBenefits()
      .then((res: any) => {
        setIsVip(res.isVip);
        setFreeLimit(res.attendReportFreeQuota - res.attendReportCnt);
      })
      .finally(() => {
        setVipDataLoading(false);
      });

    getAttendanceReportOverviewData()
      .then((res: any) => {
        setOverviewData(res);
      })
      .finally(() => {
        setOverviewDataLoading(false);
      });

    fetchReports(1)
      .then((data) => {
        setTimelineReports(data);
      })
      .finally(() => {
        setReportsLoading(false);
      });
  }, []);

  // 添加滚动监听
  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* 导航栏 - 固定在顶部 */}
      <div
        className="fixed top-0 right-0 left-0 z-20 w-full"
        ref={headerRef}
        style={{
          backgroundImage: "url('/images/attendance/bg_report.png')",
          backgroundRepeat: 'no-repeat',
          backgroundSize: '100% auto',
          backgroundPosition: 'top center',
        }}
      >
        {/* 导航栏容器 */}
        <div className="relative mt-12 flex items-center justify-center px-4 py-2">
          {/* 返回按钮 - 绝对定位到左侧 */}
          <button
            className="absolute left-4 p-2"
            onClick={handleGoBack}
            type="button"
          >
            <svg
              className="h-6 w-6 text-gray-700"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <title>返回</title>
              <path
                d="M15 19l-7-7 7-7"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
              />
            </svg>
          </button>

          {/* 标题 - 居中显示 */}
          <span className="font-bold text-gray-700 text-xl">考勤周报</span>
        </div>
      </div>

      {/* 背景图片 */}
      <div
        className="absolute inset-0"
        style={{
          backgroundImage: "url('/images/attendance/bg_report.png')",
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
      />
      {/* 时钟背景装饰 */}
      <div
        className="absolute top-24 right-4 z-20 h-90 w-60"
        style={{
          opacity: 0.3,
          transform: 'rotate(300deg)',
          marginRight: -80,
        }}
      >
        <picture>
          <img
            alt="clock"
            className="h-full w-full"
            src="/images/attendance/clock.png"
          />
        </picture>
      </div>
      {/* 顶部提示横幅 - 根据VIP数据加载状态显示 */}
      {!vipDataLoading && (
        <div
          className="fixed right-0 left-0 z-20 flex items-center justify-between px-2 py-2"
          data-cid="TtTTvIjJUpsI2DcvNY6o3w"
          ref={vipRef}
          style={{
            top: `${headerHeight}px`, // 添加top定位，紧贴导航栏下方
            zIndex: 20, // 确保层级正确
            opacity: 1,
            background:
              'url("data:image/svg+xml;charset=utf8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20version%3D%221.1%22%3E%3Cdefs%3E%3ClinearGradient%20id%3D%221%22%20x1%3D%220%22%20x2%3D%221%22%20y1%3D%220%22%20y2%3D%220%22%20gradientTransform%3D%22matrix(1.0030000000000001%2C%200.9819999999999999%2C%20-0.00778865515477601%2C%201.0030000000000001%2C%200.003%2C%20-0.018)%22%3E%3Cstop%20stop-color%3D%22%23ffecbd%22%20stop-opacity%3D%221%22%20offset%3D%220%22%3E%3C%2Fstop%3E%3Cstop%20stop-color%3D%22%23cdf7b5%22%20stop-opacity%3D%220.25%22%20offset%3D%220.49%22%3E%3C%2Fstop%3E%3Cstop%20stop-color%3D%22%23ffecbd%22%20stop-opacity%3D%220.56%22%20offset%3D%221%22%3E%3C%2Fstop%3E%3C%2FlinearGradient%3E%3C%2Fdefs%3E%3Crect%20width%3D%22100%25%22%20height%3D%22100%25%22%20fill%3D%22url(%231)%22%3E%3C%2Frect%3E%3C%2Fsvg%3E")',
          }}
        >
          <div className="flex items-center">
            <svg
              className="mr-2 h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>皇冠主体</title>
              {/* 皇冠主体 */}
              <path d="M5 16L3 7L7 9L12 4L17 9L21 7L19 16H5Z" fill="#FCD34D" />
              {/* 皇冠底部横条 */}
              <rect fill="#FCD34D" height="3" rx="1" width="16" x="4" y="16" />
              {/* 中央尖峰 */}
              <path d="M12 4L10 8H14L12 4Z" fill="#F59E0B" />
              {/* 左侧尖峰 */}
              <path d="M7 9L5.5 11H8.5L7 9Z" fill="#F59E0B" />
              {/* 右侧尖峰 */}
              <path d="M17 9L15.5 11H18.5L17 9Z" fill="#F59E0B" />
            </svg>
            {isVip ? (
              <span
                className="white-space-nowrap text-gray-700"
                style={{ fontSize: 14 }}
              >
                您已是会员，尊享无限次查看考勤周报权益~
              </span>
            ) : (
              <div
                className="white-space-nowrap text-[rgba(66,199,255,1)]"
                style={{ fontSize: 14 }}
              >
                限免剩余
                <span className="text-[rgba(255,103,103,1)]">{freeLimit}</span>
                次，成为会员可无限次查看~
              </div>
            )}
          </div>
          {!isVip && (
            <button
              className="white-space-nowrap rounded-full bg-yellow-400 px-2 py-1 text-white"
              onClick={() => {
                hinaTrack('attendreport_openVIP_click');
                navigationToNativePage(
                  'rn://MemberStack?initialRoute=ParentsIndexScreen?utm_source=2'
                );
              }}
              style={{ fontSize: 14 }}
              type="button"
            >
              <span className="whitespace-nowrap">立即开通</span>
            </button>
          )}
        </div>
      )}

      {/* 内容区域 - 增加顶部边距以避免被固定元素遮挡 */}
      <div style={{ paddingTop: `${headerHeight + vipHeight}px` }}>
        {/* 主标题*/}
        <div className="relative z-10 mb-6 px-4">
          <h1 className="mb-2 font-bold text-2xl text-gray-800">掌心时光机</h1>
          <p className="mb-4 text-gray-600">
            {time}，{studentName}
          </p>

          {/* 宝贝本月考勤数据 */}
          <div className="mb-4 flex items-center">
            <span className="mr-2 text-gray-700">宝贝本月考勤数据</span>
          </div>

          {/* 统计卡片 - 根据数据加载状态显示 */}
          <div className="mb-6 flex space-x-0 rounded-xl bg-white/50 p-4 shadow-sm">
            {!overviewDataLoading && (
              // 实际数据
              <>
                <div className="flex-1">
                  <div className="mb-1 font-bold text-base text-gray-800">
                    已点亮 {overviewData?.growthDays} 个成长日
                  </div>
                  <div className="text-[#101010] text-sm">
                    本月已出勤{' '}
                    <span className="text-[rgba(75,161,241,1)]">
                      {overviewData?.attendedDays}
                    </span>{' '}
                    天
                  </div>
                </div>
                <div className="flex-1">
                  <div className="mb-1 font-bold text-base text-gray-800">
                    享受了 {overviewData?.familyDays} 个亲子日
                  </div>
                  <div className="text-[#101010] text-sm">
                    本月休息了{' '}
                    <span className="text-[rgba(75,161,241,1)]">
                      {overviewData?.restDays}
                    </span>{' '}
                    天
                  </div>
                </div>
              </>
            )}
          </div>
        </div>

        {/* 时间线报告列表 */}
        <div className="relative z-10 space-y-6 px-6">
          {!reportsLoading &&
            // 实际报告列表
            timelineReports.map((report: any, index: number) => (
              <div className="relative" key={report.id}>
                {/* 时间线 */}
                <div className="absolute top-1.5 left-6 z-20 h-3 w-3 rounded-full border-2 border-white bg-gray-600" />

                {index <= timelineReports.length - 1 && (
                  <div
                    className="absolute top-0 left-3.5 z-10 w-0.5 bg-gray-300"
                    style={{ height: 'calc(100% + 24px)' }}
                  />
                )}

                {/* 时间标签 */}
                <div className="mb-3 ml-10">
                  <span className="font-medium text-gray-700 text-l">
                    {report.isoWeek}
                  </span>
                  <span className="font-medium text-gray-600 text-sm">
                    {report.days}
                  </span>
                </div>

                {/* 报告卡片 */}
                {report.title !== '更多报告...' ? (
                  <button
                    className="relative ml-8 overflow-hidden rounded-2xl bg-white/30 text-left shadow-lg"
                    onClick={() => handleViewWeeklyReport(report)}
                    type="button"
                  >
                    {/* 内容层 */}
                    <div className="relative z-10 p-4">
                      {/* 背景图片层 */}
                      <div
                        className="absolute inset-0 rounded-tl-2xl rounded-tr-2xl"
                        style={{
                          backgroundImage: `url('${getCardBackground(report.title)}')`,
                          backgroundSize: 'cover',
                          backgroundPosition: 'center',
                          backgroundRepeat: 'no-repeat',
                          opacity: 0.7,
                        }}
                      />
                      {/* 主题标签 */}
                      <div className="mb-3 pt-14">
                        <p
                          className="mb-2 pr-4 text-sm leading-relaxed drop-shadow-md"
                          style={{ color: 'rgba(104,120,127,1)' }}
                        >
                          {' '}
                          {report.title?.length > 0 ? '关键词' : ''}
                        </p>
                        {/* 标题 */}
                        <h3
                          className="mb-2 font-bold text-xl drop-shadow-lg"
                          style={{ color: 'rgb(33,32,87)' }}
                        >
                          {(() => {
                            if (report.extra.totalAttendDays === 0) {
                              return '当前数据无法输出有效报告';
                            }
                            if (report.title.length > 0) {
                              return report.title;
                            }
                            return '点击生成报告';
                          })()}
                        </h3>
                        <p
                          className="mb-2 pr-4 text-sm leading-relaxed drop-shadow-md"
                          style={{ color: '#101010' }}
                        >
                          {(() => {
                            if (report.extra.totalAttendDays === 0) {
                              return '宝贝本周没有出勤数据，暂时无法生成报告哦~';
                            }
                            if (report.daySentence?.length > 0) {
                              return report.daySentence;
                            }
                            return '查看您的宝贝专属考勤报告';
                          })()}
                        </p>
                      </div>
                    </div>

                    {/* 统计信息 - 根据图片调整布局 */}
                    <div className="relative z-10 mt-4 px-4">
                      <div className="mb-2 flex items-center justify-between">
                        <div
                          className="font-bold text-sm drop-shadow-md"
                          style={{ color: 'rgb(33,32,87)' }}
                        >
                          {report.student?.name}宝贝
                        </div>
                        <div
                          className="font-bold text-sm drop-shadow-md"
                          style={{ color: 'rgb(33,22,87)' }}
                        >
                          本周出勤{' '}
                          <span className="text-[rgba(75,161,241,1)]">
                            {report.extra?.totalAttendDays}
                          </span>{' '}
                          天
                        </div>
                      </div>
                      <div
                        className="text-xs drop-shadow-sm"
                        style={{ color: 'rgb(33,32,87)' }}
                      >{`休息${7 - report.extra?.totalWorkDays}天，缺勤${report.extra?.totalAbsenceDays}天，迟到${report.extra?.totalLateCounts}次，早退${report.extra?.totalLeaveEarlyCounts}次，请假${report.extra?.totalIsLeaveDays}次`}</div>
                    </div>
                    {/* 分割线 */}
                    <div className="mt-2 mb-4 h-[0.5px] bg-[rgba(104,120,127,0.3)]" />

                    {/* VIP标识和查看报告按钮 */}
                    <div className="relative z-10 mb-2 flex items-center justify-between px-4">
                      <div className="mb-2 flex items-center">
                        <svg
                          className="styles__StyledSVGIconPathComponent-sc-i3aj97-0 fBxnFy svg-icon-path-icon"
                          height="23"
                          viewBox="0 0 32 32"
                          width="23"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <title>vip</title>
                          <defs>
                            <linearGradient
                              gradientUnits="userSpaceOnUse"
                              id="Up0NYXJCUp1payIvAP2l2y1"
                              x1="0"
                              x2="100%"
                              y1="0"
                              y2="0"
                            >
                              <stop
                                offset="0"
                                stop-color="#82d1f6"
                                stop-opacity="0.5"
                              />
                              <stop
                                offset="0.5"
                                stop-color="#29b6f4"
                                stop-opacity="1"
                              />
                              <stop
                                offset="0.99"
                                stop-color="#0089cd"
                                stop-opacity="1"
                              />
                            </linearGradient>
                          </defs>
                          <g>
                            <path
                              d="M4 4h24c0.736 0 1.333 0.597 1.333 1.333v0 21.333c0 0.736-0.597 1.333-1.333 1.333v0h-24c-0.736 0-1.333-0.597-1.333-1.333v0-21.333c0-0.736 0.597-1.333 1.333-1.333v0zM14.667 11.333v9.333h2.667v-9.333h-2.667zM14.287 11.333h-2.819l-1.996 5.484-1.996-5.484h-2.819l3.481 9.285h2.667l3.481-9.285zM21.333 18h2c1.841 0 3.333-1.492 3.333-3.333s-1.492-3.333-3.333-3.333v0h-4.667v9.333h2.667v-2.667zM21.333 15.333v-1.333h2c0.368 0 0.667 0.298 0.667 0.667s-0.298 0.667-0.667 0.667v0h-2z"
                              fill="url(#Up0NYXJCUp1payIvAP2l2y1)"
                            />
                          </g>
                        </svg>

                        <span
                          className="ml-2 text-xs drop-shadow-sm"
                          style={{ color: getStatusColor(report.status) }}
                        >
                          {getStatusText(report.status)}
                        </span>
                      </div>

                      <div
                        className="flex items-center font-medium text-sm drop-shadow-sm"
                        style={{ color: 'rgba(104,120,127,1)' }}
                      >
                        <svg
                          className="ml-1 h-4 w-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <title>查看报告</title>
                          <path
                            d="M9 5l7 7-7 7"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                          />
                        </svg>
                      </div>
                    </div>
                  </button>
                ) : (
                  <div className="ml-8 rounded-2xl bg-white/30 p-8 text-center">
                    <div className="mb-2 text-gray-400 text-lg">📋</div>
                    <p className="font-medium text-gray-500">{report.title}</p>
                  </div>
                )}
              </div>
            ))}

          {/* 加载更多指示器 */}
          {loading && (
            <div className="flex items-center justify-center py-6">
              <div className="mr-2 h-6 w-6 animate-spin rounded-full border-blue-600 border-b-2" />
              <span className="text-gray-600">加载中...</span>
            </div>
          )}

          {/* 没有更多数据提示 */}
          {!hasMore && timelineReports.length > 0 && (
            <div className="py-6 text-center">
              <p className="text-gray-600">没有更多数据了</p>
            </div>
          )}

          {/* 空数据提示 */}
          {!reportsLoading && timelineReports.length === 0 && (
            <div className="py-12 text-center">
              <div className="mb-4 text-4xl text-gray-600">📋</div>
              <p className="text-gray-600">暂无考勤报告</p>
            </div>
          )}
        </div>

        {/* 底部安全区域 */}
        <div className="h-8" />
      </div>
    </div>
  );
}
