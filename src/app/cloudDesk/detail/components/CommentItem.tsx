/** biome-ignore-all lint/complexity/noForEach: <explanation> */
/** biome-ignore-all lint/nursery/noNoninteractiveElementInteractions: <explanation> */
/** biome-ignore-all lint/nursery/noShadow: <explanation> */

import { Dialog } from 'antd-mobile';
import {
  differenceInDays,
  format,
  isSameYear,
  isToday,
  isYesterday,
} from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { MessageCircle } from 'lucide-react';
import { useRef, useState } from 'react';
export const formatDateTime = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();

  // 判断是否为今天
  if (isToday(date)) {
    return format(date, 'HH:mm', { locale: zhCN });
  }

  // 判断是否为昨天
  if (isYesterday(date)) {
    return `昨天 ${format(date, 'HH:mm', { locale: zhCN })}`;
  }

  // 判断是否为前天
  const daysDiff = differenceInDays(now, date);
  if (daysDiff === 2) {
    return `前天 ${format(date, 'HH:mm', { locale: zhCN })}`;
  }

  // 判断是否为当年
  if (isSameYear(date, now)) {
    return format(date, 'M月d日 HH:mm', { locale: zhCN });
  }

  // 跨年显示
  return format(date, 'yyyy年M月d日 HH:mm', { locale: zhCN });
};
interface Comment {
  commentId: string;
  commentContent: string;
  commentUser: {
    id: string;
    name: string;
    avatar: string;
  };
  commentInst: {
    id: string;
    code: string;
    name: string;
    fullName: string;
  };
  createTime: string;
  parentId: string;
  parentCommentDelete?: boolean; // 新增字段
  parentComment?: {
    commentId: string;
    commentContent: string;
    commentUser: {
      id: string;
      name: string;
      avatar: string;
    };
    commentInst: {
      id: string;
      code: string;
      name: string;
      fullName: string;
    };
    createTime: string;
    parentId: string;
  };
}

interface CommentItemProps {
  comment: Comment;
  currentUser: { id: string; name: string };
  onReply: (commentId: string, userName: string) => void;
  onDelete?: (commentId: string) => void;
}

export function CommentItem({
  comment,
  currentUser,
  onReply,
  onDelete,
}: CommentItemProps) {
  const canReply = true;
  const isReply = comment.parentComment && comment.parentId;
  const isOwner = comment.commentUser.id === currentUser.id;

  const [isLongPressing, setIsLongPressing] = useState(false);
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);
  const longPressStartTime = useRef<number>(0);

  const handleTouchStart = () => {
    if (!isOwner) {
      return;
    }

    longPressStartTime.current = Date.now();
    setIsLongPressing(true);

    longPressTimer.current = setTimeout(() => {
      Dialog.confirm({
        title: '确认删除吗？',
        content: '删除后将无法恢复',
        onConfirm: handleDeleteConfirm,
        confirmText: '删除',
        cancelText: '取消',
      });
      setIsLongPressing(false);
    }, 500); // 500ms长按触发
  };

  const handleTouchEnd = () => {
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }
    setIsLongPressing(false);
  };

  const handleMouseDown = () => {
    if (!isOwner) {
      return;
    }
    handleTouchStart();
  };

  const handleMouseUp = () => {
    handleTouchEnd();
  };

  const handleMouseLeave = () => {
    handleTouchEnd();
  };

  const handleDeleteConfirm = () => {
    if (onDelete) {
      onDelete(comment.commentId);
    }
  };

  return (
    <div className="mb-4">
      <div
        className={`flex items-start gap-3 transition-all duration-200 ${
          isLongPressing ? 'scale-95 rounded-lg bg-red-50 p-2' : ''
        }`}
        onMouseDown={handleMouseDown}
        onMouseLeave={handleMouseLeave}
        onMouseUp={handleMouseUp}
        onTouchEnd={handleTouchEnd}
        onTouchStart={handleTouchStart}
      >
        <picture>
          <img
            alt={comment.commentUser.name}
            className="h-11 w-11 flex-shrink-0 rounded-full object-cover"
            src={comment.commentUser.avatar}
          />
        </picture>
        <div className="min-w-0 flex-1">
          {/* 用户信息和时间 */}
          <div className="flex flex-wrap items-center gap-1">
            <span className="font-medium text-gray-800">
              {comment.commentUser.name}
            </span>
            <span className="text-gray-500 text-sm">
              {formatDateTime(comment.createTime)}
            </span>
          </div>

          {/* 幼儿园信息 */}
          {comment.commentInst && (
            <div className="mb-1">
              <span className="text-gray-500 text-sm">
                {comment.commentInst.fullName || comment.commentInst.name}
              </span>
            </div>
          )}

          {/* 评论内容 */}
          <p className="mb-3 break-words text-gray-600">
            {isReply ? (
              <>
                <span className="font-medium text-blue-600">
                  回复
                  {comment.parentComment?.commentUser.name}：
                </span>
                {comment.commentContent}
              </>
            ) : (
              comment.commentContent
            )}
          </p>

          {/* 如果是回复，显示被回复的评论信息 */}
          {isReply && (
            <div className="mb-2 rounded-lg bg-gray-50 p-3">
              {comment.parentComment && !comment.parentCommentDelete ? (
                // 父评论存在且未被删除时显示完整信息
                <>
                  <div className="mb-1 flex items-center gap-2">
                    <picture>
                      <img
                        alt={comment.parentComment.commentUser.name}
                        className="h-6 w-6 rounded-full object-cover"
                        src={comment.parentComment.commentUser.avatar}
                      />
                    </picture>
                    <span className="font-medium text-gray-700 text-sm">
                      {comment.parentComment.commentUser.name}
                    </span>
                    <span className="text-gray-500 text-sm">
                      {comment.parentComment.commentInst.fullName ||
                        comment.parentComment.commentInst.name}
                    </span>
                  </div>
                  <p className="line-clamp-2 text-gray-600 text-sm">
                    {comment.parentComment.commentContent}
                  </p>
                </>
              ) : (
                // 父评论被删除时显示提示信息
                <div className="flex items-center gap-2">
                  <span className="text-gray-500 text-sm italic">
                    评论已删除
                  </span>
                </div>
              )}
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex items-center gap-4 text-sm">
            {canReply && currentUser && (
              <button
                className="flex touch-manipulation items-center gap-1 rounded-full px-2 py-1 text-gray-500 transition-colors hover:bg-blue-50 hover:text-blue-600"
                onClick={() =>
                  onReply(comment.commentId, comment.commentUser.name)
                }
                type="button"
              >
                <MessageCircle className="h-4 w-4" />
                <span>回复</span>
              </button>
            )}

            {isOwner && (
              <div className="text-gray-400 text-xs">长按删除评论</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
