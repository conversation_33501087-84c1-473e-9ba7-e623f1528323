import { ChevronsLeft, ChevronsRight, Pause, Play, X } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

interface AudioPlayerProps {
  src: string;
  title: string;
  onClose: () => void;
}

export function AudioPlayer({ src, title, onClose }: AudioPlayerProps) {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) {
      return;
    }

    const updateTime = () => setCurrentTime(audio.currentTime);
    const updateDuration = () => setDuration(audio.duration);
    const handleEnded = () => setIsPlaying(false);

    audio.addEventListener('timeupdate', updateTime);
    audio.addEventListener('loadedmetadata', updateDuration);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('timeupdate', updateTime);
      audio.removeEventListener('loadedmetadata', updateDuration);
      audio.removeEventListener('ended', handleEnded);
    };
  }, []);

  const togglePlay = () => {
    const audio = audioRef.current;
    if (!audio) {
      return;
    }

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const audio = audioRef.current;
    if (!audio) {
      return;
    }

    const newTime = Number.parseFloat(e.target.value);
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };
  // 添加getAudioType函数
  const getAudioType = (url: string): string => {
    if (!url) {
      return '';
    }

    const extension = url.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'wav':
        return 'audio/wav';
      case 'mp3':
        return 'audio/mpeg';
      case 'ogg':
      case 'oga':
        return 'audio/ogg';
      case 'aac':
        return 'audio/aac';
      case 'm4a':
        return 'audio/mp4';
      case 'flac':
        return 'audio/flac';
      case 'webm':
        return 'audio/webm';
      default:
        return 'audio/mpeg'; // 默认返回mp3格式
    }
  };
  const skip = (seconds: number) => {
    const audio = audioRef.current;
    if (!audio) {
      return;
    }

    audio.currentTime = Math.max(
      0,
      Math.min(audio.currentTime + seconds, duration)
    );
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80">
      <div className="mx-4 w-full max-w-md rounded-xl bg-white p-6">
        {/* 头部 */}
        <div className="mb-6 flex items-center justify-between">
          <h3 className="truncate font-semibold text-gray-800 text-lg">
            {title}
          </h3>
          <button
            className="rounded p-1 transition-colors hover:bg-gray-100"
            onClick={onClose}
            type="button"
          >
            <X className="h-5 w-5 text-gray-600" />
          </button>
        </div>

        {/* 音频元素 */}
        <audio ref={audioRef} src={src}>
          <source src={src} type={getAudioType(src)} />
          <track kind="captions" src={src} />
        </audio>

        {/* 控制按钮 */}
        <div className="mb-4 flex items-center justify-center gap-4">
          <button
            className="rounded-full p-2 text-gray-600 transition-colors hover:bg-gray-100"
            onClick={() => skip(-10)}
            type="button"
          >
            <ChevronsLeft className="h-5 w-5" />
          </button>
          <button
            className="rounded-full bg-orange-500 p-3 text-white transition-colors hover:bg-orange-600"
            onClick={togglePlay}
            type="button"
          >
            {isPlaying ? (
              <Pause className="h-6 w-6" />
            ) : (
              <Play className="ml-1 h-6 w-6" fill="currentColor" />
            )}
          </button>
          <button
            className="rounded-full p-2 text-gray-600 transition-colors hover:bg-gray-100"
            onClick={() => skip(10)}
            type="button"
          >
            <ChevronsRight className="h-5 w-5" />
          </button>
        </div>
        {/* 进度条 */}
        <div className="mb-4">
          <input
            className="h-2 w-full cursor-pointer appearance-none rounded-lg bg-gray-200"
            max={duration || 0}
            min={0}
            onChange={handleSeek}
            type="range"
            value={currentTime}
          />
          <div className="mt-1 flex justify-between text-gray-500 text-sm">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
