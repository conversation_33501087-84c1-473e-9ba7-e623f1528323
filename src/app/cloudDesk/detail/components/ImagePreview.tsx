/** biome-ignore-all lint/nursery/noNoninteractiveElementInteractions: <explanation> */
import { RotateCw, X, ZoomIn, ZoomOut } from 'lucide-react';
import { useState } from 'react';

interface ImagePreviewProps {
  src: string;
  alt: string;
  onClose: () => void;
}

export function ImagePreview({ src, alt, onClose }: ImagePreviewProps) {
  const [scale, setScale] = useState(1);
  const [rotation, setRotation] = useState(0);

  const handleZoomIn = () => {
    setScale((prev) => Math.min(prev + 0.25, 3));
  };

  const handleZoomOut = () => {
    setScale((prev) => Math.max(prev - 0.25, 0.25));
  };

  const handleRotate = () => {
    setRotation((prev) => (prev + 90) % 360);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/90">
      {/* 工具栏 */}
      <div className="absolute top-4 right-4 left-4 z-10 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <button
            className="rounded-lg bg-white/20 p-2 text-white transition-colors hover:bg-white/30"
            onClick={handleZoomOut}
            type="button"
          >
            <ZoomOut className="h-5 w-5" />
          </button>
          <span className="min-w-[60px] text-center text-sm text-white">
            {Math.round(scale * 100)}%
          </span>
          <button
            className="rounded-lg bg-white/20 p-2 text-white transition-colors hover:bg-white/30"
            onClick={handleZoomIn}
            type="button"
          >
            <ZoomIn className="h-5 w-5" />
          </button>
          <button
            className="rounded-lg bg-white/20 p-2 text-white transition-colors hover:bg-white/30"
            onClick={handleRotate}
            type="button"
          >
            <RotateCw className="h-5 w-5" />
          </button>
        </div>
        <button
          className="rounded-lg bg-white/20 p-2 text-white transition-colors hover:bg-white/30"
          onClick={onClose}
          type="button"
        >
          <X className="h-5 w-5" />
        </button>
      </div>

      {/* 图片容器 */}
      <div className="flex h-full w-full items-center justify-center p-4">
        <picture>
          <img
            alt={alt}
            className="max-h-full max-w-full object-contain transition-transform duration-200"
            src={src}
            style={{
              transform: `scale(${scale}) rotate(${rotation}deg)`,
            }}
          />
        </picture>
      </div>

      {/* 点击背景关闭 */}
      <div className="-z-10 absolute inset-0" onClick={onClose} />
    </div>
  );
}
