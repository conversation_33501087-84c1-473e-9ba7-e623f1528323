/** biome-ignore-all lint/a11y/useMediaCaption: <explanation> */
import {
  Maximize,
  Pause,
  Play,
  SkipBack,
  SkipForward,
  Volume2,
  VolumeX,
  X,
} from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

interface VideoPlayerProps {
  src: string;
  title: string;
  onClose: () => void;
}

export function VideoPlayer({ src, title, onClose }: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [showControls, setShowControls] = useState(true);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) {
      return;
    }

    const updateTime = () => setCurrentTime(video.currentTime);
    const updateDuration = () => setDuration(video.duration);

    video.addEventListener('timeupdate', updateTime);
    video.addEventListener('loadedmetadata', updateDuration);

    return () => {
      video.removeEventListener('timeupdate', updateTime);
      video.removeEventListener('loadedmetadata', updateDuration);
    };
  }, []);

  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) {
      return;
    }

    if (isPlaying) {
      video.pause();
    } else {
      video.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const video = videoRef.current;
    if (!video) {
      return;
    }

    const newTime = Number.parseFloat(e.target.value);
    video.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const video = videoRef.current;
    if (!video) {
      return;
    }

    const newVolume = Number.parseFloat(e.target.value);
    video.volume = newVolume;
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
  };

  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) {
      return;
    }

    if (isMuted) {
      video.volume = volume;
      setIsMuted(false);
    } else {
      video.volume = 0;
      setIsMuted(true);
    }
  };

  const skip = (seconds: number) => {
    const video = videoRef.current;
    if (!video) {
      return;
    }
    video.currentTime = Math.max(
      0,
      Math.min(video.currentTime + seconds, duration)
    );
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleFullscreen = () => {
    const video = videoRef.current;
    if (!video) {
      return;
    }

    if (video.requestFullscreen) {
      video.requestFullscreen();
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black">
      {/* 关闭按钮 */}
      <button
        className="absolute top-4 right-4 z-10 rounded-lg bg-black/50 p-2 text-white transition-colors hover:bg-black/70"
        onClick={onClose}
        type="button"
      >
        <X className="h-5 w-5" />
      </button>

      {/* 视频容器 */}
      <div className="relative flex h-full w-full items-center justify-center">
        <video
          className="max-h-full max-w-full"
          onClick={togglePlay}
          ref={videoRef}
          src={src}
        />

        {/* 控制栏 */}
        {showControls && (
          <div className="absolute right-4 bottom-4 left-4 rounded-lg bg-black/70 p-4">
            {/* 进度条 */}
            <div className="mb-4">
              <input
                className="h-1 w-full cursor-pointer appearance-none rounded-lg bg-gray-600"
                max={duration || 0}
                min={0}
                onChange={handleSeek}
                type="range"
                value={currentTime}
              />
              <div className="mt-1 flex justify-between text-sm text-white">
                <span>{formatTime(currentTime)}</span>
                <span>{formatTime(duration)}</span>
              </div>
            </div>

            {/* 控制按钮 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <button
                  className="rounded p-2 text-white transition-colors hover:bg-white/20"
                  onClick={() => skip(-10)}
                  type="button"
                >
                  <SkipBack className="h-5 w-5" />
                </button>
                <button
                  className="rounded p-2 text-white transition-colors hover:bg-white/20"
                  onClick={togglePlay}
                  type="button"
                >
                  {isPlaying ? (
                    <Pause className="h-6 w-6" />
                  ) : (
                    <Play className="h-6 w-6" />
                  )}
                </button>
                <button
                  className="rounded p-2 text-white transition-colors hover:bg-white/20"
                  onClick={() => skip(10)}
                  type="button"
                >
                  <SkipForward className="h-5 w-5" />
                </button>
              </div>

              <div className="flex items-center gap-2">
                <button
                  className="rounded p-2 text-white transition-colors hover:bg-white/20"
                  onClick={toggleMute}
                  type="button"
                >
                  {isMuted ? (
                    <VolumeX className="h-5 w-5" />
                  ) : (
                    <Volume2 className="h-5 w-5" />
                  )}
                </button>
                <input
                  className="h-1 w-20 cursor-pointer appearance-none rounded-lg bg-gray-600"
                  max={1}
                  min={0}
                  onChange={handleVolumeChange}
                  step={0.1}
                  type="range"
                  value={isMuted ? 0 : volume}
                />
                <button
                  className="rounded p-2 text-white transition-colors hover:bg-white/20"
                  onClick={handleFullscreen}
                  type="button"
                >
                  <Maximize className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
