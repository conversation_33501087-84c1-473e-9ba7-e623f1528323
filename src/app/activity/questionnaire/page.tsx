'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  Button,
  Card,
  Checkbox,
  Radio,
  Space,
  TextArea,
  Toast,
} from 'antd-mobile';
import { CheckCircle2, Send } from 'lucide-react';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';

import { hinaTrack, postMessage } from '@/utils';

const formSchema = z
  .object({
    cancelReason: z.enum(
      [
        '价格过高',
        '权益不符合需求',
        '使用频率低',
        '转向了其他产品',
        'APP体验问题（加载慢/卡顿等）',
        'other',
      ],
      {
        required_error: '请选择取消订阅的主要原因',
      }
    ),
    otherReasonText: z.string().optional(),
    valuePerception: z.enum(
      ['非常不值得', '不太值得', '一般', '比较值得', '非常值得'],
      {
        required_error: '请选择您对会员权益价值的看法',
      }
    ),
    priceConsideration: z
      .enum([
        '维持当前价格但增加新的权益',
        '降价10%-20%',
        '提供更灵活的付费周期（如按周付）',
        '暂时不考虑续费',
      ])
      .optional(),
    desiredBenefits: z.array(z.string()).optional(),
    otherBenefitText: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.cancelReason === '价格过高' && !data.priceConsideration) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '请选择您可能考虑续费的价格范围',
        path: ['priceConsideration'],
      });
    }
    if (
      data.cancelReason === '权益不符合需求' &&
      (!data.desiredBenefits || data.desiredBenefits.length === 0)
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '请选择您希望新增的权益',
        path: ['desiredBenefits'],
      });
    }
  });

const benefits = {
  ai: 'AI相关功能（例如AI育儿助手、AI生成写真集等）',
  exclusiveContent: ' 独家内容/课程',
  multiDevice: '多设备同时登陆',
  videoArchive: '宝贝在线视频存档',
  exclusiveSkins: '会员专享皮肤/图标',
  morePoints: '更多积分兑换商品',
  other: '其他权益',
};

export default function SurveyForm() {
  const [isSubmitted, setIsSubmitted] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      desiredBenefits: [],
    },
  });

  const watchCancelReason = form.watch('cancelReason');
  const watchDesiredBenefits = form.watch('desiredBenefits');
  const showPriceQuestion = watchCancelReason === '价格过高';
  const showOtherReasonInput = watchCancelReason === 'other';
  const showOtherBenefitInput = watchDesiredBenefits?.includes('other');

  function onSubmit(values: z.infer<typeof formSchema>) {
    console.log(values);
    if (values.cancelReason === 'other' && !values.otherReasonText?.trim()) {
      Toast.show('请填写您取消订阅的主要原因');
      return;
    }
    if (
      values.desiredBenefits?.includes('other') &&
      !values.otherBenefitText?.trim()
    ) {
      Toast.show('请填写您希望的具体权益');
      return;
    }
    const postData = {
      ...values,
      cancelReason:
        values.cancelReason === 'other'
          ? `其他：${values.otherReasonText}`
          : values.cancelReason,
      // 如果包含其他权益，将具体权益附加到desiredBenefits中
      desiredBenefits: values.desiredBenefits?.map((benefit) =>
        benefit === 'other'
          ? `其他：${values.otherBenefitText}`
          : benefits[benefit as keyof typeof benefits]
      ),
    };
    hinaTrack('cancelSurvey', {
      cancelReason: postData.cancelReason,
      valuePerception: postData.valuePerception,
      priceConsideration: postData.priceConsideration,
      desiredBenefits: postData.desiredBenefits?.toString(),
    });
    postMessage({
      type: 'cancelSurvey',
    });
    setIsSubmitted(true);
  }

  const goBackApp = () => {
    postMessage({
      type: 'goBackApp',
    });
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="mx-auto max-w-md pt-20">
          <Card className="text-center">
            <div className="p-4">
              <div className="mx-auto mb-6 flex size-20 items-center justify-center rounded-full bg-green-100">
                <CheckCircle2 className="size-12 text-green-600" />
              </div>
              <h1 className="mb-4 font-bold text-base text-green-700 leading-tight">
                自动续费已经取消，感谢您的反馈！
              </h1>
              <p className="mb-8 text-gray-600 text-sm leading-relaxed">
                您的意见对我们非常重要，我们将努力改进产品体验
              </p>
              <Button
                block
                className="rounded-lg font-medium"
                color="success"
                onClick={goBackApp}
                size="large"
              >
                返回APP
              </Button>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="mx-auto max-w-2xl">
        <Card className="mb-4">
          <div className="p-4">
            <p className="text-gray-700 text-lg leading-relaxed">
              很遗憾您选择取消订阅，您的反馈能帮助我们改进数百万用户的体验，感谢您的耐心填写
            </p>
          </div>
        </Card>

        <Card>
          <div className="p-2">
            <form className="space-y-8" onSubmit={form.handleSubmit(onSubmit)}>
              {/* 问题1：取消原因 */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 font-medium text-gray-800 text-lg">
                  <span className="flex size-6 items-center justify-center rounded-full bg-blue-100 font-bold text-blue-600 text-sm">
                    1
                  </span>
                  您取消订阅的主要原因是什么？
                </div>
                <Controller
                  control={form.control}
                  name="cancelReason"
                  render={({ field }) => (
                    <Radio.Group onChange={field.onChange} value={field.value}>
                      <Space className="w-full" direction="vertical">
                        <Radio value="价格过高">价格过高</Radio>
                        <Radio value="权益不符合需求">权益不符合需求</Radio>
                        <Radio value="使用频率低">使用频率低</Radio>
                        <Radio value="转向了其他产品">转向了其他产品</Radio>
                        <Radio value="APP体验问题（加载慢/卡顿等）">
                          APP体验问题（加载慢/卡顿等）
                        </Radio>
                        <Radio value="other">其他，请说明</Radio>
                      </Space>
                    </Radio.Group>
                  )}
                />
                {form.formState.errors.cancelReason && (
                  <p className="text-red-500 text-sm">
                    {form.formState.errors.cancelReason.message}
                  </p>
                )}
              </div>

              {/* 其他原因输入框 */}
              {showOtherReasonInput && (
                <div className="space-y-2">
                  <Controller
                    control={form.control}
                    name="otherReasonText"
                    render={({ field }) => (
                      <TextArea
                        {...field}
                        className="w-full"
                        placeholder="请说明您取消订阅的其他原因..."
                        rows={3}
                      />
                    )}
                  />
                </div>
              )}

              {/* 分割线 */}
              <div className="border-gray-200 border-t" />

              {/* 问题2：价值感知 */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 font-medium text-gray-800 text-lg">
                  <span className="flex size-6 items-center justify-center rounded-full bg-blue-100 font-bold text-blue-600 text-sm">
                    2
                  </span>
                  您认为当前会员权益给予的价值如何？
                </div>
                <Controller
                  control={form.control}
                  name="valuePerception"
                  render={({ field }) => (
                    <Radio.Group onChange={field.onChange} value={field.value}>
                      <Space className="w-full" direction="vertical">
                        <Radio value="非常不值得">非常不值得</Radio>
                        <Radio value="不太值得">不太值得</Radio>
                        <Radio value="一般">一般</Radio>
                        <Radio value="比较值得">比较值得</Radio>
                        <Radio value="非常值得">非常值得</Radio>
                      </Space>
                    </Radio.Group>
                  )}
                />
                {form.formState.errors.valuePerception && (
                  <p className="text-red-500 text-sm">
                    {form.formState.errors.valuePerception.message}
                  </p>
                )}
              </div>

              {/* 分割线 */}
              <div className="border-gray-200 border-t" />

              {/* 问题3：价格考虑（条件显示） */}
              {showPriceQuestion && (
                <>
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 font-medium text-gray-800 text-lg">
                      <span className="flex size-6 items-center justify-center rounded-full bg-blue-100 font-bold text-blue-600 text-sm">
                        3
                      </span>
                      如果调整价格您可能考虑续费的范围是？
                    </div>
                    <Controller
                      control={form.control}
                      name="priceConsideration"
                      render={({ field }) => (
                        <Radio.Group
                          onChange={field.onChange}
                          value={field.value}
                        >
                          <Space className="w-full" direction="vertical">
                            <Radio value="维持当前价格但增加新的权益">
                              维持当前价格但增加新的权益
                            </Radio>
                            <Radio value="降价10%-20%">降价10%-20%</Radio>
                            <Radio value="提供更灵活的付费周期（如按周付）">
                              提供更灵活的付费周期（如按周付）
                            </Radio>
                            <Radio value="暂时不考虑续费">暂时不考虑续费</Radio>
                          </Space>
                        </Radio.Group>
                      )}
                    />
                    {form.formState.errors.priceConsideration && (
                      <p className="text-red-500 text-sm">
                        {form.formState.errors.priceConsideration.message}
                      </p>
                    )}
                  </div>
                  <div className="border-gray-200 border-t" />
                </>
              )}

              {/* 问题3：权益需求（条件显示） */}
              {watchCancelReason === '权益不符合需求' && (
                <>
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 font-medium text-gray-800 text-lg">
                      <span className="flex size-6 items-center justify-center rounded-full bg-blue-100 font-bold text-blue-600 text-sm">
                        3
                      </span>
                      您最希望我们新增哪类权益？
                    </div>
                    <p className="text-gray-500 text-sm">最多可选3项</p>
                    <Controller
                      control={form.control}
                      name="desiredBenefits"
                      render={({ field }) => (
                        <Checkbox.Group
                          onChange={(values) => {
                            if (values.length > 3) {
                              Toast.show('最多只能选择3项');
                              return;
                            }
                            field.onChange(values);
                          }}
                          value={field.value}
                        >
                          <Space className="w-full" direction="vertical">
                            <Checkbox value="ai">
                              <div>
                                <div>AI相关功能</div>
                                <div className="text-gray-500 text-sm">
                                  (例如AI育儿助手、AI生成写真集等)
                                </div>
                              </div>
                            </Checkbox>
                            <Checkbox value="exclusiveContent">
                              独家内容/课程
                            </Checkbox>
                            <Checkbox value="multiDevice">
                              多设备同时登陆
                            </Checkbox>
                            <Checkbox value="videoArchive">
                              宝贝在线视频存档
                            </Checkbox>
                            <Checkbox value="exclusiveSkins">
                              会员专享皮肤/图标
                            </Checkbox>
                            <Checkbox value="morePoints">
                              更多积分兑换商品
                            </Checkbox>
                            <Checkbox value="other">其他权益，请说明</Checkbox>
                          </Space>
                        </Checkbox.Group>
                      )}
                    />
                    {form.formState.errors.desiredBenefits && (
                      <p className="text-red-500 text-sm">
                        {form.formState.errors.desiredBenefits.message}
                      </p>
                    )}
                  </div>

                  {/* 其他权益输入框 */}
                  {showOtherBenefitInput && (
                    <div className="space-y-2">
                      <Controller
                        control={form.control}
                        name="otherBenefitText"
                        render={({ field }) => (
                          <TextArea
                            {...field}
                            className="w-full"
                            placeholder="请说明您希望的其他权益..."
                            rows={3}
                          />
                        )}
                      />
                    </div>
                  )}
                </>
              )}

              {/* 提交按钮 */}
              <div className="pt-6">
                <Button
                  block
                  className="rounded-lg font-medium"
                  color="primary"
                  size="large"
                  type="submit"
                >
                  <Send className="mr-2 size-4" />
                  提交问卷
                </Button>
              </div>
            </form>
          </div>
        </Card>
      </div>
    </div>
  );
}
