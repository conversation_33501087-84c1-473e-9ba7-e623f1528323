'use client';

import { Card, Divider, Tabs, Tag } from 'antd-mobile';
import clsx from 'clsx';
import { motion } from 'framer-motion';
import {
  Baby,
  BookOpen,
  Calendar,
  Camera,
  CheckSquare,
  Crown,
  Gift,
  GraduationCap,
  HandHeart,
  Heart,
  Medal,
  School,
  Star,
  Trophy,
  User,
  Users,
  UtensilsCrossed,
} from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function SchoolOpeningRules() {
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState('rules');
  const [type] = useState<string>(searchParams?.get('type') ?? '0'); // q,a,z

  useEffect(() => {
    const originalTitle = document.title;
    document.title = '活动规则详情';
    return () => {
      document.title = originalTitle;
    };
  }, []);

  const tabItems = [
    {
      key: 'rules',
      title: (
        <div className="flex items-center">
          <BookOpen className="mr-2 size-4" />
          活动规则
        </div>
      ),
    },
    {
      key: 'tasks',
      title: (
        <div className="flex items-center">
          <CheckSquare className="mr-2 size-4" />
          每日任务
        </div>
      ),
    },
    {
      key: 'rewards',
      title: (
        <div className="flex items-center">
          <Gift className="mr-2 size-4" />
          额外奖励
        </div>
      ),
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-gradient-to-r from-indigo-600 to-purple-600 py-8 text-white">
        <div className="container mx-auto px-4">
          <h1 className="mb-2 flex items-center justify-center font-bold text-2xl">
            <School className="mr-3 size-6" />
            园所活跃挑战
          </h1>
          <p className="mx-auto max-w-3xl text-base">
            完成每日任务，积累活跃度，30天内累计21天完成挑战即可赢得环创礼包大奖！
          </p>

          <div className="mt-6 flex flex-wrap justify-center gap-4">
            <Tag
              className="border-white/30 bg-white/20 px-4 py-2 text-sm text-white"
              color="transparent"
            >
              <Calendar className="mr-2 size-4" /> 30天挑战
            </Tag>
            <Tag
              className="border-white/30 bg-white/20 px-4 py-2 text-sm text-white"
              color="transparent"
            >
              <CheckSquare className="mr-2 size-4" /> 7项任务
            </Tag>
            <Tag
              className="border-white/30 bg-white/20 px-4 py-2 text-sm text-white"
              color="transparent"
            >
              <Trophy className="mr-2 size-4" /> 丰厚奖励
            </Tag>
          </div>
        </div>
      </header>

      <Tabs activeKey={activeTab} className="mb-8" onChange={setActiveTab}>
        {tabItems.map((item) => (
          <Tabs.Tab key={item.key} title={item.title}>
            {item.key === 'rules' && (
              <div className="space-y-6">
                <Card className="overflow-hidden border-0 shadow-lg">
                  <div className="p-4">
                    <div className="mb-4">
                      <div className="mb-2 flex items-center text-2xl text-indigo-700">
                        <School className="mr-3 size-6" />
                        园所活跃挑战规则
                      </div>
                      <div className="text-gray-600 text-lg">
                        园所完成每日任务挑战，积累活跃度
                      </div>
                    </div>

                    <div className="space-y-6">
                      <div className="rounded-lg bg-indigo-50 p-4">
                        <p className="text-lg">
                          活跃度到达一定标准则视为完成当日挑战。30天内累计21天完成挑战即可赢得环创礼包大奖！
                        </p>
                      </div>

                      <div className="space-y-4">
                        <h3 className="flex items-center font-semibold text-indigo-700 text-xl">
                          <School className="mr-2 size-5" />
                          园所活跃挑战达标要求：
                        </h3>

                        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                          {type === 'q' && (
                            <div className="rounded-lg border-indigo-400 border-l-4 bg-white p-4 shadow-md">
                              <h4 className="mb-2 flex items-center font-semibold">
                                <Users className="mr-2 text-indigo-400" />
                                园所要求
                              </h4>
                              <p>
                                全园每日活跃度指数达到100（不可累计、不可跨日）
                              </p>
                            </div>
                          )}
                          {type === 'a' && (
                            <div className="rounded-lg border-purple-400 border-l-4 bg-white p-4 shadow-md">
                              <h4 className="mb-2 flex items-center font-semibold">
                                <Users className="mr-2 text-purple-400" />
                                园所要求
                              </h4>
                              <p>
                                全园每日活跃度指数达到200（不可累计、不可跨日）
                              </p>
                            </div>
                          )}
                          {type === 'z' && (
                            <div className="rounded-lg border-pink-400 border-l-4 bg-white p-4 shadow-md">
                              <h4 className="mb-2 flex items-center font-semibold">
                                <Users className="mr-2 text-pink-400" />
                                园所要求
                              </h4>
                              <p>
                                全园每日活跃度指数达到300（不可累计、不可跨日）
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            )}

            {item.key === 'tasks' && (
              <div className="space-y-6">
                <Card className="overflow-hidden border-0 shadow-lg">
                  <div className="p-4">
                    <div className="mb-4">
                      <div className="mb-2 flex items-center text-2xl text-blue-700">
                        <CheckSquare className="mr-3 size-6" />
                        每日任务
                      </div>
                      <div className="text-gray-600 text-lg">
                        完成以下任务获取活跃度积分
                      </div>
                    </div>

                    <div className="space-y-6">
                      {[
                        {
                          id: 1,
                          title: '发布有效动态',
                          description:
                            '老师发布有效动态（图文内容完整+包含互动引导话术+家长有有效的互动）即可获得活跃度',
                          points: 3,
                          limit: '每日最多20条',
                          icon: <GraduationCap className="text-blue-500" />,
                          color: 'blue',
                          name: '班级动态',
                        },
                        {
                          id: 2,
                          title: '动态互动达标',
                          description:
                            '老师发布班级动态后，单条动态浏览量达到20次，同时点赞评论数达到10次视为任务完成，每日前20次完成累计积分，每次完成累计5分。',
                          points: 5,
                          limit: '每日最多20次',
                          icon: <HandHeart className="text-indigo-500" />,
                          color: 'indigo',
                          name: '班级动态',
                        },
                        {
                          id: 3,
                          title: '录入家长人脸',
                          description:
                            '园所内每录入成功一位家长人脸视为完成任务一次，录入人脸任务不限次数，每一位成功录入的人脸累计5积分。',
                          points: 5,
                          limit: '不限次数',
                          icon: <User className="text-purple-500" />,
                          color: 'purple',
                          name: '人脸入录',
                        },
                        {
                          id: 4,
                          title: '全员人脸录入',
                          description:
                            '园所内部全部学生，每位学生有2个及以上的已录入家长人脸，视为完成任务，该任务为一次性任务，单次完成累计100积分。',
                          points: 100,
                          limit: '一次性任务',
                          icon: <Camera className="text-pink-500" />,
                          color: 'pink',
                          name: '人脸入录',
                        },
                        {
                          id: 5,
                          title: '发布校园食谱',
                          description:
                            '老师成功发布校园食谱视为完成任务一次，每天可完成一次该任务，该任务累计20积分。',
                          points: 20,
                          limit: '每日1次',
                          icon: <UtensilsCrossed className="text-orange-500" />,
                          color: 'orange',
                          name: '校园食谱',
                        },
                        {
                          id: 6,
                          title: '发布亲子任务',
                          description:
                            '老师成功发布一项亲子任务视为完成任务一次，每日完成该任务不限次数，每次完成可累计10积分。',
                          points: 10,
                          limit: '不限次数',
                          icon: <Heart className="text-green-500" />,
                          color: 'green',
                          name: '亲子任务',
                        },
                        {
                          id: 7,
                          title: '亲子任务高完成率',
                          description:
                            '老师发布的亲子动态，完成率达到70%以上，视为完成该任务一次，每日不限次数，每次完成可累计60积分。',
                          points: 60,
                          limit: '不限次数',
                          icon: <Baby className="text-teal-500" />,
                          color: 'teal',
                          name: '亲子任务',
                        },
                      ].map((task) => (
                        <div className="flex-1" key={task.id}>
                          <div className="mt-3 flex items-center justify-between">
                            <h3 className="font-semibold text-lg">
                              {task.title}
                            </h3>
                            <Tag
                              className="font-semibold text-[16px]"
                              color="blue"
                            >
                              {task.name}
                            </Tag>
                          </div>
                          <p className="mt-1 text-gray-600">
                            {task.description}
                          </p>
                          <div className="mt-3 flex items-center justify-between">
                            <Tag color="default">{task.limit}</Tag>
                            <Tag
                              className="px-3 py-1 font-semibold text-sm"
                              color={task.color}
                            >
                              +{task.points} 积分/次
                            </Tag>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </Card>
              </div>
            )}

            {item.key === 'rewards' && (
              <div className="space-y-6">
                <Card className="overflow-hidden border-0 shadow-lg">
                  <div className="p-4">
                    <div className="mb-4">
                      <div className="mb-2 flex items-center text-2xl text-amber-700">
                        <Gift className="mr-3 size-6" />
                        老师奖励规则
                      </div>
                      <div className="text-gray-600 text-lg">
                        参与活动获得丰厚奖励
                      </div>
                    </div>

                    <div className="space-y-6">
                      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                        <h3 className="mb-3 flex items-center font-semibold text-amber-600 text-lg">
                          <Gift className="mr-2 size-5" />
                          启动激励
                        </h3>
                        <p>活动第一天发送动态即得3元红包</p>
                        <div className="mt-3 flex justify-end">
                          <Tag color="orange">
                            <Star className="mr-1 size-3" /> 简单获取
                          </Tag>
                        </div>

                        <h3 className="mb-3 flex items-center font-semibold text-indigo-600 text-lg">
                          <Gift className="mr-2 size-5" />
                          首周激励
                        </h3>
                        <p>
                          第一周，10条以上有效动态，且每条互动率都达到60%，奖励现金红包
                        </p>
                        <div className="mt-3 flex justify-end">
                          <Tag color="blue">
                            <Star className="mr-1 size-3" /> 互动奖励
                          </Tag>
                        </div>

                        <h3 className="mb-3 flex items-center font-semibold text-lg text-purple-600">
                          <Gift className="mr-2 size-5" />
                          14天激励
                        </h3>
                        <p>
                          活动开始后第14天将统计每位老师负责班级的活跃度贡献，对活跃度最高的十位老师，赠送现金奖励
                        </p>
                        <div className="mt-3 flex justify-end">
                          <Tag color="purple">
                            <Crown className="mr-1 size-3" /> 排名奖励
                          </Tag>
                        </div>

                        <h3 className="mb-3 flex items-center font-semibold text-lg text-pink-600">
                          <Gift className="mr-2 size-5" />
                          月度激励
                        </h3>
                        <p>
                          活动结束后，统计整个活动期间的有效动态数+整体的互动率。根据排名发放奖励
                        </p>
                        <ul className="mt-2 list-disc space-y-1 pl-5">
                          <li>一等奖（1-3）：100元</li>
                          <li>二等奖（4-10）：50元</li>
                        </ul>
                        <div className="mt-3 flex justify-end">
                          <Tag color="magenta">
                            <Medal className="mr-1 size-3" /> 顶级奖励
                          </Tag>
                        </div>
                      </div>

                      <Divider className="my-6" />

                      <h3 className="mb-4 flex items-center font-semibold text-blue-600 text-xl">
                        <Trophy className="mr-2 size-6" />
                        半月园所奖励
                      </h3>
                      <p className="mb-4">
                        在活动开始第14天时统计前半个月的园所活动表现，根据园所的活跃率和家长参与率进行排名，对排名靠前的园所赠送绘本套装奖励。
                      </p>

                      <Divider className="my-6" />

                      <h3 className="mb-3 flex items-center font-semibold text-xl">
                        <Crown className="mr-2 size-6" />
                        额外奖励规则
                      </h3>
                      <p className="text-lg">
                        21天以外每多一天完成挑战，多30元现金红包奖励，最多320元。
                      </p>
                    </div>
                  </div>
                </Card>
              </div>
            )}
          </Tabs.Tab>
        ))}
      </Tabs>

      {activeTab !== 'rewards' && (
        <Card className="mb-8 overflow-hidden border-0 shadow-lg">
          <div className="p-4">
            <div className="mb-4">
              <div className="mb-2 flex items-center text-2xl text-green-700">
                <Users className="mr-3 size-6" />
                任务指引
              </div>
              <div className="text-gray-600 text-lg">如何完成各项任务</div>
            </div>

            <div>
              <div className="rounded-lg bg-white shadow-md">
                <h3 className="mb-4 flex items-center font-semibold text-green-600 text-xl">
                  <GraduationCap className="mr-2 size-5" />
                  班级动态、校园食谱、亲子任务发布位置
                </h3>
                <p className="mb-4 text-lg">掌心智校/掌心宝贝园丁端APP首页</p>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <div className="overflow-hidden rounded-lg shadow-md">
                    <img
                      alt="App interface"
                      className="w-full object-cover"
                      src="https://unicorn-media.ancda.com/production/app/activity/taskRelease1.png"
                    />
                    <div className="bg-green-50 p-3 text-center text-green-700">
                      APP首页发布
                    </div>
                  </div>
                  <div className="overflow-hidden rounded-lg shadow-md">
                    <img
                      alt="App interface"
                      className="w-full object-cover"
                      src="https://unicorn-media.ancda.com/production/app/activity/taskRelease2.png"
                    />
                    <div className="bg-green-50 p-3 text-center text-green-700">
                      发布功能入口
                    </div>
                  </div>
                </div>
              </div>

              <Divider />

              <div className="rounded-lg bg-white p-6 shadow-md">
                <h3 className="mb-4 flex items-center font-semibold text-purple-600 text-xl">
                  <User className="mr-2 size-5" />
                  录入人脸位置
                </h3>
                <p className="mb-4 text-lg">
                  掌心APP/宝贝园丁端APP，底部选项栏点击工作台--下滑找到安全健康--点击人脸录入图标。
                </p>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                  <div className="overflow-hidden rounded-lg shadow-md">
                    <img
                      alt="App interface"
                      className="w-full object-cover"
                      src="https://unicorn-media.ancda.com/production/app/activity/faceTask.jpg"
                    />
                    <div className="bg-purple-50 p-3 text-center text-purple-700">
                      点击人脸录入
                    </div>
                  </div>
                </div>
              </div>

              <Divider />

              <div className="rounded-lg bg-white p-6">
                <h3 className="mb-4 flex items-center font-semibold text-blue-600 text-xl">
                  <HandHeart className="mr-2 size-5" />
                  家长参与互动方式
                </h3>
                <div className="rounded-lg bg-blue-50 p-4 text-lg">
                  老师发布班级动态、校园食谱、亲子任务后，家长即可再掌心智校/掌心宝贝家长端APP首页看到老师的动态，点击即可互动。
                </div>
              </div>

              <Divider />

              <div className="rounded-lg bg-white p-6 shadow-md">
                <h3 className="mb-4 flex items-center font-semibold text-amber-600 text-xl">
                  <User className="mr-2 size-5" />
                  家长录入人脸位置
                </h3>
                <p className="mb-4 text-lg">掌心智校/掌心宝贝家长端APP首页：</p>
                <div className="overflow-hidden rounded-lg shadow-md">
                  <img
                    alt="Parent app interface"
                    className="w-full object-cover"
                    src="https://unicorn-media.ancda.com/production/app/activity/parentFace.png"
                  />
                  <div className="bg-amber-50 p-3 text-center text-amber-700">
                    家长端APP首页
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      )}

      <footer className="bg-gradient-to-r from-indigo-600 to-purple-600 py-8 text-white">
        <div className="container mx-auto px-4 text-center">
          <p className="text-white/80">© 2025 掌心智校/掌心宝贝 </p>
        </div>
      </footer>
    </div>
  );
}
