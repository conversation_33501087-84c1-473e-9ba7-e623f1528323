'use client';

import { Bell, Info } from 'lucide-react';
import Head from 'next/head';

//获取领取绘本消息
export default function Home() {
  const handleAcceptInvitation = () => {
    const downloadUrl =
      'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/%E5%B9%BC%E5%84%BF%E5%85%A5%E5%9B%AD%E9%80%82%E5%BA%94%E7%BB%98%E6%9C%AC%E5%85%A8%E4%B8%83%E5%86%8C%EF%BC%88%E7%94%B5%E5%AD%90%E7%89%88%EF%BC%89.zip';

    // 方法1：创建隐藏的链接并触发点击（推荐用于移动端）
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = '幼儿入园适应绘本全七册（电子版）.zip';
    link.target = '_blank';
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 方法2：备用方案 - 使用 window.open
    setTimeout(() => {
      try {
        window.open(downloadUrl, '_blank');
      } catch (error) {
        console.error('下载失败:', error);
        // 方法3：最后备用方案
        window.location.href = downloadUrl;
      }
    }, 100);
  };
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <Head>
        <title>消息详情</title>
      </Head>

      {/* Main Content */}
      <main className="pt-1 pb-6">
        {/* Message Card */}
        <div className="mx-4 mt-4 overflow-hidden rounded-xl bg-white shadow-sm">
          {/* Message Header */}
          <div className="border-gray-100 border-b p-4">
            <div className="mb-2 flex items-center gap-3">
              <div className="rounded-lg bg-[#4E78FF1A] p-2">
                <Bell className="size-5 text-[#4E78FF]" />
              </div>
              <h2 className="font-semibold text-gray-900 text-lg">系统通知</h2>
            </div>

            {/* Message Meta  */}
            <div className="mt-3 flex flex-wrap gap-x-4 gap-y-2">
              <div className="flex items-center text-gray-500 text-sm">
                <Info className="mr-1 size-4" />
                <span>绘本领取通知</span>
              </div>
            </div>
          </div>

          {/* Message  Content */}
          <div className="relative p-4">
            <div className="whitespace-pre-line text-gray-700 leading-relaxed">
              尊敬的用户： <br />
              您好！ <br />
              感谢您信任并选择购买掌心会员，我们已经为您准备好开学季会员福利《幼儿入园适应绘本全七册（电子版）》，点击【领取】即可下载
              <br />
            </div>
            <button
              className="rounded-lg bg-[#4E78FF] px-4 py-1 text-center font-medium text-sm text-white transition-colors"
              onClick={handleAcceptInvitation}
              type="button"
            >
              领取
            </button>
          </div>
        </div>
      </main>
    </div>
  );
}
