'use client';

import { Bell, Info } from 'lucide-react';
import Head from 'next/head';
import { useEffect, useState } from 'react';
import {
  getSummerActivityPictureCardStatus,
  postSummerActivityPictureCardReceive,
} from '@/api/activity';

interface StatusData {
  canReceive: boolean;
  hasReceived: boolean;
  availableOrderList: Array<{ orderId: string }>;
}

//获取领取洪恩绘本月卡消息
export default function Home() {
  const [statusData, setStatusData] = useState<StatusData | null>(null);
  const [loading, setLoading] = useState(true);
  const [receiving, setReceiving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取状态数据
  const fetchStatus = async () => {
    try {
      setLoading(true);
      setError(null);
      const response: any = await getSummerActivityPictureCardStatus();
      setStatusData(response);
    } catch (err) {
      setError('获取状态失败，请重试');
      console.error('获取状态失败:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const originalTitle = document.title;
    document.title = '消息详情';

    // 页面加载时获取状态
    fetchStatus();

    return () => {
      document.title = originalTitle;
    };
  }, []);

  const handleAcceptInvitation = async () => {
    if (!statusData?.canReceive || receiving || statusData.hasReceived) {
      return;
    }

    const orderId = statusData.availableOrderList?.[0]?.orderId;
    if (!orderId) {
      setError('订单信息不完整');
      return;
    }

    try {
      setReceiving(true);
      setError(null);
      await postSummerActivityPictureCardReceive({ orderId });
      // 领取成功后重新获取状态
      await fetchStatus();
    } catch (err) {
      setError('领取失败，请重试');
      console.error('领取失败:', err);
    } finally {
      setReceiving(false);
    }
  };

  // 计算按钮状态
  const getButtonConfig = () => {
    if (loading) {
      return { text: '加载中...', disabled: true, className: 'bg-gray-400' };
    }

    if (statusData?.hasReceived) {
      return { text: '已领取', disabled: true, className: 'bg-gray-400' };
    }

    if (statusData?.canReceive) {
      return {
        text: receiving ? '领取中...' : '领取',
        disabled: receiving,
        className: receiving ? 'bg-gray-400' : 'bg-[#4E78FF]',
      };
    }

    return { text: '暂不可领取', disabled: true, className: 'bg-gray-400' };
  };

  const buttonConfig = getButtonConfig();
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <Head>
        <title>消息详情</title>
      </Head>

      {/* Main Content */}
      <main className="pt-1 pb-6">
        {/* Message Card */}
        <div className="mx-4 mt-4 overflow-hidden rounded-xl bg-white shadow-sm">
          {/* Message Header */}
          <div className="border-gray-100 border-b p-4">
            <div className="mb-2 flex items-center gap-3">
              <div className="rounded-lg bg-[#4E78FF1A] p-2">
                <Bell className="size-5 text-[#4E78FF]" />
              </div>
              <h2 className="font-semibold text-gray-900 text-lg">系统通知</h2>
            </div>

            {/* Message Meta  */}
            <div className="mt-3 flex flex-wrap gap-x-4 gap-y-2">
              <div className="flex items-center text-gray-500 text-sm">
                <Info className="mr-1 size-4" />
                <span>领取您的洪恩绘本月卡</span>
              </div>
            </div>
          </div>

          {/* Message Content */}
          <div className="relative p-4">
            <div className="whitespace-pre-line text-gray-700 leading-relaxed">
              尊敬的用户： <br />
              您好！
              <br />
              感谢您信任并选择购买掌心会员，请领取您的开学季会员福利【洪恩双语绘本-月卡】，点击【领取】即可激活权益。
              <br />
            </div>

            {/* 错误提示 */}
            {error && (
              <div className="mb-3 rounded-md bg-red-50 p-3 text-red-600 text-sm">
                {error}
              </div>
            )}

            <button
              className={`rounded-lg px-4 py-1 text-center font-medium text-sm text-white transition-colors ${buttonConfig.className}`}
              disabled={buttonConfig.disabled}
              onClick={handleAcceptInvitation}
              type="button"
            >
              {buttonConfig.text}
            </button>
          </div>
          <div className="border-gray-50 border-t p-4">
            温馨提示：领取后洪恩双语绘本月卡会员将会在24小时内激活至您的手机账户。如您尚未成为洪恩双语绘本APP用户，激活时将自动为您注册该APP账号，并将会员权益实时下放到您的手机账户中，用此账号登录洪恩双语绘本APP后，您将享受会员权益。
          </div>
        </div>
      </main>
    </div>
  );
}
