'use client';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { navigationToNativePage } from '@/utils';

export default function SchoolSeason() {
  const [images] = useState([
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason1.png',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason2.png',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason3.png',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason4.png',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason5.png',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason6_01.png',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason6_02.png',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason7.png',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason8.png',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason9.png',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason10.png',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason11.png',
  ]);

  // 设置页面标题
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '开学季 - 新学期新征程';
    }
  }, []);
  useEffect(() => {
    const preventZoom = (e: TouchEvent) => {
      if (e.touches.length > 1) {
        e.preventDefault();
      }
    };

    document.addEventListener('touchmove', preventZoom, { passive: false });

    return () => {
      document.removeEventListener('touchmove', preventZoom);
    };
  }, []);

  // 处理"去开通"按钮点击事件
  const handleGoToMember = () => {
    navigationToNativePage('rn://MemberStack?initialRoute=ParentsIndexScreen');
  };

  // 判断是否为需要添加点击事件的图片（第7、9、11张图片）
  const shouldAddClickable = (index: number) => {
    return [6, 8, 10].includes(index); // 对应第7、9、11张图片
  };

  return (
    <div className="w-full">
      {images.map((imageUrl, index) => (
        <div className="relative w-full" key={`school-season-${index + 1}`}>
          <Image
            alt={`schoolSeason${index + 1}`}
            className="block h-auto w-full"
            height={1334}
            loading={index < 2 ? 'eager' : 'lazy'}
            priority={index < 2}
            src={imageUrl}
            width={750}
          />
          {/* 为第7、9、11张图片添加"去开通"按钮热区 */}
          {shouldAddClickable(index) && (
            <button
              aria-label="去开通会员"
              className="absolute right-[8%] bottom-[0%] h-[50%] w-[25%] cursor-pointer bg-transparent"
              onClick={handleGoToMember}
              type="button"
            />
          )}
        </div>
      ))}
    </div>
  );
}
