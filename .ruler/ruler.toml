# Ruler Configuration File
# See https://ai.intellectronica.net/ruler for documentation.

# To specify which agents are active by default when --agents is not used,
# uncomment and populate the following line. If omitted, all agents are active.
# default_agents = ["copilot", "claude"]

# --- Agent Specific Configurations ---
# You can enable/disable agents and override their default output paths here.
# Use lowercase agent identifiers: copilot, claude, codex, cursor, windsurf, cline, aider, kilocode

# [agents.copilot]
# enabled = true
# output_path = ".github/copilot-instructions.md"

# [agents.claude]
# enabled = true
# output_path = "CLAUDE.md"

# [agents.codex]
# enabled = true
# output_path = "AGENTS.md"

# [agents.cursor]
# enabled = true
# output_path = ".cursor/rules/ruler_cursor_instructions.mdc"

# [agents.windsurf]
# enabled = true
# output_path = ".windsurf/rules/ruler_windsurf_instructions.md"

# [agents.cline]
# enabled = true
# output_path = ".clinerules"

# [agents.aider]
# enabled = true
# output_path_instructions = "ruler_aider_instructions.md"
# output_path_config = ".aider.conf.yml"

# [agents.firebase]
# enabled = true
# output_path = ".idx/airules.md"

# [agents.gemini-cli]
# enabled = true

# [agents.kilocode]
# enabled = true
# output_path = ".kilocode/rules/ruler_kilocode_instructions.md"
